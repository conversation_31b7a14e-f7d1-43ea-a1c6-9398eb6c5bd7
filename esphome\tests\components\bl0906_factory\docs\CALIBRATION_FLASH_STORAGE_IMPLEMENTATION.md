# BL0906校准数据Flash存储功能实施总结

## 概述

本次修改实现了BL0906芯片校准数据的Flash存储功能，支持将校准寄存器数值保存到ESP32的自定义Flash分区中，同时保持电量持久化数据仍使用NVS分区存储。

## 主要修改内容

### 1. 新增文件

- **partitions.csv** - ESP32分区表配置文件，添加了16KB的bl0906_cal分区
- **calibration_storage_base.h/cpp** - 基础校准存储类，提供只读功能（量产版和校准版共用）
- **calibration_storage.h/cpp** - 扩展校准存储类，添加写入功能（仅校准版使用）
- **test_calibration_mode.yaml** - 校准版本示例配置
- **test_production_mode.yaml** - 量产版本示例配置
- **CALIBRATION_FLASH_STORAGE_IMPLEMENTATION.md** - 本文档

### 2. 修改文件

#### bl0906_factory.h
- 添加了条件编译支持，根据BL0906_CALIBRATION_MODE宏区分版本
- 添加了校准存储类的包含和成员变量
- 添加了`save_all_calibration_to_flash()`方法（仅校准版）
- 添加了`load_calibration_from_flash()`方法（通用）

#### bl0906_factory.cpp
- 修改了`setup()`方法，集成校准存储初始化
- 实现了`load_calibration_from_flash()`方法
- 实现了`save_all_calibration_to_flash()`方法（仅校准版）
- 支持从YAML配置迁移校准值到Flash

#### __init__.py
- 添加了`calibration_mode`配置选项
- 添加了`initial_calibration`配置支持
- 根据配置生成相应的编译宏定义

### 3. 数据结构设计

```cpp
// 校准数据分区头部结构
struct CalibrationHeader {
    uint32_t magic;          // 魔数: 0xB0906CAL
    uint16_t version;        // 数据格式版本: 1
    uint16_t entry_count;    // 校准项数量
    uint32_t crc32;          // 数据区CRC校验
    uint32_t timestamp;      // 最后更新时间戳
    uint8_t reserved[16];    // 预留字段
};

// 单个校准项结构
struct CalibrationEntry {
    uint8_t register_addr;   // 寄存器地址
    uint8_t reserved;        // 保留字节，用于对齐
    int16_t value;           // 校准值
};
```

## 使用方法

### 1. 校准流程

1. **准备分区表**
   - 将`partitions.csv`文件放置在项目根目录
   - 确保分区表中包含bl0906_cal分区

2. **配置校准版固件**
   ```yaml
   bl0906_factory:
     id: bl0906_1
     calibration_mode: true  # 启用校准模式
     initial_calibration:    # 初始校准值（可选）
       - register: 0xA1
         value: 1000
   ```

3. **添加保存按钮**
   ```yaml
   button:
     - platform: template
       name: "保存校准数据"
       on_press:
         lambda: |-
           auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(bl0906_1));
           if (bl0906 != nullptr) {
             bl0906->save_all_calibration_to_flash();
           }
   ```

4. **执行校准**
   - 通过Number组件调整校准值
   - 点击保存按钮将校准数据写入Flash

### 2. 量产部署

1. **配置量产版固件**
   ```yaml
   bl0906_factory:
     id: bl0906_1
     calibration_mode: false  # 禁用校准模式（默认值）
   ```

2. **自动加载校准数据**
   - 量产版固件启动时自动从Flash读取校准数据
   - 无需Number组件和保存按钮

### 3. 编译配置

#### 校准版编译
```bash
# 使用校准版配置文件
esphome compile test_calibration_mode.yaml
```

#### 量产版编译
```bash
# 使用量产版配置文件
esphome compile test_production_mode.yaml
```

## 特性说明

### 1. 模块化设计
- 基础类提供只读功能，减少量产版代码体积
- 扩展类仅在校准模式下编译，包含写入功能
- 使用条件编译控制功能差异

### 2. 数据完整性
- CRC32校验确保数据完整性
- 魔数和版本号防止误读取
- 支持数据迁移和验证

### 3. 手动保存机制
- 避免频繁写入Flash，延长寿命
- 用户控制保存时机
- 支持批量保存所有校准值

### 4. 向后兼容
- 保留原有的YAML配置方式
- 首次运行时自动迁移YAML配置到Flash
- 量产版读取失败时有合理的错误处理

## 注意事项

1. **分区表配置**
   - 确保bl0906_cal分区地址不与其他分区冲突
   - 分区大小16KB足够存储128个校准项

2. **版本管理**
   - 校准版和量产版使用相同的数据格式
   - 校准版生成的数据可直接用于量产版

3. **Flash寿命**
   - 采用手动保存机制，减少写入次数
   - 建议校准完成后再保存

4. **错误处理**
   - 量产版加载失败会记录错误日志
   - 校准版支持从YAML配置恢复

## 未来扩展

1. 支持导出/导入校准数据
2. 添加校准数据版本管理
3. 支持增量更新
4. 开发图形化校准工具 