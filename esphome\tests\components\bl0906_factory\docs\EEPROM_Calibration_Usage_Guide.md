# BL0906 EEPROM校准存储使用指南

## 概述

BL0906Factory组件现在支持使用EEPROM存储校准数据，这是对原有Flash存储方案的重要改进。EEPROM存储方案具有配置简单、兼容性好、支持多实例等优势。

## 主要特性

### 1. 多实例支持
- 支持最多3个BL0906实例
- 每个实例有独立的实例ID
- 校准数据在EEPROM中独立存储

### 2. 存储结构
- **EEPROM大小**: 4KB (4096字节)
- **头部**: 32字节，包含魔数、版本、实例数量、CRC校验等
- **实例数据**: 每个实例156字节（4字节实例ID + 152字节校准数据）
- **校准条目**: 每个实例最多38个校准寄存器

### 3. 数据完整性
- CRC32校验确保数据完整性
- 魔数验证防止数据损坏
- 版本控制支持未来扩展

## 配置方法

### 1. 基本配置

```yaml
esphome:
  name: bl0906-eeprom-test
  platform: ESP32
  board: esp32dev
  platformio_options:
    build_flags:
      - -DBL0906_CALIBRATION_MODE=1
      - -DUSE_EEPROM_CALIBRATION=1

# 启用EEPROM组件
eeprom:
  size: 4096  # 4KB EEPROM大小

bl0906_factory:
  - id: bl0906_1
    uart_id: uart_bl0906_1
    instance_id: 0x906B0001  # 可选：手动指定实例ID
    calibration:
      enabled: true
      storage_type: eeprom  # 使用EEPROM存储
      auto_apply: true
```

### 2. 多实例配置

```yaml
bl0906_factory:
  # 第一个实例
  - id: bl0906_1
    uart_id: uart_bl0906_1
    instance_id: 0x906B0001
    calibration:
      storage_type: eeprom
    initial_calibration:
      - register: 0x6A  # CHGN_1
        value: 0x8000

  # 第二个实例
  - id: bl0906_2
    uart_id: uart_bl0906_2
    instance_id: 0x906B0002
    calibration:
      storage_type: eeprom
    initial_calibration:
      - register: 0x6A  # CHGN_1
        value: 0x7F00

  # 第三个实例
  - id: bl0906_3
    uart_id: uart_bl0906_3
    instance_id: 0x906B0003
    calibration:
      storage_type: eeprom
```

### 3. 向后兼容配置

```yaml
bl0906_factory:
  - id: bl0906_1
    calibration:
      use_eeprom: true  # 旧的配置方式，仍然支持
```

## 实例ID管理

### 1. 自动生成
如果不指定`instance_id`，系统会自动生成：
- 基于UART引脚号
- 加入时间戳作为随机因子
- 确保ID唯一性

### 2. 手动指定
推荐为每个实例手动指定唯一的实例ID：
```yaml
instance_id: 0x906B0001  # 第一个实例
instance_id: 0x906B0002  # 第二个实例
instance_id: 0x906B0003  # 第三个实例
```

### 3. ID格式
- 32位十六进制数
- 建议使用`0x906B`前缀
- 后4位用于区分不同实例

## 存储操作

### 1. 手动保存（推荐）
为了延长EEPROM使用寿命，校准数据只支持手动保存。通过按钮触发批量保存：
```yaml
button:
  - platform: template
    name: "Save Calibration"
    on_press:
      - lambda: |-
          id(bl0906_1).save_all_calibration_to_flash();
```

### 2. 数据加载
系统启动时自动从EEPROM加载校准数据。

### 3. 校准值修改
- 通过Number组件可以实时修改校准值
- 修改后的值会立即应用到BL0906芯片
- 但不会自动保存到EEPROM，需要手动保存

## 优势对比

### EEPROM存储 vs Flash存储

| 特性 | EEPROM存储 | Flash存储 |
|------|------------|-----------|
| 配置复杂度 | 简单 | 复杂（需要自定义分区表） |
| ESPHome兼容性 | 原生支持 | 需要特殊配置 |
| 多实例支持 | 原生支持 | 需要额外开发 |
| 存储空间 | 4KB（足够） | 4KB |
| 数据完整性 | CRC32校验 | CRC32校验 |
| 写入次数限制 | ~10万次 | ~10万次 |
| 保存方式 | 仅手动保存 | 支持自动保存 |
| EEPROM寿命保护 | 是 | 不适用 |

## 迁移指南

### 从Flash存储迁移到EEPROM存储

1. **备份现有校准数据**
   - 记录当前的校准值
   - 或导出校准数据

2. **修改配置文件**
   ```yaml
   # 添加编译标志
   esphome:
     platformio_options:
       build_flags:
         - -DUSE_EEPROM_CALIBRATION=1
   
   # 启用EEPROM
   eeprom:
     size: 4096
   
   # 修改存储类型
   bl0906_factory:
     calibration:
       storage_type: eeprom  # 从flash改为eeprom
   ```

3. **重新编译和上传**
   - 首次使用EEPROM时，会自动初始化
   - 如果有`initial_calibration`配置，会自动迁移

4. **验证数据**
   - 检查校准值是否正确加载
   - 测试保存功能

## 故障排除

### 1. EEPROM初始化失败
```
错误: EEPROM初始化失败
解决: 检查EEPROM组件配置，确保size设置为4096
```

### 2. 实例ID冲突
```
错误: 多个实例使用相同的实例ID
解决: 为每个实例设置不同的instance_id
```

### 3. 数据验证失败
```
错误: CRC校验失败
解决: EEPROM数据可能损坏，会自动重新初始化
```

### 4. 校准数据丢失
```
错误: 未找到校准数据
解决: 检查实例ID是否正确，或重新设置initial_calibration
```

## 最佳实践

### 1. 实例ID规划
- 使用有意义的ID命名
- 保持ID的一致性
- 记录每个实例的用途

### 2. 校准数据管理
- 定期备份重要的校准值
- 使用版本控制管理配置文件
- 测试校准数据的保存和恢复

### 3. 调试技巧
- 启用DEBUG日志查看详细信息
- 使用Web界面监控校准值变化
- 通过按钮测试保存功能

### 4. EEPROM寿命保护
- 只支持手动保存，避免频繁写入
- EEPROM写入次数限制约10万次
- 建议只在校准完成后手动保存
- 避免在循环或自动化脚本中频繁调用保存

### 5. 性能优化
- 避免频繁的校准值修改
- 合理设置update_interval
- 监控EEPROM写入次数

## 示例项目

完整的示例配置请参考：`test_eeprom_calibration.yaml`

该示例展示了：
- 3个BL0906实例的配置
- EEPROM存储的使用
- 多实例校准数据管理
- 传感器和Number组件配置
- 保存按钮的实现

## 技术细节

### EEPROM布局
```
偏移量    大小    描述
0x0000    32      头部（魔数、版本、实例数量、CRC等）
0x0020    156     实例1数据（ID + 38个校准条目）
0x00BC    156     实例2数据
0x0158    156     实例3数据
0x01F4    3628    剩余空间（未使用）
```

### 数据结构
```cpp
struct EEPROMCalibHeader {
    uint32_t magic;           // 0xB0906CAL
    uint16_t version;         // 版本号（2）
    uint8_t instance_count;   // 实例数量
    uint8_t reserved1;        // 保留
    uint32_t crc32;          // 数据区CRC32
    uint32_t timestamp;       // Unix时间戳
    uint8_t reserved[16];     // 保留
};

struct EEPROMInstanceData {
    uint32_t instance_id;                              // 实例标识
    EEPROMCalibEntry entries[38];                      // 38个校准条目
};
```

## 总结

EEPROM校准存储方案为BL0906Factory组件提供了更简单、更可靠的校准数据管理方式。通过支持多实例、数据完整性校验和简化的配置，这个方案特别适合需要管理多个BL0906设备的应用场景。 