# BL0906 Factory 条件编译使用指南

## 概述

BL0906 Factory 组件支持两种校准数据存储方式：
- **preference**: 使用 ESP32 内置 NVS 存储
- **eeprom**: 使用外部 I2C EEPROM 芯片

为了优化编译效率和减少二进制文件大小，组件采用了条件编译技术。当选择 preference 存储时，不会编译 I2C EEPROM 相关代码。

## 条件编译机制

### 编译宏定义

| 宏定义 | 触发条件 | 作用 |
|--------|----------|------|
| `USE_I2C_EEPROM_CALIBRATION` | `storage_type: eeprom` | 启用 I2C EEPROM 存储代码 |
| `BL0906_CALIBRATION_MODE` | `calibration_mode: true` | 启用校准模式功能 |

### 代码文件的条件编译

#### 1. I2C EEPROM 存储文件
```cpp
// i2c_eeprom_calibration_storage.h
#pragma once

#ifdef USE_I2C_EEPROM_CALIBRATION
// EEPROM 相关代码只在需要时编译
#include "calibration_storage_interface.h"
#include "esphome/components/i2c/i2c.h"
// ... EEPROM 类定义 ...
#endif  // USE_I2C_EEPROM_CALIBRATION
```

```cpp
// i2c_eeprom_calibration_storage.cpp
#ifdef USE_I2C_EEPROM_CALIBRATION
// EEPROM 实现代码只在需要时编译
#include "i2c_eeprom_calibration_storage.h"
// ... EEPROM 实现 ...
#endif  // USE_I2C_EEPROM_CALIBRATION
```

#### 2. 主组件文件
```cpp
// bl0906_factory.h
#include "preference_calibration_storage.h"  // 总是包含
#ifdef USE_I2C_EEPROM_CALIBRATION
  #include "i2c_eeprom_calibration_storage.h"  // 条件包含
  #include "esphome/components/i2c/i2c.h"
#endif

class BL0906Factory {
  // 基础存储配置
  std::string storage_type_{"preference"};
  
  // I2C 相关成员变量只在需要时定义
#ifdef USE_I2C_EEPROM_CALIBRATION
  EEPROMType eeprom_type_{EEPROMType::TYPE_24C02};
  i2c::I2CComponent *i2c_parent_{nullptr};
  uint8_t i2c_address_{0x50};
#endif
};
```

#### 3. 初始化函数
```cpp
// bl0906_factory.cpp
bool BL0906Factory::init_calibration_storage() {
    if (storage_type_ == "preference") {
        // preference 存储总是可用
        calibration_storage_.reset(new PreferenceCalibrationStorage());
    } 
#ifdef USE_I2C_EEPROM_CALIBRATION
    else if (storage_type_ == "eeprom") {
        // EEPROM 存储只在编译时可用
        calibration_storage_.reset(new I2CEEPROMCalibrationStorage(
            i2c_parent_, eeprom_type_, i2c_address_));
    }
#endif
    else {
        ESP_LOGE(TAG, "未知的存储类型: %s", storage_type_.c_str());
        return false;
    }
    // ... 其余初始化代码 ...
}
```

## 配置示例

### 1. Preference 存储配置（推荐用于开发）

```yaml
# 6-ch-monitor-preference.yaml
bl0906_factory:
  id: sensor_bl0906
  uart_id: uart_bus
  instance_id: 0x906B0001
  calibration_mode: true
  
  calibration:
    enabled: true
    storage_type: preference    # 使用内置 NVS 存储
    # 注意：不需要配置 eeprom_type 和 I2C 相关参数
```

**编译结果**:
- ✅ 编译 `preference_calibration_storage.cpp`
- ❌ 不编译 `i2c_eeprom_calibration_storage.cpp`
- ❌ 不包含 I2C 库依赖
- 🎯 **二进制文件更小，编译更快**

### 2. I2C EEPROM 存储配置（用于生产环境）

```yaml
# 6-ch-monitor-eeprom.yaml
# 必须配置 I2C 总线
i2c:
  sda: 8
  scl: 9
  scan: true

bl0906_factory:
  id: sensor_bl0906
  uart_id: uart_bus
  i2c_id: bus_a              # 指定 I2C 总线
  address: 0x50              # EEPROM 地址
  instance_id: 0x906B0001
  calibration_mode: true
  
  calibration:
    enabled: true
    storage_type: eeprom       # 使用外部 EEPROM
    eeprom_type: 24c04         # 选择 EEPROM 型号
```

**编译结果**:
- ✅ 编译 `preference_calibration_storage.cpp`
- ✅ 编译 `i2c_eeprom_calibration_storage.cpp`
- ✅ 包含 I2C 库依赖
- 🎯 **功能完整，支持外部存储**

## 编译优化效果

### 二进制大小对比

| 存储方式 | 编译的文件 | 大致增加的大小 | 适用场景 |
|----------|------------|----------------|----------|
| **preference** | 仅基础存储 | +0KB | 开发、测试、原型 |
| **eeprom** | 基础+I2C存储 | +8-12KB | 生产、多实例 |

### 编译时间对比

| 存储方式 | I2C 代码编译 | 编译时间节省 |
|----------|-------------|-------------|
| **preference** | 跳过 | ~10-15% |
| **eeprom** | 包含 | 基准时间 |

## 最佳实践

### 1. 开发阶段建议
```yaml
# 使用 preference 存储进行快速开发
calibration:
  storage_type: preference
```
**优势**:
- 编译快速
- 无需外部硬件
- 调试方便

### 2. 生产阶段建议
```yaml
# 使用 EEPROM 存储确保数据安全
calibration:
  storage_type: eeprom
  eeprom_type: 24c04  # 根据需求选择
```
**优势**:
- 数据独立存储
- 不占用 Flash 空间
- 支持多实例

### 3. 型号选择指南

| EEPROM 型号 | 容量 | 最大实例数 | 适用场景 |
|-------------|------|------------|----------|
| **24c02** | 256字节 | 1个实例 | 单设备应用 |
| **24c04** | 512字节 | 3个实例 | 小型多设备 |
| **24c08** | 1024字节 | 6个实例 | 中型系统 |
| **24c16** | 2048字节 | 12个实例 | 大型系统 |

## 故障排除

### 1. 编译错误：找不到 I2C 相关定义
**原因**: 选择了 `eeprom` 存储但未正确配置 I2C
**解决**: 确保配置文件包含 `i2c:` 部分

### 2. 运行时错误：未知存储类型
**原因**: 配置了 `eeprom` 但编译时未定义相关宏
**解决**: 检查 ESPHome 配置是否正确触发条件编译

### 3. 二进制文件过大
**原因**: 不必要地编译了 EEPROM 代码
**解决**: 改用 `preference` 存储方式

## 总结

条件编译机制让 BL0906 Factory 组件能够：

1. **🚀 优化性能**: 只编译需要的代码
2. **💾 节省空间**: 减少不必要的二进制大小
3. **⚡ 加速编译**: 跳过不需要的文件编译
4. **🔧 灵活配置**: 根据应用场景选择存储方式

通过合理选择存储方式，可以在开发效率和功能完整性之间找到最佳平衡点。 