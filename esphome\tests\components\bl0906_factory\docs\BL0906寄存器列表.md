# 4、 内部寄存器

**4.1** **电参量寄存器（只读）**

|地址|名称|位宽|默认值|描述|
|---|---|---|---|---|
|1|保留||||
|2|I[1]_WAVE|24|0x000000|电流 1 通道波形寄存器（正常电流 和快速电流可选）|
|3|I[2]_WAVE|24|0x000000|电流 2 通道波形寄存器（正常电流 和快速电流可选）|
|4|I[3]_WAVE|24|0x000000|电流 3 通道波形寄存器（正常电流 和快速电流可选）|
|5|I[4]_WAVE|24|0x000000|电流 4 通道波形寄存器（正常电流 和快速电流可选）|
|6|保留||||
|7|保留||||
|8|I[5]_WAVE|24|0x000000|电流 5 通道波形寄存器（正常电流 和快速电流可选）|
|9|I[6]_WAVE|24|0x000000|电流 6 通道波形寄存器（正常电流 和快速电流可选）|
|A|保留||||
|B|V_WAVE|24|0x000000|电压通道波形寄存器|
|C|保留||||
|D|I[1]_RMS|24|0x000000|电流 1 通道有效值寄存器|
|E|I[2]_RMS|24|0x000000|电流 2 通道有效值寄存器|
|F|I[3]_RMS|24|0x000000|电流 3 通道有效值寄存器|
|10|I[4]_RMS|24|0x000000|电流 4 通道有效值寄存器|
|11|保留||||
|12|保留||||
|13|I[5]_RMS|24|0x000000|电流 5 通道有效值寄存器|
|14|I[6]_RMS|24|0x000000|电流 6 通道有效值寄存器|
|15|保留||||
|16|V_RMS|24|0x000000|电压通道有效值寄存器|
|17|保留||||
|18|I[1]_FAST_RMS|24|0x000000|电流 1 通道快速有效值寄存器|
|19|I[2]_FAST_RMS|24|0x000000|电流 2 通道快速有效值寄存器|
|1A|I[3]_FAST_RMS|24|0x000000|电流 3 通道快速有效值寄存器|
|1B|I[4]_FAST_RMS|24|0x000000|电流 4 通道快速有效值寄存器|
|1C|保留||||
|1D|保留||||
|1E|I[5]_FAST_RMS|24|0x000000|电流 5 通道快速有效值寄存器|
|1F|I[6]_FAST_RMS|24|0x000000|电流 6 通道快速有效值寄存器|
|20|保留||||
|21|V_FAST_RMS|24|0x000000|电压通道快速有效值寄存器|
|22|保留||||
|23|WATT[1]|24|0x000000|通道 1 有功功率寄存器|
|24|WATT[2]|24|0x000000|通道 2 有功功率寄存器|
|25|WATT[3]|24|0x000000|通道 3 有功功率寄存器|
|26|WATT[4]|24|0x000000|通道 4 有功功率寄存器|
|27|保留||||
|28|保留||||
|29|WATT[5]|24|0x000000|通道 5 有功功率寄存器|
|2A|WATT[6]|24|0x000000|通道 6 有功功率寄存器|
|2B|保留||||
|2C|WATT|24|0x000000|总有功功率寄存器|
|2D|VAR|24|0x000000|可选通道无功功率寄存器（基波）|
|2E|VA|24|0x000000|可选通道视在功率寄存器|
|2F|保留||||
|30|CF[1]_CNT|24|0x000000|通道 1 有功脉冲计数|
|31|CF[2]_CNT|24|0x000000|通道 2 有功脉冲计数|
|32|CF[3]_CNT|24|0x000000|通道 3 有功脉冲计数|
|33|CF[4]_CNT|24|0x000000|通道 4 有功脉冲计数|
|34|保留||||
|35|保留||||
|36|CF[5]_CNT|24|0x000000|通道 5 有功脉冲计数|
|37|CF[6]_CNT|24|0x000000|通道 6 有功脉冲计数|
|38|保留||||
|39|CF_CNT|24|0x000000|总有功脉冲计数|
|3A|CFQ_CNT|24|0x000000|可选通道无功脉冲计数|
|3B|CFS_CNT|24|0x000000|可选通道视在脉冲计数|
|3C|保留||||
|3D|ANGLE[1]|16|0x000000|通道 1 电流电压波形夹角寄存器|
|3E|ANGLE[2]|16|0x000000|通道 2 电流电压波形夹角寄存器|
|3F|ANGLE[3]|16|0x000000|通道 3 电流电压波形夹角寄存器|
|40|ANGLE[4]|16|0x000000|通道 4 电流电压波形夹角寄存器|
|41|保留||||
|42|保留||||
|43|ANGLE[5]|16|0x000000|通道 5 电流电压波形夹角寄存器|
|44|ANGLE[6]|16|0x000000|通道 6 电流电压波形夹角寄存器|
|45|保留||||
|46|保留||||
|47|I[1]_FAST_RMS_HOLD|24|0x000000|电流 1 通道快速有效值寄存器，无 符号，保持|
|48|I[2]_FAST_RMS_HOLD|24|0x000000|电流 2 通道快速有效值寄存器，无 符号，保持|
|49|I[3]_FAST_RMS_HOLD|24|0x000000|电流 3 通道快速有效值寄存器，无 符号，保持|
|4A|PF|24|0x000000|可选通道功率因子寄存器|
|4B|LINE_ WATTHR|24|0|线周期累计有功能量寄存器|
|4C|LINE_ VARHR|24|0|线周期累计无功能量寄存器|
|4D|SIGN|24|0x000000|功率符号位。对应当前电能脉冲计 数的符号位，在出 CF 脉冲时刷新|
|4E|PERIOD|20|0x000000|线电压频率周期寄存器（可选通道）|
|4F|保留||||
|50|保留||||
|51|保留||||
|52|保留||||
|53|保留||||
|54|STATUS1|24|0x000000|中断状态寄存器 1|
|55|保留||||
|56|STATUS3|10|0x000|M 状态寄存器|
|57|I[4]_FAST_RMS_HOLD|24|0x000000|电流 4 通道快速有效值寄存器，无 符号，保持|
|58|保留||||
|59|保留||||
|5A|I[5]_FAST_RMS_HOLD|24|0x000000|电流 5 通道快速有效值寄存器，无 符号，保持|
|5B|I[6]_FAST_RMS_HOLD|24|0x000000|电流 6 通道快速有效值寄存器，无 符号，保持|
|5C|保留||||
|5D|VAR|24|0x000000|可选通道（全波）无功功率寄存器|
|5E|TPS|10|0x000000|内部温度值寄存器|
|5F|保留||||

**4.2** **校表寄存器（外部写）**

|地 址|名称|位 宽|默认值|描述|
|---|---|---|---|---|
|60|GAIN1|24|0x000000|通道 PGA 增益调整寄存器<br><br>[3:0]：电压通道<br><br>[11:8]：电流 1 通道 [15:12]：电流 2 通道 [19:16]：电流 3 通道 [23:20]：电流 4 通道|
|61|GAIN2|20|0x00000|通道 PGA 增益调整寄存器 [11:8]：电流 5 通道<br><br>[15:12]：电流 6 通道|
|62|保留||||
|63|保留||||
|64|NA/<br><br>PHASE[1]|16|0x1010|[15:8]：保留<br><br>[7:0]：电流 1 通道|
|65|PHASE[2]/ PHASE[3]|16|0x1010|[15:8]：电流 2 通道 [7:0]：电流 3 通道|
|66|PHASE[4]/|16|0x1010|[15:8]：电流 4 通道|
||NA|||[7:0]：保留|
|67|NA/<br><br>PHASE[5]|16|0x1010|[15:8]：保留<br><br>[7:0]：电流 5 通道|
|68|PHASE[6]/ NA|16|0x1010|[15:8]：电流 6 通道 [7:0]：保留|
|69|PHASE[V]|8|0x10|电压通道|
|6A|VAR_PHCAL_I|5|0000H|电流通道无功相位校正|
|6B|VAR_PHCAL_V|5|0000H|电压通道无功相位校正|
|6C|保留||||
|6D|RMSGN[1]|16|0x0000|电流 1 通道有效值增益调整寄存器|
|6E|RMSGN[2]|16|0x0000|电流 2 通道有效值增益调整寄存器|
|6F|RMSGN[3]|16|0x0000|电流 3 通道有效值增益调整寄存器|
|70|RMSGN[4]|16|0x0000|电流 4 通道有效值增益调整寄存器|
|71|保留||||
|72|保留||||
|73|RMSGN[5]|16|0x0000|电流 5 通道有效值增益调整寄存器|
|74|RMSGN[6]|16|0x0000|电流 6 通道有效值增益调整寄存器|
|75|保留||||
|76|RMSGN[V]|16|0x0000|电压通道有效值增益调整寄存器|
|77|保留||||
|78|RMSOS[1]|24|0x000000|电流 1 通道有效值偏置校正寄存器|
|79|RMSOS[2]|24|0x000000|电流 2 通道有效值偏置校正寄存器|
|7A|RMSOS[3]|24|0x000000|电流 3 通道有效值偏置校正寄存器|
|7B|RMSOS[4]|24|0x000000|电流 4 通道有效值偏置校正寄存器|
|7C|保留||||
|7D|保留||||
|7E|RMSOS[5]|24|0x000000|电流 5 通道有效值偏置校正寄存器|
|7F|RMSOS[6]|24|0x000000|电流 6 通道有效值偏置校正寄存器|
|80|保留||||
|81|RMSOS[V]|24|0x000000|电压通道有效值偏置校正寄存器|
|82|保留/<br><br>WA_LOS[1]|24|0x000000|[23:12]：保留<br><br>[11:0]：通道 1 有功功率小信号补偿寄存器|
|83|WA_LOS[2]/|24|0x000000|[23:12]：通道 2 有功功率小信号补偿寄存|
||WA_LOS[3]|||器<br><br>[11:0]：通道 3 有功功率小信号补偿寄存器|
|84|WA_LOS[4]/ 保留|24|0x000000|[23:12]：通道 4 有功功率小信号补偿寄存 器<br><br>[11:0]：保留|
|85|保留/<br><br>WA_LOS[5]|24|0x000000|[23:12]：保留<br><br>[11:0]：通道 5 有功功率小信号补偿寄存器|
|86|WA_LOS[6]/ 保留|24|0x000000|[23:12]：通道 6 有功功率小信号补偿寄存 器<br><br>[11:0]：保留|
|87|VAR_LOS/ FVAR_LOS|24|0x000|[11:0]对应无功（基波）小信号补偿寄存 器，补码。[23:12]对应无功小信号补偿寄 存器，补码。|
|88|VAR_CREEP/ WA_CREEP|24|0x04C04C|[11:0]为有功防潜动功率阈值寄存器<br><br>WA_CREEP；[23:12] 为无功防潜动功率阈 值寄存器 VAR_CREEP。|
|89|WA_CREEP2|12|0x000|[11:0]总有功防潜动阈值寄存器|
|8A|RMS_CREEP|12|0x200|有效值小信号阈值寄存器|
|8B|FAST_RMS_CTRL|24|0x20FFFF|[23:21]通道快速有效值寄存器刷新时间，<br><br>可选半周波和 N 周波，默认是周波；[20:0] 通道快速有效值阈值寄存器|
|8C|I_PKLVL/ V_PKLVL|24|0xFFFFFF|[23:12]电流峰值门限寄存器 I_PKLVL； [11:0]电压峰值门限寄存器 V_PKLVL|
|8D|保留||||
|8E|SAGCYC/ ZXTOUT|24|0x04FFFF|[23:16]跌落线周期寄存器 SAGCYC，缺省 04H；[15:0]过零超时寄存器 ZXTOUT，如 果在此寄存器表示的时间内没有过零信<br><br>号，将产生过零超时中断，缺省 FFFFH。|
|8F|SAGLVL/ LINECYC|24|0x100009|[23:12]跌落电压阈值寄存器 SAGLVL，电 压通道输入连续低于此寄存器值的时间<br><br>超过 SAGCYC 中的时间，将产生线电压跌 落中断，缺省为 100H，约 1/16 满幅度电 压输入；[11:0] 线能量累加周期数寄存器 LINECYC，缺省 009H，代表 10 个周期。|
|90|flag_ctrl|24|0x000000|主控直接控制 M1~M6 输出的电平状态|
|91|flag_ctrl1|24|0x000000|过流指示控制寄存器 1。<br><br>[23:10]断开延时计时，0.1ms/lsb；<br><br>[9:0]指示控制，M1-M6：0-输出实时中断； 1-输出延时控制<br><br>[1]：电流 1 通道；[2]：电流 2 通道；<br><br>[3]：电流 3 通道；[4]：电流 4 通道；<br><br>[7]：电流 5 通道；[8]：电流 6 通道|
|92|flag_ctrl2|24|0x000000|过流指示控制寄存器 2。<br><br>[23:10]闭合延时计时，0.1ms/lsb；<br><br>[9:0]闭合控制，M1-M6：0-闭合，1-断开<br><br>[1]：电流 1 通道；[2]：电流 2 通道；<br><br>[3]：电流 3 通道；[4]：电流 4 通道；<br><br>[7]：电流 5 通道；[8]：电流 6 通道|
|93|ADC_PD|11|0x000|7 个通道 ADC 的使能控制：<br><br>[0]-电压通道；<br><br>[2]-电流 1 通道；[3]-电流 2 通道；[4]-电流<br><br>3 通道；[5]-电流 4 通道；[8]-电流 5 通道；<br><br>[9]-电流 6 通道|
|94|保留||||
|95|保留||||
|96|MODE1|24|0x000000|用户模式选择寄存器 1|
|97|MODE2|24|0x000000|用户模式选择寄存器 2|
|98|MODE3|24|0x000000|用户模式选择寄存器 3|
|99|保留||||
|9A|MASK1|24|0x000000|中断屏蔽寄存器，控制一个中断是否产生 一个有效的 IRQ 输出，详见“中断屏蔽寄 存器”说明|
|9B|保留||||
|9C|保留||||
|9D|RST_ENG|24|0x000000|能量清零设置寄存器，详见“能量清零设 置寄存器”说明|
|9E|USR_WRPROT|16|0x00|用户写保护设置寄存器，写入 5555H 时， 表示可操作用户寄存器 对 reg60 到reg9d，|
|||||rega0 到 d0|
|9F|SOFT_RESET|24|0x000000|当输入为 5A5A5A 时，系统复位——只复 位数字部分的状态机和寄存器！<br><br>当输入为 55AA55 时，用户读写寄存器复 位——Reset：reg60 到 reg9f，rega0 到 regd0|

**4.3** **OTP寄存器**

| 地址  | 名称           | 位宽  | 默认值    | 描述                                            |
| --- | ------------ | --- | ------ | --------------------------------------------- |
| A0  | 保留           |     |        |                                               |
| A1  | CHGN[1]      | 16  | 0x0000 | 电流 1 通道增益调整寄存器，补码                             |
| A2  | CHGN[2]      | 16  | 0x0000 | 电流 2 通道增益调整寄存器，补码                             |
| A3  | CHGN[3]      | 16  | 0x0000 | 电流 3 通道增益调整寄存器，补码                             |
| A4  | CHGN[4]      | 16  | 0x0000 | 电流 4 通道增益调整寄存器，补码                             |
| A5  | 保留           |     |        |                                               |
| A6  | 保留           |     |        |                                               |
| A7  | CHGN[5]      | 16  | 0x0000 | 电流 5 通道增益调整寄存器，补码                             |
| A8  | CHGN[6]      | 16  | 0x0000 | 电流 6 通道增益调整寄存器，补码                             |
| A9  | 保留           |     |        |                                               |
| AA  | CHGN[V]      | 16  | 0x0000 | 电压通道增益调整寄存器，补码                                |
| AB  | 保留           |     |        |                                               |
| AC  | CHOS[1]      | 16  | 0x0000 | 电流 1 通道通道偏置调整寄存器，补码                           |
| AD  | CHOS[2]      | 16  | 0x0000 | 电流 2 通道通道偏置调整寄存器，补码                           |
| AE  | CHOS[3]      | 16  | 0x0000 | 电流 3 通道通道偏置调整寄存器，补码                           |
| AF  | CHOS[4]      | 16  | 0x0000 | 电流 4 通道通道偏置调整寄存器，补码                           |
| B0  | 保留           |     |        |                                               |
| B1  | 保留           |     |        |                                               |
| B2  | CHOS[5]      | 16  | 0x0000 | 电流 5 通道通道偏置调整寄存器，补码                           |
| B3  | CHOS[6]      | 16  | 0x0000 | 电流 6 通道通道偏置调整寄存器，补码                           |
| B4  | 保留           |     |        |                                               |
| B5  | CHOS[V]      | 16  | 0x0000 | 电压通道偏置调整寄存器，补码                                |
| B6  | 保留           |     |        |                                               |
| B7  | WATTGN[1]    | 16  | 0x0000 | 通道 1 有功功率增益调整寄存器，补码                           |
| B8  | WATTGN[2]    | 16  | 0x0000 | 通道 2 有功功率增益调整寄存器，补码                           |
| B9  | WATTGN[3]    | 16  | 0x0000 | 通道 3 有功功率增益调整寄存器，补码                           |
| BA  | WATTGN[4]    | 16  | 0x0000 | 通道 4 有功功率增益调整寄存器，补码                           |
| BB  | 保留           |     |        |                                               |
| BC  | 保留           |     |        |                                               |
| BD  | WATTGN[5]    | 16  | 0x0000 | 通道 5 有功功率增益调整寄存器，补码                           |
| BE  | WATTGN[6]    | 16  | 0x0000 | 通道 6 有功功率增益调整寄存器，补码                           |
| BF  | 保留           |     |        |                                               |
| C0  | 保留           |     |        |                                               |
| C1  | WATTOS[1]    | 16  | 0x0000 | 通道 1 有功功率偏置调整寄存器，补码                           |
| C2  | WATTOS[2]    | 16  | 0x0000 | 通道 2 有功功率偏置调整寄存器，补码                           |
| C3  | WATTOS[3]    | 16  | 0x0000 | 通道 3 有功功率偏置调整寄存器，补码                           |
| C4  | WATTOS[4]    | 16  | 0x0000 | 通道 4 有功功率偏置调整寄存器，补码                           |
| C5  | 保留           |     |        |                                               |
| C6  | 保留           |     |        |                                               |
| C7  | WATTOS[5]    | 16  | 0x0000 | 通道 5 有功功率偏置调整寄存器，补码                           |
| C8  | WATTOS[9]    | 16  | 0x0000 | 对应通道有功功率偏置调整寄存器，补码                            |
| C9  | 保留           |     |        |                                               |
| CA  | VARGN        | 16  | 0x0000 | 对应通道无功功率增益调整寄存器，补码                            |
| CB  | VAROS        | 16  | 0x0000 | 对应通道无功功率偏置调整寄存器，补码                            |
| CC  | VAGN         | 16  | 0x0000 | 对应通道视在功率增益调整寄存器，补码                            |
| CD  | VAOS         | 16  | 0x0000 | 对应通道视在功率偏置调整寄存器，补码                            |
| CE  | CFDIV        | 12  | 0x010  | CF 缩放比例寄存器                                    |
| CF  | 保留           |     |        |                                               |
| D0  | OTP checksum | 16  | 0x00   | OTP 寄存器校验和，checksum 有问题恢 复到 0 对 rega0 到 regd0 |

**4.4** **模式寄存器**

_4.4.1_ _模式寄存器1（MODE1）_

|   |   |   |   |
|---|---|---|---|
|0x96|MODE1|工作模式寄存器|   |
|No.|name|default value|description|
|[10:0]|WAVE_SEL|11{1'b0}|WATT 全波波形选择，0-交流全波，1-直流|

|   |   |   |   |
|---|---|---|---|
||||[1]：电流 1 通道；[2]：电流 2 通道；<br><br>[3]：电流 3 通道；[4]：电流 4 通道；<br><br>[7]：电流 5 通道；[8]：电流 6 通道；<br><br>[10]：电压通道|
|[21:11]|保留|||
|[22]|L_F_SEL|1'b0|快速有效值选择通过高通，默认为0-没有高 通， 1-选择高通|
|[23]|WAVE_REG_SEL|1'b0|电流 WAVE 波形寄存器输出选择，默认 0<br><br>选择正常电流通道的波形，为 1 选择快速有<br><br>效值的波形输出|

_4.4.2_ _模式寄存器2（MODE2）_

|   |   |   |   |
|---|---|---|---|
|0x97|MODE2|工作模式寄存器|   |
|No.|name|default value|description|
|[21:0]|WAVE_RMS_SEL|11{2'b00}|有效值波形选择：<br><br>00-交流全波，10-直流，01-基波，11-全波 [3:2]：电流 1 通道；[5:4]：电流 2 通道；<br><br>[7:6]：电流 3 通道；[9:8]：电流 4 通道；<br><br>[15:14]：电流 5 通道；[17:16]：电流 6 通道； [21:20]：电压通道|
|[22]|RMS_UPDATE_SEL|1'b0|有效值存器更新速度选择，1-1000ms， 0-500ms，默认选择 500ms|
|[23]|AC_FREQ_SEL|1'b0|交流电频率选择：<br><br>1-60Hz；0-50Hz，默认选择 50Hz|

_4.4.3模式寄存器3（MODE3）_

|   |   |   |   |
|---|---|---|---|
|0x98|MODE3|工作模式寄存器|   |
|No.|name|default value|description|
|[3:0]|VAR I SEL|4'b000|选择无功电流计量通道，6 选 1，默认 0000 0001-通道 1；0010-通道 2；0011-通道 3；<br><br>0100-通道 4；0111-通道 5；1000-通道 6。|
|[8]|add_sel|1'b0|watt 合相总和加方式：0-绝对值加；1-代数和加。|

|   |   |   |   |
|---|---|---|---|
|[9]|cf_enable|1'b0|0-cf disable，默认；1-cf enable|
|[13:10]|CF_SEL|4'b0000|通道 CF_WATT 输出选择：<br><br>0000、1110、1111-默认关闭 CF；<br><br>0010-通道 1 的功率 CF；0011-通道 2 的功率 CF； 0100-通道 3 的功率 CF；0101-通道 4 的功率 CF； 1000-通道 5 的功率 CF；1001-通道 6 的功率 CF；<br><br>1011-总有功功率 CF；<br><br>1100-无功功率 CF（通道可选）； 1101-视在功率 CF（通道可选）；<br><br>另，CF_VAR 一直为无功功率 CF（通道可选） 不变|
|[14]|hpf_sel|1'b0|hpf 选择：0-使用 hpf；1-不用 hpf|
|[15]|cf_add_sel|1'b0|watt 和var 能量加方式：<br><br>0-绝对值加；1-代数和加（分相和合相）|
|[16]|var_sel|1'b0|var 能量选择：0-基波；1-全波|

**4.5** **中断状态寄存器**

_4.5.1_ _STATUS1寄存器_

|   |   |   |   |
|---|---|---|---|
|reg54|STATUS1|||
|位置|中断标志|默认值|描述|
|0|sag|0|线电压跌落|
|1|zxto|0|过零超限|
|2|保留|||
|3|zx01|0|电流 1 通道过零信号|
|4|zx02|0|电流 2 通道过零信号|
|5|zx03|0|电流 3 通道过零信号|
|6|zx04|0|电流 4 通道过零信号|
|7|保留|||
|8|保留|||
|9|zx05|0|电流 5 通道过零信号|
|10|zx06|0|电流 6 通道过零信号|
|11|保留|||
|12|zx|0|电压通道过零信号|
|13|保留|||
|14|pk01|0|电流 1 通道峰值超限信号|
|15|pk02|0|电流 2 通道峰值超限信号|
|16|pk03|0|电流 3 通道峰值超限信号|
|17|pk04|0|电流 4 通道峰值超限信号|
|18|保留|||
|19|保留|||
|20|pk05|0|电流 5 通道峰值超限信号|
|21|pk06|0|电流 6 通道峰值超限信号|
|22|保留|||
|23|pkv||电压通道峰值超限信号|

_4.5.2_ _STATUS3寄存器_

|reg56|STATUS3|||

|位置|中断标志|默认值|描述|
|---|---|---|---|
|0|保留|||
|1|flag01|0|电流 1 通道标志|
|2|flag02|0|电流 2 通道标志|
|3|flag03|0|电流 3 通道标志|
|4|flag04|0|电流 4 通道标志|
|5|保留|||
|6|保留|||
|7|flag05|0|电流 5 通道标志|
|8|flag06|0|电流 6 通道标志|
|9|保留|||
