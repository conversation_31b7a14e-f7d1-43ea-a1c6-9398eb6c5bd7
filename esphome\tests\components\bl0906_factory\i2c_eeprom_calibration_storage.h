#pragma once

#include "calibration_storage_interface.h"
#include "esphome/components/i2c/i2c.h"
#include "esphome/core/log.h"

namespace esphome {
namespace bl0906_factory {

// 支持的EEPROM型号枚举
enum class EEPROMType : uint8_t {
    TYPE_24C02 = 0x02,  // 256字节
    TYPE_24C04 = 0x04,  // 512字节
    TYPE_24C08 = 0x08,  // 1024字节
    TYPE_24C16 = 0x16   // 2048字节
};

// EEPROM头部结构（20字节）
struct EEPROMHeader {
    uint32_t magic;           // 0x24CXCAL, X为型号标识
    uint16_t version;         // 版本3
    uint8_t eeprom_type;      // 型号标识: 02/04/08/16
    uint8_t max_instances;    // 最大实例数
    uint8_t instance_count;   // 当前实例数
    uint16_t header_crc;      // 头部CRC
    uint32_t data_crc;        // 数据区CRC
    uint32_t timestamp;       // 最后更新时间戳
    uint8_t reserved;         // 保留
} __attribute__((packed));

class I2CEEPROMCalibrationStorage : public CalibrationStorageInterface {
public:
    I2CEEPROMCalibrationStorage(i2c::I2CBus *i2c, EEPROMType type, uint8_t address = 0x50);
    
    bool init() override;
    bool read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) override;
    bool write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) override;
    bool delete_instance(uint32_t instance_id) override;
    bool verify() override;
    bool erase() override;
    
    std::vector<uint32_t> get_instance_list() override;
    size_t get_max_instances() override;

private:
    i2c::I2CBus *i2c_;
    uint8_t address_;
    EEPROMType eeprom_type_;
    size_t eeprom_size_;
    size_t max_instances_;
    size_t entries_per_instance_;
    
    // I2C操作
    bool read_bytes(uint16_t addr, uint8_t *data, size_t len);
    bool write_bytes(uint16_t addr, const uint8_t *data, size_t len);
    bool write_page(uint16_t addr, const uint8_t *data, size_t len);
    
    // 数据验证
    uint16_t calculate_crc(const uint8_t* data, size_t len);
    uint32_t get_magic_for_type(EEPROMType type);
    
    // 布局计算
    void calculate_layout();
    uint16_t get_instance_offset(int instance_index);
    int find_instance_index(uint32_t instance_id);
    
    // 头部操作
    bool read_header(EEPROMHeader& header);
    bool write_header(const EEPROMHeader& header);
    
    static const char *const TAG;
};

}  // namespace bl0906_factory
}  // namespace esphome 