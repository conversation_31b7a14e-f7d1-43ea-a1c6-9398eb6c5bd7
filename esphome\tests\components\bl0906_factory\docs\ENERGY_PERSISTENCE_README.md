# BL0906电量数据持久化存储功能

## 功能概述

本功能为BL0906工厂校准组件添加了电量数据持久化存储能力，可以在设备重启后保持累计电量数据，实现真正的电量统计功能。

## 主要特性

### 1. 数据持久化存储
- 使用ESPHome的preferences组件将电量数据保存到flash存储
- 支持各通道独立的累计电量统计
- 支持总累计电量统计
- 数据完整性校验，防止数据损坏

### 2. 智能电量累计
- 基于BL0906的脉冲计数寄存器(CF_CNT)进行电量累计
- 自动处理脉冲计数溢出和重置情况
- 实时计算电量增量并累加到总电量

### 3. 灵活的配置选项
- 可启用/禁用电量持久化功能
- 可配置数据保存间隔
- 支持手动重置累计电量数据

### 4. 完整的传感器支持
- 各通道累计电量传感器
- 总累计电量传感器
- 与Home Assistant完美集成

## 技术实现

### 数据结构
```cpp
struct EnergyPersistenceData {
  float total_energy[6];        // 各通道累计电量 (kWh)
  float total_energy_sum;       // 总累计电量 (kWh)
  uint32_t last_cf_count[6];    // 上次读取的脉冲计数
  uint32_t last_cf_count_sum;   // 上次读取的总脉冲计数
  uint32_t save_timestamp;      // 保存时间戳
  uint32_t checksum;            // 数据校验和
};
```

### 核心方法
- `setup_energy_persistence()`: 初始化持久化存储
- `load_energy_data()`: 从flash加载电量数据
- `save_energy_data()`: 保存电量数据到flash
- `update_total_energy()`: 更新累计电量
- `reset_energy_data()`: 重置累计电量数据

### 电量计算原理
1. 读取BL0906的脉冲计数寄存器(CF_CNT)
2. 计算与上次读取的脉冲增量
3. 根据校准系数将脉冲转换为电量(kWh)
4. 累加到对应通道的总电量
5. 定期保存到flash存储

## 配置说明

### 1. 启用preferences组件
```yaml
preferences:
  flash_write_interval: 1min  # 每分钟写入一次flash
```

### 2. 配置累计电量传感器
```yaml
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_chip
    # 各通道累计电量传感器
    total_energy_1:
      name: "Channel 1 Total Energy"
      unit_of_measurement: kWh
      accuracy_decimals: 3
      device_class: energy
      state_class: total_increasing
    
    # 总累计电量传感器
    total_energy_sum:
      name: "Total Accumulated Energy"
      unit_of_measurement: kWh
      accuracy_decimals: 3
      device_class: energy
      state_class: total_increasing
```

### 3. 添加控制按钮和开关
```yaml
# 重置累计电量按钮
button:
  - platform: template
    name: "Reset Total Energy"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = id(bl0906_chip);
            bl0906->reset_energy_data();

# 电量持久化开关
switch:
  - platform: template
    name: "Energy Persistence"
    optimistic: true
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - lambda: |-
          auto bl0906 = id(bl0906_chip);
          bl0906->set_energy_persistence_enabled(true);
    turn_off_action:
      - lambda: |-
          auto bl0906 = id(bl0906_chip);
          bl0906->set_energy_persistence_enabled(false);
```

## 使用方法

### 1. 基本使用
1. 在配置文件中启用preferences组件
2. 配置累计电量传感器
3. 编译并上传固件
4. 设备启动后会自动开始累计电量

### 2. 数据管理
- **查看累计电量**: 通过Home Assistant或ESPHome日志查看
- **重置累计电量**: 使用重置按钮或调用`reset_energy_data()`方法
- **启用/禁用持久化**: 使用开关或调用`set_energy_persistence_enabled()`方法

### 3. 监控和调试
- 查看ESPHome日志了解电量累计过程
- 使用DEBUG日志级别查看详细的脉冲计数信息
- 监控flash写入频率避免过度磨损

## 注意事项

### 1. Flash存储寿命
- 默认每分钟保存一次数据，平衡数据安全性和flash寿命
- 可根据需要调整保存间隔
- 避免频繁重置累计电量数据

### 2. 数据精度
- 电量计算基于BL0906的脉冲计数，精度取决于校准质量
- 建议定期校准以确保测量精度
- 累计电量可能存在微小的累积误差

### 3. 系统兼容性
- 需要ESPHome 2023.x或更高版本
- 需要ESP32或ESP8266平台
- 确保有足够的flash存储空间

## 故障排除

### 1. 数据不保存
- 检查preferences组件是否正确配置
- 确认flash存储空间充足
- 查看日志中的错误信息

### 2. 电量计算异常
- 检查BL0906校准参数是否正确
- 确认脉冲计数寄存器读取正常
- 验证电量转换系数

### 3. 数据校验失败
- 可能是flash数据损坏，会自动重置
- 检查设备供电是否稳定
- 考虑增加数据备份机制

## 扩展功能

### 1. 数据导出
可以添加MQTT或HTTP API接口导出累计电量数据

### 2. 多级统计
可以扩展支持日、月、年电量统计

### 3. 报警功能
可以添加电量超限报警功能

### 4. 数据可视化
可以集成图表组件显示电量趋势

## 总结

电量数据持久化存储功能为BL0906组件提供了完整的电量统计能力，通过flash存储确保数据在设备重启后不丢失，是构建智能电量监控系统的重要基础功能。
