# SPI通讯协议

## 5. 通讯接口

寄存器数据均按3字节（24bit）发送，不足3字节的寄存器数据，未使用位补0，凑足3字节发送。

通过管脚SEL选择：
- SEL=1：SPI模式
- SEL=0：UART模式

## 5.1 SPI

### 5.1.1 概述

- 从模式，半双工通讯，最大通讯速率1.5M
- 8-bit数据传输，MSB在前，LSB在后
- 固定一种时钟极性/相位（CPOL=0，CPHA=1）

### 5.1.2 工作模式

主设备工作在Mode1：CPOL=0，CPHA=1
- 空闲态时，SCLK处于低电平
- 数据发送是在第1个边沿，也就是SCLK由低电平到高电平的跳变
- 数据采样是在下降沿，数据发送是在上升沿

### 5.1.3 帧结构

在通信模式下，先发送8bit识别字节：
- `0x82`：读识别字节
- `0x81`：写识别字节

然后再发送寄存器地址字节，决定访问寄存器的地址（请参见BL0910寄存器列表）。

一帧数据传送完成，BL0910重新进入通信模式。每次读/写操作所需的SCLK的脉冲个数均为48位。

#### 1) 写寄存器操作

**命令格式：**
```
Cmd: {0x81} + Addr + Data_H + Data_M + Data_L + SUM
```

**字段说明：**
- `{0x81}`：写操作的帧识别字节
- `Addr`：写操作对应的BL0910的内部寄存器地址
- `CHECKSUM`：校验和字节 = `((0x81 + ADDR + DATA_H + DATA_M + DATA_L) & 0xFF)` 再按位取反

**帧结构表：**

| 字节1 | 字节2 | 字节3 | 字节4 | 字节5 | 字节6 |
|-------|-------|-------|-------|-------|-------|
| 0x81 | ADDR[7:0] | DATA_H[7:0] | DATA_M[7:0] | DATA_L[7:0] | CHECKSUM[7:0] |

#### 2) 读寄存器操作

**命令格式：**
```
Cmd: {0x82} + Addr
返回: Data_H + Data_M + Data_L + SUM
```

**字段说明：**
- `{0x82}`：读操作的帧识别字节
- `Addr`：读操作对应的BL0910的内部寄存器地址(0x00-0xFF)
- `CHECKSUM`：校验和字节 = `((0x82 + ADDR + DATA_H + DATA_M + DATA_L) & 0xFF)` 再按位取反

**发送帧结构表：**

| 字节1 | 字节2 |
|-------|-------|
| 0x82 | ADDR[7:0] |

**返回帧结构表：**

| 字节1 | 字节2 | 字节3 | 字节4 |
|-------|-------|-------|-------|
| DATA_H[7:0] | DATA_M[7:0] | DATA_L[7:0] | CHECKSUM[7:0] |

### 5.1.4 读出操作时序

在对BL0910进行数据读出操作期间：
1. 在SCLK的上升沿，BL0910将相应的数据位移出到DOUT逻辑输出管脚
2. 在接下来的SCLK为1的时间内，DOUT数值保持不变
3. 在下一个下降沿时，外部设备可以对DOUT值进行采样
4. 在数据读出操作之前MCU必须先发送识别字节和地址字节

**时序说明：**
```
SCLK:     __|‾|__|‾|__|‾|__|‾|__|‾|__|‾|__|‾|__|‾|__
          
发送阶段:  [0x82] [ADDR[7:0]]

接收阶段:         [DATA_H[7:0]] [DATA_M[7:0]] [DATA_L[7:0]] [CHECKSUM[7:0]]
```

当BL0910处于通信模式时：
1. 帧识别字节`{0x82}`表示下一个数据传送操作是读出
2. 然后紧跟的字节是待读出目标寄存器的地址
3. BL0910在SCLK的上升沿开始移出寄存器中的数据
4. 寄存器数据的所有其余位在随后的SCLK上升沿被移出
5. 在下降沿，外部设备可以对SPI的输出数据进行采样操作
6. 一旦读出操作结束，串行接口便重新进入通信模式
7. DOUT逻辑输出在最后一个SCLK信号的下降沿进入高阻状态

### 5.1.5 写入操作时序

串行写入顺序按下述方式进行：
1. 帧识别字节`{0x81}`表示数据传送操作是写入
2. MCU将需要写入BL0910的数据位在SCLK的下沿之前准备好
3. 在SCLK的该时钟的下沿开始移入寄存器数据
4. 寄存器数据的所有其余位也在该SCLK的下沿进行左移移位操作

**时序说明：**
```
SCLK:     __|‾|__|‾|__|‾|__|‾|__|‾|__|‾|__|‾|__|‾|__

SDO:      [0x81] [ADDR[7:0]] [DATA_H[7:0]] [DATA_M[7:0]] [DATA_L[7:0]] [CHECKSUM[7:0]]
```

### 5.1.6 SPI接口的容错机制

1. **数据校验**：如果帧识别字节错误或SUM字节错误，则该帧数据放弃
2. **SPI模块复位**：通过SPI接口下发6个字节的0xFF，可单独对SPI接口进行复位
3. **CS复位**：CS拉高复位
