# BL0906校准数据存储方案对比

## 存储方案对比表

| 特性 | 自定义Flash分区 | EEPROM分区 | NVS/Preferences |
|------|----------------|------------|-----------------|
| **分区位置** | 0x290000 | 0x390000 | NVS分区 |
| **存储空间** | 16KB | 4KB | 共享NVS空间 |
| **ESPHome支持** | ❌ 需要自定义分区表 | ✅ 原生支持 | ✅ 原生支持 |
| **配置复杂度** | 高 | 低 | 低 |
| **独立性** | ✅ 完全独立 | ✅ 独立分区 | ❌ 与其他数据共享 |
| **实现难度** | 高（分区表配置问题） | 中 | 低 |
| **写入限制** | Flash寿命限制 | 约10万次 | NVS寿命限制 |
| **数据结构** | 自定义 | 自定义 | 键值对 |

## 推荐方案：EEPROM存储

### 原因：
1. **平衡性好**：在独立性和易用性之间取得平衡
2. **空间足够**：4KB足够存储128个校准条目（实际需求约20-30个）
3. **无需修改分区表**：避免ESPHome分区表配置的复杂性
4. **原生支持**：ESPHome和Arduino框架都原生支持EEPROM

### EEPROM使用示例：
```cpp
// 初始化
EEPROM.begin(4096);  // 4KB

// 写入数据
EEPROM.put(address, data);
EEPROM.commit();  // 保存到Flash

// 读取数据
EEPROM.get(address, data);
```

## 实施建议

1. **短期方案**：使用EEPROM存储校准数据
   - 快速实现
   - 无需修改分区表
   - 稳定可靠

2. **长期优化**：如果将来ESPHome改进分区表支持
   - 可以迁移到自定义Flash分区
   - 获得更大存储空间
   - 更好的数据隔离

## 代码架构优势

使用继承结构，便于切换存储方案：
```
CalibrationStorageBase (基类)
    ├── CalibrationStorage (Flash存储)
    └── EEPROMCalibrationStorage (EEPROM存储)
```

通过编译标志轻松切换：
- `-DUSE_FLASH_CALIBRATION`：使用Flash分区
- `-DUSE_EEPROM_CALIBRATION`：使用EEPROM分区 