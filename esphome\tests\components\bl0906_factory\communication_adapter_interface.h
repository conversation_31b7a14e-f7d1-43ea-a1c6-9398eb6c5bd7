#pragma once

#include <string>
#include <cstdint>
#include <cstddef>

namespace esphome {
namespace bl0906_factory {

/**
 * 通信错误码枚举
 */
enum class CommunicationError {
  SUCCESS = 0,
  TIMEOUT,
  CHECKSUM_ERROR,
  DEVICE_NOT_AVAILABLE,
  BUFFER_OVERFLOW,
  INVALID_RESPONSE,
  HARDWARE_ERROR,
  UNKNOWN_ERROR
};

/**
 * 通信统计信息结构体
 */
struct CommunicationStats {
  size_t success_count = 0;
  size_t error_count = 0;
  size_t timeout_count = 0;
  size_t checksum_error_count = 0;
  uint32_t last_error_timestamp = 0;
  CommunicationError last_error = CommunicationError::SUCCESS;
};

/**
 * 通信适配器接口
 * 
 * 这个接口定义了BL0906Factory与底层通信硬件（UART/SPI）之间的统一接口。
 * 所有具体的通信实现都必须实现这个接口。
 */
class CommunicationAdapterInterface {
public:
  virtual ~CommunicationAdapterInterface() = default;
  
  // ========== 核心通信方法 ==========
  
  /**
   * 初始化通信适配器
   * @return true 初始化成功，false 初始化失败
   */
  virtual bool initialize() = 0;
  
  /**
   * 读取寄存器值
   * @param address 寄存器地址
   * @param success 可选的成功标志指针，用于返回操作是否成功
   * @return 读取到的寄存器值，如果失败返回0
   */
  virtual int32_t read_register(uint8_t address, bool* success = nullptr) = 0;
  
  /**
   * 写入寄存器值
   * @param address 寄存器地址
   * @param value 要写入的值
   * @return true 写入成功，false 写入失败
   */
  virtual bool write_register(uint8_t address, int16_t value) = 0;
  
  /**
   * 发送原始命令序列（用于特殊操作如写保护解锁）
   * @param command 原始命令字节数组
   * @param length 命令长度
   * @return true 发送成功，false 发送失败
   */
  virtual bool send_raw_command(const uint8_t* command, size_t length) = 0;
  
  // ========== 状态查询方法 ==========
  
  /**
   * 检查通信设备是否可用
   * @return true 设备可用，false 设备不可用
   */
  virtual bool is_available() = 0;
  
  /**
   * 检查是否已连接到设备
   * @return true 已连接，false 未连接
   */
  virtual bool is_connected() = 0;
  
  // ========== 缓冲区管理 ==========
  
  /**
   * 清空接收缓冲区
   */
  virtual void flush_buffer() = 0;
  
  // ========== 错误处理 ==========
  
  /**
   * 获取最后一次错误的描述
   * @return 错误描述字符串
   */
  virtual std::string get_last_error() const = 0;
  
  /**
   * 重置错误状态
   */
  virtual void reset_error_state() = 0;
  
  /**
   * 获取最后一次错误码
   * @return 错误码
   */
  virtual CommunicationError get_last_error_code() const = 0;
  
  // ========== 统计信息 ==========
  
  /**
   * 获取成功操作次数
   * @return 成功次数
   */
  virtual size_t get_success_count() const = 0;
  
  /**
   * 获取错误操作次数
   * @return 错误次数
   */
  virtual size_t get_error_count() const = 0;
  
  /**
   * 获取完整的统计信息
   * @return 统计信息结构体
   */
  virtual CommunicationStats get_statistics() const = 0;
  
  /**
   * 重置统计信息
   */
  virtual void reset_statistics() = 0;
  
  // ========== 调试和诊断 ==========
  
  /**
   * 获取适配器类型名称（用于日志和调试）
   * @return 适配器类型名称
   */
  virtual std::string get_adapter_type() const = 0;
  
  /**
   * 获取适配器状态信息（用于诊断）
   * @return 状态信息字符串
   */
  virtual std::string get_status_info() const = 0;
  
  /**
   * 执行自检
   * @return true 自检通过，false 自检失败
   */
  virtual bool self_test() = 0;
};

} // namespace bl0906_factory
} // namespace esphome 