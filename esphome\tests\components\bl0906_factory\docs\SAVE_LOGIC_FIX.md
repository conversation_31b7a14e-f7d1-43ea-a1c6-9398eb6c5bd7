# 保存逻辑修复说明

## 问题描述

原始代码中的保存逻辑存在以下问题：

1. **错误的执行顺序**：在 `save_energy_data()` 方法中，先保存数据到Flash，然后才更新统计传感器
2. **递归调用问题**：在 `handle_new_day()` 等方法中调用 `save_statistics_data_safe()`，导致无限递归调用

## 修复方案

### 1. 修正保存逻辑顺序

**修改前的错误逻辑：**
```cpp
void BL0906Factory::save_energy_data() {
  // 1. 更新统计数据
  energy_stats_manager_->update_statistics_on_save();
  
  // 2. 准备数据结构
  OptimizedEnergyPersistenceData data;
  // ... 填充数据 ...
  
  // 3. 保存到Flash
  bool save_success = energy_pref_.save(&data);
  
  // 4. 保存成功后才更新传感器 ❌ 错误！
  if (save_success) {
    energy_stats_manager_->update_sensors_on_save();
  }
}
```

**修改后的正确逻辑：**
```cpp
void BL0906Factory::save_energy_data() {
  // 第一步：先更新统计数据和传感器 ✅
  if (energy_statistics_enabled_ && energy_stats_manager_) {
    ESP_LOGD(FACTORY_TAG, "更新统计数据...");
    energy_stats_manager_->update_statistics_on_save();
    
    ESP_LOGD(FACTORY_TAG, "更新统计传感器数据...");
    energy_stats_manager_->update_sensors_on_save();
  }

  // 第二步：准备保存数据结构
  OptimizedEnergyPersistenceData data;
  // ... 填充数据（包含已更新的传感器数据）...
  
  // 第三步：保存到Flash
  bool save_success = energy_pref_.save(&data);
}
```

### 2. 解决递归调用问题

**问题分析：**
- `handle_new_day()` → `save_statistics_data_safe()` → `parent_->save_energy_data()` → `update_statistics_on_save()` → 无限循环

**解决方案：**
在 `handle_new_*` 方法中不再调用 `save_statistics_data_safe()`，而是等待下次正常的保存周期：

```cpp
void EnergyStatisticsManager::handle_new_day() {
  // 更新统计数据，但不立即保存
  // ... 更新逻辑 ...
  
  // 注意：这里不调用save_statistics_data_safe()，避免递归调用
  // 数据会在下次正常的save_energy_data()调用时被保存
  ESP_LOGI(ENERGY_STATS_TAG, "新日统计数据更新完成，等待下次保存周期");
}
```

## 修复的好处

### 1. 数据一致性保证
- 确保传感器显示的数据与保存到Flash的数据完全一致
- 避免传感器数据滞后于实际保存的数据

### 2. 避免数据丢失
- 如果Flash保存失败，至少传感器已经更新了最新数据
- 用户可以立即看到最新的统计信息

### 3. 性能优化
- 避免递归调用导致的栈溢出风险
- 减少不必要的Flash写入操作

### 4. 逻辑清晰
- 保存流程更加直观：更新 → 保存
- 便于调试和维护

## 测试验证

使用 `test_save_logic.yaml` 配置文件可以验证修复效果：

1. **手动保存测试**：通过按钮触发保存，观察日志输出顺序
2. **定时保存测试**：每30秒自动保存，验证长期稳定性
3. **诊断功能**：查看保存的数据是否与传感器数据一致

## 关键修改点

### BL0906Factory::save_energy_data()
- ✅ 先更新统计数据和传感器
- ✅ 再获取最新数据并保存
- ✅ 移除保存成功后的传感器更新

### EnergyStatisticsManager::handle_new_*()
- ✅ 移除 `save_statistics_data_safe()` 调用
- ✅ 避免递归调用问题
- ✅ 数据在下次正常保存周期时自动保存

### EnergyStatisticsManager::calculate_energy_for_period()
- ✅ 修正通道范围检查，支持总和通道（索引6）

## 总结

通过这次修复，保存逻辑变得更加合理和安全：
1. **先更新传感器数据**，确保用户看到最新信息
2. **再保存所有数据**，确保数据一致性
3. **避免递归调用**，提高系统稳定性

这样的设计确保了电量统计功能的可靠性和用户体验。 