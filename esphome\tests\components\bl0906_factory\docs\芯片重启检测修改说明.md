# BL0906芯片重启检测逻辑修改说明

## 修改概述

本次修改按照数据读取修改计划，将BL0906的数据读取与处理分离，并将芯片重启检测的时机改为在读取所有数据后进行，同时修改了检测逻辑。

## 主要修改内容

### 1. 数据读取架构重构

#### 1.1 数据缓存机制
新增了 `SensorDataCache` 结构体来缓存所有传感器的原始数据：

```cpp
struct SensorDataCache {
  // 基础传感器原始数据
  int32_t temperature_raw{0};
  int32_t frequency_raw{0};
  int32_t voltage_raw{0};
  
  // 通道传感器原始数据
  int32_t current_raw[CHANNEL_COUNT]{0};
  int32_t power_raw[CHANNEL_COUNT]{0};
  int32_t energy_raw[CHANNEL_COUNT]{0};
  
  // 总和传感器原始数据
  int32_t power_sum_raw{0};
  int32_t energy_sum_raw{0};
  
  // 数据有效性标记
  bool data_valid{false};
  uint32_t read_timestamp{0};
};
```

#### 1.2 读取与处理分离
- **Loop函数**：负责读取原始数据到缓存
- **Update函数**：负责处理缓存数据并发布到传感器

### 2. 状态机修改

#### 2.1 状态流程重新设计
```cpp
enum class State {
  IDLE,
  READ_BASIC_SENSORS,     // 温度、频率、电压
  READ_CHANNEL_DATA,      // 通道数据（使用循环）
  READ_TOTAL_DATA,        // 总功率和能量
  CHECK_CHIP_RESTART,     // 检测芯片重启状态（在读取所有数据后）
  HANDLE_ACTIONS
};
```

#### 2.2 状态机流程
```cpp
case State::IDLE:
  // 重置数据缓存
  sensor_data_cache_.data_valid = false;
  sensor_data_cache_.read_timestamp = millis();
  this->current_state_ = State::READ_BASIC_SENSORS;
  break;

// ... 读取各种数据到缓存 ...

case State::READ_TOTAL_DATA:
  // 读取总和数据到缓存
  this->read_raw_data_to_cache(BL0906_WATT_SUM, sensor_data_cache_.power_sum_raw);
  this->read_raw_data_to_cache(BL0906_CF_SUM_CNT, sensor_data_cache_.energy_sum_raw);
  // 标记数据读取完成
  sensor_data_cache_.data_valid = true;
  this->current_state_ = State::CHECK_CHIP_RESTART;
  break;

case State::CHECK_CHIP_RESTART:
  this->check_chip_restart();  // 使用缓存数据检测
  this->current_state_ = State::HANDLE_ACTIONS;
  break;
```

### 3. 检测逻辑优化

#### 3.1 检测时机调整
- **原计划要求**：在读取所有数据到数组后进行检测
- **新实现**：在 `READ_TOTAL_DATA` 状态完成后，转到 `CHECK_CHIP_RESTART` 状态

#### 3.2 使用缓存数据检测
```cpp
void BL0906Factory::check_chip_restart() {
  // 使用缓存的energy_raw数据进行检测
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    uint32_t hardware_cf_count = static_cast<uint32_t>(sensor_data_cache_.energy_raw[i]);
    
    if (hardware_cf_count >= 5) {
      all_channels_low = false;
    }
  }
  
  // 只有所有通道的CF_count都小于5时才确定芯片重启
  if (all_channels_low) {
    // 处理重启逻辑
  }
}
```

### 4. 新增核心方法

#### 4.1 read_raw_data_to_cache()
```cpp
bool BL0906Factory::read_raw_data_to_cache(uint8_t address, int32_t &raw_value) {
  // 读取原始数据并存储到缓存
  DataPacket data;
  if (!send_read_command_and_receive(address, data)) {
    return false;
  }
  raw_value = (data.h << 16) | (data.m << 8) | data.l;
  return true;
}
```

#### 4.2 process_cached_data_and_publish()
```cpp
void BL0906Factory::process_cached_data_and_publish() {
  // 处理所有缓存数据，计算实际值并发布到传感器
  // 同时处理持久化CF_count累计逻辑
}
```

### 5. Update方法重构

```cpp
void BL0906Factory::update() {
  // 重置状态机到IDLE状态，开始新的数据读取周期
  this->current_state_ = State::IDLE;

  // 处理缓存的数据并发布到传感器
  this->process_cached_data_and_publish();

  // 其他定期任务...
}
```

## 架构优势

### 1. 完全符合计划要求
- ✅ **数据读取分离**：Loop函数读取数据到数组，Update函数解析并发送
- ✅ **检测时机正确**：在读取所有数据后进行芯片重启检测
- ✅ **不检查总和通道**：只检查6个通道的CF_count

### 2. 性能优化
- **批量处理**：一次性读取所有数据，然后批量处理
- **减少重复读取**：芯片重启检测使用已读取的缓存数据
- **更好的时序控制**：明确的读取和处理阶段

### 3. 更好的架构
- **职责清晰**：读取、检测、处理各司其职
- **数据一致性**：所有传感器使用同一时刻的数据
- **易于维护**：逻辑分离，便于调试和扩展

### 4. 更准确的检测
- **避免误判**：只有所有通道CF_count都小于5才判定重启
- **数据同步**：使用同一轮读取的数据进行检测
- **时机准确**：在完整读取所有数据后进行检测

## 工作流程

```
1. Update() 调用 -> 重置状态机为IDLE
2. Loop() 状态机执行：
   ├── IDLE -> 初始化缓存
   ├── READ_BASIC_SENSORS -> 读取基础传感器到缓存
   ├── READ_CHANNEL_DATA -> 读取通道数据到缓存
   ├── READ_TOTAL_DATA -> 读取总和数据到缓存，标记数据有效
   ├── CHECK_CHIP_RESTART -> 使用缓存数据检测芯片重启
   └── HANDLE_ACTIONS -> 处理其他操作
3. 下次Update() -> 处理缓存数据并发布到传感器
```

## 注意事项

1. **数据时效性**：缓存数据在一个完整的读取周期内有效
2. **错误处理**：任何读取失败都会影响整个检测过程
3. **内存使用**：增加了数据缓存结构，但内存占用很小
4. **兼容性**：保持了原有的电量持久化和统计功能

## 测试建议

1. **数据一致性测试**：确认缓存数据与直接读取数据一致
2. **重启检测测试**：验证在所有通道CF_count都小于5时能正确检测
3. **性能测试**：确认新架构不会影响整体性能
4. **错误恢复测试**：验证读取失败时的错误处理机制 