# BL0906 Factory 电量统计传感器使用指南

## 概述

BL0906 Factory组件现在支持电量统计传感器，可以提供昨日、今日、本周、本月、本年的电量统计数据。这些传感器基于持久化存储的CF_count数据，能够在设备重启后保持数据的连续性。

**新的设计理念**：电量统计传感器现在按通道进行组织，每个通道包含完整的电量统计项目，使配置更加直观和易于管理。

## 支持的电量统计传感器类型

### 按通道组织的电量统计传感器（通道1-6）

每个通道（1-6）都支持以下完整的传感器组：

**基础传感器**：
- `current_X`: 通道X电流
- `power_X`: 通道X功率  
- `energy_X`: 通道X实时电量
- `total_energy_X`: 通道X累计电量（持久化存储）

**电量统计传感器**：
- `yesterday_energy_X`: 通道X昨日电量
- `today_energy_X`: 通道X今日电量
- `week_energy_X`: 通道X本周电量
- `month_energy_X`: 通道X本月电量
- `year_energy_X`: 通道X本年电量

### 总电量统计传感器（所有通道合计）

- `yesterday_total_energy`: 昨日总电量
- `today_total_energy`: 今日总电量  
- `week_total_energy`: 本周总电量
- `month_total_energy`: 本月总电量
- `year_total_energy`: 本年总电量

## 配置示例

### 按通道组织的基本配置

```yaml
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    
    # 通道1完整传感器组
    current_1:
      name: "通道1电流"
    power_1:
      name: "通道1功率"
    energy_1:
      name: "通道1实时电量"
    total_energy_1:
      name: "通道1累计电量"
    # 通道1电量统计
    yesterday_energy_1:
      name: "通道1昨日电量"
    today_energy_1:
      name: "通道1今日电量"
    week_energy_1:
      name: "通道1本周电量"
    month_energy_1:
      name: "通道1本月电量"
    year_energy_1:
      name: "通道1本年电量"
    
    # 通道2完整传感器组
    current_2:
      name: "通道2电流"
    power_2:
      name: "通道2功率"
    energy_2:
      name: "通道2实时电量"
    total_energy_2:
      name: "通道2累计电量"
    # 通道2电量统计
    yesterday_energy_2:
      name: "通道2昨日电量"
    today_energy_2:
      name: "通道2今日电量"
    week_energy_2:
      name: "通道2本周电量"
    month_energy_2:
      name: "通道2本月电量"
    year_energy_2:
      name: "通道2本年电量"
    
    # 总电量统计传感器
    yesterday_total_energy:
      name: "昨日总电量"
    today_total_energy:
      name: "今日总电量"
    week_total_energy:
      name: "本周总电量"
    month_total_energy:
      name: "本月总电量"
    year_total_energy:
      name: "本年总电量"
```

### 完整配置示例

```yaml
esphome:
  name: bl0906-energy-monitor
  platform: ESP32
  board: esp32dev

uart:
  id: uart_bus
  tx_pin: GPIO1
  rx_pin: GPIO3
  baud_rate: 9600

# 时间组件（电量统计功能需要）
time:
  - platform: sntp
    id: sntp_time
    timezone: Asia/Shanghai

bl0906_factory:
  id: bl0906_device
  uart_id: uart_bus
  update_interval: 60s
  # 启用电量统计功能
  energy_statistics_enabled: true
  time_component: sntp_time

sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    
    # 基础传感器
    voltage:
      name: "电压"
    frequency:
      name: "频率"
    temperature:
      name: "温度"
    
    # 通道1传感器组（完整配置）
    current_1:
      name: "通道1电流"
    power_1:
      name: "通道1功率"
    energy_1:
      name: "通道1实时电量"
    total_energy_1:
      name: "通道1累计电量"
    yesterday_energy_1:
      name: "通道1昨日电量"
    today_energy_1:
      name: "通道1今日电量"
    week_energy_1:
      name: "通道1本周电量"
    month_energy_1:
      name: "通道1本月电量"
    year_energy_1:
      name: "通道1本年电量"
    
    # 通道2传感器组（完整配置）
    current_2:
      name: "通道2电流"
    power_2:
      name: "通道2功率"
    energy_2:
      name: "通道2实时电量"
    total_energy_2:
      name: "通道2累计电量"
    yesterday_energy_2:
      name: "通道2昨日电量"
    today_energy_2:
      name: "通道2今日电量"
    week_energy_2:
      name: "通道2本周电量"
    month_energy_2:
      name: "通道2本月电量"
    year_energy_2:
      name: "通道2本年电量"
    
    # 通道3传感器组（部分配置示例）
    current_3:
      name: "通道3电流"
    power_3:
      name: "通道3功率"
    yesterday_energy_3:
      name: "通道3昨日电量"
    today_energy_3:
      name: "通道3今日电量"
    
    # 总和传感器
    power_sum:
      name: "总功率"
    energy_sum:
      name: "总实时电量"
    total_energy_sum:
      name: "总累计电量"
    
    # 总电量统计传感器
    yesterday_total_energy:
      name: "昨日总电量"
    today_total_energy:
      name: "今日总电量"
    week_total_energy:
      name: "本周总电量"
    month_total_energy:
      name: "本月总电量"
    year_total_energy:
      name: "本年总电量"
```

## 配置优势

### 1. 按通道组织
- **直观性**：每个通道的所有传感器集中在一起，便于理解和管理
- **完整性**：每个通道包含从基础测量到统计分析的完整传感器组
- **灵活性**：可以选择性配置每个通道需要的传感器

### 2. 层次化结构
```
通道X传感器组:
├── 基础测量: current_X, power_X, energy_X
├── 累计数据: total_energy_X
└── 统计分析: yesterday_energy_X, today_energy_X, week_energy_X, month_energy_X, year_energy_X

总电量统计:
└── 全局统计: yesterday_total_energy, today_total_energy, week_total_energy, month_total_energy, year_total_energy
```

### 3. 选择性配置
```yaml
# 示例：只配置通道1的基础传感器和今日统计
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    
    # 通道1最小配置
    current_1:
      name: "通道1电流"
    power_1:
      name: "通道1功率"
    today_energy_1:
      name: "通道1今日电量"
    
    # 总电量统计
    today_total_energy:
      name: "今日总电量"
```

## 传感器属性

所有电量统计传感器都具有以下属性：

- **单位**: kWh（千瓦时）
- **精度**: 3位小数
- **设备类别**: energy
- **状态类别**: total_increasing
- **更新频率**: 在持久化保存时更新（默认20分钟间隔）

## 工作原理

1. **数据源**: 基于BL0906硬件的CF_count寄存器数据
2. **持久化存储**: 使用ESPHome的preferences API保存数据
3. **时间管理**: 基于RealTimeClock组件进行日期变更检测
4. **统计计算**: 通过比较不同时间点的持久化CF_count值计算电量差值

## 注意事项

1. **时间组件依赖**: 电量统计功能需要配置时间组件（如SNTP）
2. **数据持久性**: 统计数据在设备重启后会保持，但首次使用时需要一段时间积累数据
3. **更新频率**: 统计传感器在持久化保存时更新，不是实时更新
4. **内存使用**: 启用电量统计功能会增加一定的内存使用量
5. **通道组织**: 建议按通道组织配置，便于管理和理解

## 故障排除

### 统计数据为0或不更新

1. 检查时间组件是否正常工作
2. 确认`energy_statistics_enabled`设置为true
3. 检查日志中是否有错误信息
4. 等待至少一个保存周期（20分钟）

### 数据不准确

1. 检查设备时间是否正确
2. 确认CF_count数据是否正常读取
3. 检查持久化存储是否正常工作

## 示例Home Assistant配置

```yaml
# configuration.yaml
sensor:
  - platform: template
    sensors:
      # 通道1电费计算
      channel_1_daily_cost:
        friendly_name: "通道1今日电费"
        unit_of_measurement: "元"
        value_template: "{{ (states('sensor.today_energy_1') | float * 0.6) | round(2) }}"
      
      # 总电费计算
      total_daily_cost:
        friendly_name: "今日总电费"
        unit_of_measurement: "元"
        value_template: "{{ (states('sensor.today_total_energy') | float * 0.6) | round(2) }}"
      
      # 通道效率分析
      channel_1_efficiency:
        friendly_name: "通道1效率占比"
        unit_of_measurement: "%"
        value_template: >
          {% set ch1 = states('sensor.today_energy_1') | float %}
          {% set total = states('sensor.today_total_energy') | float %}
          {{ ((ch1 / total * 100) if total > 0 else 0) | round(1) }}
```

这样就可以在Home Assistant中实现按通道的电量统计分析和成本计算。 