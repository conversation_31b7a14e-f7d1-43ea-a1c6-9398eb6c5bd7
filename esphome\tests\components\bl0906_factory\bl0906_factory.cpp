#include "bl0906_factory.h"
#include "bl0906_number.h"
#include "energy_statistics_manager.h"
#include "adapter_registry.h"

#include <map>
#include <string>
#include <cstring>  // for memcpy
#include <cmath>    // for abs
#include <memory>   // for std::unique_ptr
#include "esphome/core/hal.h"
#include "esphome/core/application.h"
#include "esphome/core/time.h"
#include "esphome/components/time/real_time_clock.h"

// 避免命名空间冲突
namespace esphome_time = esphome::time;

/*
 * 重要说明：
 * 此组件继承了uart::UARTDevice，因此不需要使用parent_指针。
 * 在ESPHome中，如果组件继承了UARTDevice，它已经可以直接使用this->write_byte()等方法进行通信。
 * 因此，不需要调用set_parent方法或使用parent_指针。
 * 本次修改将所有使用parent_指针的地方改为使用继承的UART方法（this->write_byte等）
 */

namespace esphome {
namespace bl0906_factory {

static const char *const FACTORY_TAG = "bl0906_factory";  // 修改日志标签
const char *const BL0906Factory::BL0906_FACTORY_ID = "bl0906_factory";
// 定义静态实例指针 - 修改：指向 BL0906Factory 类型
BL0906Factory *BL0906Factory::bl0906_instance = nullptr;

// 旧的写保护命令数组已被适配器中的动态命令生成替代

// 添加校验和函数定义
uint8_t bl0906_checksum(const uint8_t address, const DataPacket *data) {
  uint8_t sum = address;
  sum += data->l;
  sum += data->m;
  sum += data->h;
  return sum ^ 0xFF;
}
BL0906Factory::BL0906Factory() {}

// ========== 通信适配器接口实现 ==========

void BL0906Factory::set_communication_adapter(std::unique_ptr<CommunicationAdapterInterface> adapter) {
  comm_adapter_ = std::move(adapter);
  ESP_LOGI(FACTORY_TAG, "设置通信适配器: %s", 
           comm_adapter_ ? comm_adapter_->get_adapter_type().c_str() : "null");
}

CommunicationAdapterInterface* BL0906Factory::get_communication_adapter() const {
  return comm_adapter_.get();
}

// 统一的寄存器读取函数（现在通过适配器实现）
// 这是唯一的数据读取函数，所有数据读取都通过此函数
// 修改：添加success参数来区分读取失败和寄存器值为0的情况
int32_t BL0906Factory::send_read_command_and_receive(uint8_t address, bool* success) {
  if (!comm_adapter_) {
    ESP_LOGE(FACTORY_TAG, "通信适配器未设置");
    if (success) *success = false;
    return 0;
  }
  
  return comm_adapter_->read_register(address, success);
}



// 统一的数据转换函数（根据寄存器地址和原始值转换为实际值）
// 这是唯一的数据转换函数，所有数据转换都通过此函数
float BL0906Factory::convert_raw_to_value(uint8_t address, int32_t raw_value) {
  switch (address) {
    case BL0906_FREQUENCY:
      // 频率是无符号数，确保使用无符号计算
      return (raw_value > 0) ? 10000000.0f / static_cast<uint32_t>(raw_value) : 0;
      
    case BL0906_TEMPERATURE:
      // 温度是无符号数
      return (static_cast<uint32_t>(raw_value) - 64) * 12.5f / 59.0f - 40.0f;
      
    case BL0906_V_RMS:
      // 电压是无符号数
      return static_cast<uint32_t>(raw_value) / Kv;
      
    // 电流寄存器（无符号）
    case BL0906_I_RMS[0]:
    case BL0906_I_RMS[1]:
    case BL0906_I_RMS[2]:
    case BL0906_I_RMS[3]:
    case BL0906_I_RMS[4]:
    case BL0906_I_RMS[5]:
      return static_cast<uint32_t>(raw_value) / Ki;
      
    // 功率寄存器（有符号）
    case BL0906_WATT[0]:
    case BL0906_WATT[1]:
    case BL0906_WATT[2]:
    case BL0906_WATT[3]:
    case BL0906_WATT[4]:
    case BL0906_WATT[5]:
      return raw_value / Kp;  // 保持有符号
      
    case BL0906_WATT_SUM:
      return raw_value / Kp_sum;  // 保持有符号
      
    // 电量寄存器（无符号）
    case BL0906_CF_CNT[0]:
    case BL0906_CF_CNT[1]:
    case BL0906_CF_CNT[2]:
    case BL0906_CF_CNT[3]:
    case BL0906_CF_CNT[4]:
    case BL0906_CF_CNT[5]:
      return static_cast<uint32_t>(raw_value) / Ke;
      
    case BL0906_CF_SUM_CNT:
      return static_cast<uint32_t>(raw_value) / Ke_sum;
      
    default:
      ESP_LOGW(FACTORY_TAG, "未知寄存器地址: 0x%02X", address);
      return raw_value;  // 未知寄存器直接返回原始值
  }
}

// 重构后的状态机 - 数据读取和处理完全分离
void BL0906Factory::loop() {
  // 通信适配器会处理缓冲区管理，无需条件编译

  switch (this->current_state_) {
    case State::IDLE:
      // 初始化新的数据读取周期
      current_data_ = {};
      current_data_.timestamp = millis();
      data_collection_complete_ = false;
      data_read_start_time_ = millis();
      this->current_state_ = State::READ_BASIC_SENSORS;
      break;

    case State::READ_BASIC_SENSORS:
      // 直接调用统一读取函数读取基础传感器数据
      {
        bool success = false;
        int32_t temp_raw = send_read_command_and_receive(BL0906_TEMPERATURE, &success);
        current_data_.temperature_raw = success ? static_cast<uint32_t>(temp_raw) : 0;
        
        int32_t freq_raw = send_read_command_and_receive(BL0906_FREQUENCY, &success);
        current_data_.frequency_raw = success ? static_cast<uint32_t>(freq_raw) : 0;
        
        int32_t volt_raw = send_read_command_and_receive(BL0906_V_RMS, &success);
        current_data_.voltage_raw = success ? static_cast<uint32_t>(volt_raw) : 0;
      }
      this->current_state_ = State::READ_CHANNEL_1;
      break;

    case State::READ_CHANNEL_1:
      // 直接调用统一读取函数读取通道1数据
      {
        bool success = false;
        int32_t current_raw = send_read_command_and_receive(BL0906_I_RMS[0], &success);
        current_data_.channels[0].current_raw = success ? static_cast<uint32_t>(current_raw) : 0;
        
        current_data_.channels[0].power_raw = send_read_command_and_receive(BL0906_WATT[0], &success);
        if (!success) current_data_.channels[0].power_raw = 0;
        
        int32_t energy_raw = send_read_command_and_receive(BL0906_CF_CNT[0], &success);
        current_data_.channels[0].energy_raw = success ? static_cast<uint32_t>(energy_raw) : 0;
      }
      this->current_state_ = State::READ_CHANNEL_2;
      break;

    case State::READ_CHANNEL_2:
      // 直接调用统一读取函数读取通道2数据
      {
        bool success = false;
        int32_t current_raw = send_read_command_and_receive(BL0906_I_RMS[1], &success);
        current_data_.channels[1].current_raw = success ? static_cast<uint32_t>(current_raw) : 0;
        
        current_data_.channels[1].power_raw = send_read_command_and_receive(BL0906_WATT[1], &success);
        if (!success) current_data_.channels[1].power_raw = 0;
        
        int32_t energy_raw = send_read_command_and_receive(BL0906_CF_CNT[1], &success);
        current_data_.channels[1].energy_raw = success ? static_cast<uint32_t>(energy_raw) : 0;
      }
      this->current_state_ = State::READ_CHANNEL_3;
      break;

    case State::READ_CHANNEL_3:
      // 直接调用统一读取函数读取通道3数据
      {
        bool success = false;
        int32_t current_raw = send_read_command_and_receive(BL0906_I_RMS[2], &success);
        current_data_.channels[2].current_raw = success ? static_cast<uint32_t>(current_raw) : 0;
        
        current_data_.channels[2].power_raw = send_read_command_and_receive(BL0906_WATT[2], &success);
        if (!success) current_data_.channels[2].power_raw = 0;
        
        int32_t energy_raw = send_read_command_and_receive(BL0906_CF_CNT[2], &success);
        current_data_.channels[2].energy_raw = success ? static_cast<uint32_t>(energy_raw) : 0;
      }
      this->current_state_ = State::READ_CHANNEL_4;
      break;

    case State::READ_CHANNEL_4:
      // 直接调用统一读取函数读取通道4数据
      {
        bool success = false;
        int32_t current_raw = send_read_command_and_receive(BL0906_I_RMS[3], &success);
        current_data_.channels[3].current_raw = success ? static_cast<uint32_t>(current_raw) : 0;
        
        current_data_.channels[3].power_raw = send_read_command_and_receive(BL0906_WATT[3], &success);
        if (!success) current_data_.channels[3].power_raw = 0;
        
        int32_t energy_raw = send_read_command_and_receive(BL0906_CF_CNT[3], &success);
        current_data_.channels[3].energy_raw = success ? static_cast<uint32_t>(energy_raw) : 0;
      }
      this->current_state_ = State::READ_CHANNEL_5;
      break;

    case State::READ_CHANNEL_5:
      // 直接调用统一读取函数读取通道5数据
      {
        bool success = false;
        int32_t current_raw = send_read_command_and_receive(BL0906_I_RMS[4], &success);
        current_data_.channels[4].current_raw = success ? static_cast<uint32_t>(current_raw) : 0;
        
        current_data_.channels[4].power_raw = send_read_command_and_receive(BL0906_WATT[4], &success);
        if (!success) current_data_.channels[4].power_raw = 0;
        
        int32_t energy_raw = send_read_command_and_receive(BL0906_CF_CNT[4], &success);
        current_data_.channels[4].energy_raw = success ? static_cast<uint32_t>(energy_raw) : 0;
      }
      this->current_state_ = State::READ_CHANNEL_6;
      break;

    case State::READ_CHANNEL_6:
      // 直接调用统一读取函数读取通道6数据
      {
        bool success = false;
        int32_t current_raw = send_read_command_and_receive(BL0906_I_RMS[5], &success);
        current_data_.channels[5].current_raw = success ? static_cast<uint32_t>(current_raw) : 0;
        
        current_data_.channels[5].power_raw = send_read_command_and_receive(BL0906_WATT[5], &success);
        if (!success) current_data_.channels[5].power_raw = 0;
        
        int32_t energy_raw = send_read_command_and_receive(BL0906_CF_CNT[5], &success);
        current_data_.channels[5].energy_raw = success ? static_cast<uint32_t>(energy_raw) : 0;
      }
      this->current_state_ = State::READ_TOTAL_DATA;
      break;

    case State::READ_TOTAL_DATA:
      // 直接调用统一读取函数读取总和数据
      {
        bool success = false;
        current_data_.power_sum_raw = send_read_command_and_receive(BL0906_WATT_SUM, &success);
        if (!success) current_data_.power_sum_raw = 0;
        
        int32_t energy_sum_raw = send_read_command_and_receive(BL0906_CF_SUM_CNT, &success);
        current_data_.energy_sum_raw = success ? static_cast<uint32_t>(energy_sum_raw) : 0;
      }
      current_data_.read_complete = true;
      data_collection_complete_ = true;
      this->current_state_ = State::CHECK_CHIP_RESTART;
      break;

    case State::CHECK_CHIP_RESTART:
      detect_chip_restart(current_data_);
      this->current_state_ = State::PROCESS_PERSISTENCE;
      break;

    case State::PROCESS_PERSISTENCE:
      process_energy_persistence(current_data_);
      this->current_state_ = State::UPDATE_STATISTICS;
      break;

    case State::UPDATE_STATISTICS:
      update_energy_statistics(current_data_);
      this->current_state_ = State::PUBLISH_SENSORS;
      break;

    case State::PUBLISH_SENSORS:
      publish_all_sensors(current_data_);
      this->current_state_ = State::HANDLE_ACTIONS;
      break;

    case State::HANDLE_ACTIONS:
      this->handle_actions_();
      break;
  }
}



// 芯片重启检测（基于完整数据集）- 优化版本，避免重复检测
void BL0906Factory::detect_chip_restart(const RawSensorData& data) {
  if (!energy_persistence_enabled_) {
    ESP_LOGV(FACTORY_TAG, "电量持久化存储已禁用，跳过芯片重启检测");
    return;
  }

  // 添加超时机制，防止重启检测永远不恢复（5分钟超时）
  static const uint32_t RESTART_DETECTION_TIMEOUT = 300000;  // 5分钟超时
  if (chip_restart_detected_ && 
      (millis() - last_restart_detection_time_) > RESTART_DETECTION_TIMEOUT) {
    ESP_LOGW(FACTORY_TAG, "芯片重启检测超时，强制重新启用检测");
    chip_restart_detected_ = false;
  }

  ESP_LOGV(FACTORY_TAG, "开始检测BL0906芯片重启...");

  bool all_channels_low = true;  // 标记所有通道是否都小于5

  // 检查各通道的CF_count
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    uint32_t hardware_cf_count = data.channels[i].energy_raw;
    
    ESP_LOGV(FACTORY_TAG, "通道%d硬件CF_count: %u", i+1, hardware_cf_count);

    // 检查是否小于5
    if (hardware_cf_count >= 5) {
      all_channels_low = false;
      ESP_LOGV(FACTORY_TAG, "通道%d CF_count=%u >= 5，芯片未重启", i+1, hardware_cf_count);
    }
  }

  // 如果之前已检测到重启，检查是否可以重新启用检测
  if (chip_restart_detected_) {
    if (!all_channels_low) {
      // 条件不满足了，重新启用检测
      chip_restart_detected_ = false;
      ESP_LOGI(FACTORY_TAG, "芯片重启检测条件不再满足，重新启用重启检测");
    } else {
      // 仍然满足重启条件，跳过检测
      ESP_LOGV(FACTORY_TAG, "芯片重启已检测，跳过重复检测");
      return;
    }
  }

  // 只有所有通道的CF_count都小于5时才确定芯片重启
  if (all_channels_low && !chip_restart_detected_) {
    ESP_LOGW(FACTORY_TAG, "检测到BL0906芯片重启：所有通道CF_count都小于5");
    
    // 设置重启检测标记
    chip_restart_detected_ = true;
    last_restart_detection_time_ = millis();
    chip_restart_count_++;  // 递增重启次数计数器
    
    // 重新记录各通道的CF_count值
    for (int i = 0; i < CHANNEL_COUNT; i++) {
      uint32_t hardware_cf_count = data.channels[i].energy_raw;
      ESP_LOGI(FACTORY_TAG, "通道%d重启时CF_count: %u", i+1, hardware_cf_count);
      last_cf_count_[i] = hardware_cf_count;
    }
    
    // 标记需要保存last_cf_count
    last_cf_count_needs_save_ = true;
    
    ESP_LOGI(FACTORY_TAG, "芯片重启检测完成，已更新基准CF_count值，暂停重启检测 (重启次数: %u)", chip_restart_count_);
  } else {
    ESP_LOGV(FACTORY_TAG, "芯片未重启，继续正常运行");
  }
}

// 持久化存储处理
void BL0906Factory::process_energy_persistence(const RawSensorData& data) {
  if (!energy_persistence_enabled_) {
    return;
  }

  // 处理各通道的电量持久化
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    uint32_t current_count = data.channels[i].energy_raw;

    // 正常情况下，计算脉冲增量并更新持久化CF_count
    if (current_count >= last_cf_count_[i]) {
      uint32_t pulse_increment = current_count - last_cf_count_[i];
      if (pulse_increment > 0) {
        // 更新持久化CF_count
        persistent_cf_count_[i] += pulse_increment;
        
        ESP_LOGD(FACTORY_TAG, "通道%d脉冲增量: %u, 持久化CF_count: %u (硬件: %u -> %u)",
                 i+1, pulse_increment, persistent_cf_count_[i], last_cf_count_[i], current_count);
        
        // 按需保存：每100个脉冲保存一次
        if (pulse_increment >= 100) {
          save_energy_data();
        }
      }
      last_cf_count_[i] = current_count;
    } else {
      // 计数值减少，可能是溢出或其他异常，重新初始化
      ESP_LOGD(FACTORY_TAG, "通道%d脉冲计数异常: %u -> %u，重新初始化",
               i+1, last_cf_count_[i], current_count);
      last_cf_count_[i] = current_count;
      last_cf_count_needs_save_ = true;  // 标记需要保存last_cf_count
    }
  }

  // 处理总脉冲计数
  uint32_t current_sum_count = data.energy_sum_raw;
  const int sum_index = 6;  // 总和存储在数组的第7个元素（索引6）

  if (current_sum_count >= last_cf_count_[sum_index]) {
    uint32_t pulse_increment = current_sum_count - last_cf_count_[sum_index];
    if (pulse_increment > 0) {
      // 更新持久化总CF_count
      persistent_cf_count_[sum_index] += pulse_increment;
      
      ESP_LOGD(FACTORY_TAG, "总脉冲增量: %u, 持久化总CF_count: %u (硬件: %u -> %u)",
               pulse_increment, persistent_cf_count_[sum_index], last_cf_count_[sum_index], current_sum_count);
      
      // 按需保存：每100个脉冲保存一次
      if (pulse_increment >= 100) {
        save_energy_data();
      }
    }
    last_cf_count_[sum_index] = current_sum_count;
  } else {
    // 计数值减少，可能是溢出或其他异常，重新初始化
    ESP_LOGD(FACTORY_TAG, "总脉冲计数异常: %u -> %u，重新初始化",
             last_cf_count_[sum_index], current_sum_count);
    last_cf_count_[sum_index] = current_sum_count;
    last_cf_count_needs_save_ = true;  // 标记需要保存last_cf_count
  }
}

// 能量统计更新
void BL0906Factory::update_energy_statistics(const RawSensorData& data) {
  if (!energy_statistics_enabled_ || !energy_stats_manager_) {
    return;
  }

  // 更新各通道的统计数据
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    uint32_t current_count = data.channels[i].energy_raw;
    if (current_count >= last_cf_count_[i]) {
      uint32_t pulse_increment = current_count - last_cf_count_[i];
      if (pulse_increment > 0) {
        energy_stats_manager_->update_persistent_cf_count(i, pulse_increment);
      }
    }
  }

  // 更新总和统计数据
  uint32_t current_sum_count = data.energy_sum_raw;
  const int sum_index = 6;
  if (current_sum_count >= last_cf_count_[sum_index]) {
    uint32_t pulse_increment = current_sum_count - last_cf_count_[sum_index];
    if (pulse_increment > 0) {
      energy_stats_manager_->update_persistent_cf_count_sum(pulse_increment);
    }
  }
}

// 传感器数据发布
void BL0906Factory::publish_all_sensors(const RawSensorData& data) {
  // 发布基础传感器数据
  if (temperature_sensor_) {
    float value = convert_raw_to_value(BL0906_TEMPERATURE, data.temperature_raw);
    temperature_sensor_->publish_state(value);
    ESP_LOGV(FACTORY_TAG, "温度传感器: 原始值=%u, 计算值=%.2f", data.temperature_raw, value);
  }
  
  if (frequency_sensor_) {
    float value = convert_raw_to_value(BL0906_FREQUENCY, data.frequency_raw);
    frequency_sensor_->publish_state(value);
    ESP_LOGV(FACTORY_TAG, "频率传感器: 原始值=%u, 计算值=%.2f", data.frequency_raw, value);
  }
  
  if (voltage_sensor_) {
    float value = convert_raw_to_value(BL0906_V_RMS, data.voltage_raw);
    voltage_sensor_->publish_state(value);
    ESP_LOGV(FACTORY_TAG, "电压传感器: 原始值=%u, 计算值=%.2f", data.voltage_raw, value);
  }

  // 发布通道传感器数据
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    if (current_sensors_[i]) {
      float value = convert_raw_to_value(BL0906_I_RMS[i], data.channels[i].current_raw);
      current_sensors_[i]->publish_state(value);
      ESP_LOGV(FACTORY_TAG, "通道%d电流: 原始值=%u, 计算值=%.2f", i+1, data.channels[i].current_raw, value);
    }
    
    if (power_sensors_[i]) {
      float value = convert_raw_to_value(BL0906_WATT[i], data.channels[i].power_raw);
      power_sensors_[i]->publish_state(value);
      ESP_LOGV(FACTORY_TAG, "通道%d功率: 原始值=%d, 计算值=%.2f", i+1, data.channels[i].power_raw, value);
    }
    
    if (energy_sensors_[i]) {
      float value = convert_raw_to_value(BL0906_CF_CNT[i], data.channels[i].energy_raw);
      energy_sensors_[i]->publish_state(value);
      ESP_LOGV(FACTORY_TAG, "通道%d电量: 原始值=%u, 计算值=%.2f", i+1, data.channels[i].energy_raw, value);
    }
  }

  // 发布总和传感器数据
  if (power_sum_sensor_) {
    float value = convert_raw_to_value(BL0906_WATT_SUM, data.power_sum_raw);
    power_sum_sensor_->publish_state(value);
    ESP_LOGV(FACTORY_TAG, "总功率: 原始值=%d, 计算值=%.2f", data.power_sum_raw, value);
  }
  
  if (energy_sum_sensor_) {
    float value = convert_raw_to_value(BL0906_CF_SUM_CNT, data.energy_sum_raw);
    energy_sum_sensor_->publish_state(value);
    ESP_LOGV(FACTORY_TAG, "总电量: 原始值=%u, 计算值=%.2f", data.energy_sum_raw, value);
  }
}

// 辅助方法：检查通道是否有效
bool BL0906Factory::is_valid_channel(int channel) const {
  return channel >= 0 && channel < CHANNEL_COUNT;
}





// 简化的update方法
void BL0906Factory::update() {
  // 重置状态机到IDLE状态，开始新的数据读取周期
  this->current_state_ = State::IDLE;

  // 检查统计周期变化（不更新传感器）
  if (energy_statistics_enabled_ && energy_stats_manager_) {
    energy_stats_manager_->check_period_changes();
  }

  // 定期更新所有 total_energy 传感器
  if (energy_persistence_enabled_) {
    // 更新各通道的累计电量传感器
    for (int i = 0; i < CHANNEL_COUNT; i++) {
      if (total_energy_sensors_[i] != nullptr) {
        float total_energy = calculate_total_energy_from_cf_count(i);
        total_energy_sensors_[i]->publish_state(total_energy);
        ESP_LOGV(FACTORY_TAG, "定期更新通道%d累计电量: %.6f kWh", i+1, total_energy);
      }
    }
    
    // 更新总累计电量传感器
    if (total_energy_sum_sensor_ != nullptr) {
      float total_energy_sum = calculate_total_energy_from_cf_count(6);
      total_energy_sum_sensor_->publish_state(total_energy_sum);
      ESP_LOGV(FACTORY_TAG, "定期更新总累计电量: %.6f kWh", total_energy_sum);
    }
  }
}

size_t BL0906Factory::enqueue_action_(ActionCallbackFuncPtr function) {
  this->action_queue_.push_back(function);
  return this->action_queue_.size();
}

void BL0906Factory::handle_actions_() {
  if (this->action_queue_.empty()) {
    return;
  }
  ActionCallbackFuncPtr ptr_func = nullptr;
  for (int i = 0; i < this->action_queue_.size(); i++) {
    ptr_func = this->action_queue_[i];
    if (ptr_func) {
      ESP_LOGI(FACTORY_TAG, "HandleActionCallback[%d]...", i);
      (this->*ptr_func)();
    }
  }

  this->action_queue_.clear();
}

// 现代化校准寄存器读取方法
void BL0906Factory::read_calib_register(CalibNumberType type, int channel) {
  uint8_t reg_addr = get_register_address(static_cast<CalibRegType>(type), channel);
  if (reg_addr == 0) {
    ESP_LOGW(FACTORY_TAG, "无效的校准寄存器类型或通道: type=%d, channel=%d", 
             static_cast<int>(type), channel);
    return;
  }

  ESP_LOGD(FACTORY_TAG, "读取校准寄存器: type=%d, channel=%d, 地址=0x%02X", 
           static_cast<int>(type), channel, reg_addr);
  
  int32_t value = read_register_value(reg_addr);
  ESP_LOGI(FACTORY_TAG, "校准寄存器 0x%02X 读取完成: %d", reg_addr, value);

  // 更新对应的Number组件
  number::Number* num = get_calib_number(type, channel);
  if (num) {
    num->publish_state(value);
    ESP_LOGD(FACTORY_TAG, "已更新Number组件状态: 0x%02X -> %d", reg_addr, value);
  } else {
    ESP_LOGW(FACTORY_TAG, "未找到对应的Number组件: type=%d, channel=%d", 
             static_cast<int>(type), channel);
  }
}



#ifndef USE_BL0906_FACTORY_SPI
// 旧的wait_until_available方法已被适配器替代

// 实现BL0906Factory类的refresh_all_calib_numbers方法
void BL0906Factory::refresh_all_calib_numbers() {
  ESP_LOGI(FACTORY_TAG, "刷新所有校准数字组件...");
  ESP_LOGI(FACTORY_TAG, "已注册的校准数字组件数量: %d", calib_numbers_.size());

  for (size_t i = 0; i < calib_numbers_.size(); i++) {
    auto *number = calib_numbers_[i];
    if (number != nullptr) {
      ESP_LOGD(FACTORY_TAG, "刷新第 %d 个校准数字组件: %p (寄存器: 0x%02X)",
               i, number, number->get_register_address());

      // 检查父指针是否正确设置
      if (!number->has_parent()) {
        ESP_LOGE(FACTORY_TAG, "第 %d 个校准数字组件的父指针为空！", i);
        continue;
      }

      number->update_from_register();
      // 防止看门狗复位
      arch_feed_wdt();
    } else {
      ESP_LOGW(FACTORY_TAG, "第 %d 个校准数字组件指针为空", i);
    }
  }

  ESP_LOGI(FACTORY_TAG, "所有校准数字组件刷新完成");
}

// 旧的flush_rx_buffer方法已被适配器替代



// 修改read_register_value方法使用适配器
int32_t BL0906Factory::read_register_value(uint8_t address) {
  ESP_LOGI(FACTORY_TAG, "读取寄存器0x%02X的值", address);

  if (!comm_adapter_) {
    ESP_LOGE(FACTORY_TAG, "通信适配器未设置");
    return 0;
  }

  // 使用适配器读取数据
  // 适配器已经根据寄存器类型正确处理了数据格式（16位/24位，有符号/无符号）
  // 这里直接返回适配器处理后的结果，避免重复处理
  bool read_success = false;
  int32_t result = comm_adapter_->read_register(address, &read_success);
  
  if (!read_success) {
    ESP_LOGE(FACTORY_TAG, "读取寄存器0x%02X失败", address);
    return 0;  // 读取失败时返回0
  }

  ESP_LOGV(FACTORY_TAG, "寄存器0x%02X读取成功: %d", address, result);
  return result;
}

// 修改write_register_value方法使用适配器
bool BL0906Factory::write_register_value(uint8_t reg, int16_t value) {
  if (!comm_adapter_) {
    ESP_LOGE(FACTORY_TAG, "通信适配器未设置");
    return false;
  }
  
  // 在写入前解除写保护
  if (!this->turn_off_write_protect()) {
    ESP_LOGE(FACTORY_TAG, "无法解除写保护，写入寄存器失败");
    return false;
  }

  ESP_LOGI(FACTORY_TAG, "写保护已解除，继续写入寄存器 0x%02X", reg);
  
  return comm_adapter_->write_register(reg, value);
}

// 修改turn_off_write_protect方法，使用适配器
bool BL0906Factory::turn_off_write_protect() {
  ESP_LOGI(FACTORY_TAG, "开始执行关闭写保护操作");

  if (!comm_adapter_) {
    ESP_LOGE(FACTORY_TAG, "通信适配器未设置");
    return false;
  }

  // 写保护状态寄存器地址
  const uint8_t WR_PROTECT_REG = 0x9E;

  // 检查当前写保护状态
  bool read_success = false;
  int32_t raw_value = comm_adapter_->read_register(WR_PROTECT_REG, &read_success);
  if (!read_success) {
    ESP_LOGE(FACTORY_TAG, "读取写保护状态失败");
    return false;
  }

  // 分析写保护状态
  uint8_t low_byte = raw_value & 0xFF;
  uint8_t mid_byte = (raw_value >> 8) & 0xFF;
  uint16_t reg_16bit = (mid_byte << 8) | low_byte;  // 组合成16位值
  bool is_unlocked = (reg_16bit == 0x5555);  // 修正：检查16位值是否为0x5555
  
  ESP_LOGI(FACTORY_TAG, "写保护状态寄存器值: 0x%06X, 16位值: 0x%04X, 状态: %s", 
           raw_value & 0xFFFFFF, reg_16bit, is_unlocked ? "已解除" : "启用中");

  if (is_unlocked) {
    ESP_LOGI(FACTORY_TAG, "写保护已经是关闭状态，无需操作");
    return true;
  }

  ESP_LOGI(FACTORY_TAG, "开始解除寄存器写保护");
  bool success = false;

  // 重复尝试解锁，最多3次
  for (int attempt = 0; attempt < 3; attempt++) {
    // 发送写保护解锁命令 - 通过适配器发送原始命令
    ESP_LOGI(FACTORY_TAG, "发送写保护解锁命令 (尝试 %d)", attempt + 1);  // 改为LOGI确保能看到
    
    // 添加适配器状态检查
    if (!comm_adapter_) {
      ESP_LOGE(FACTORY_TAG, "通信适配器为空！");
      break;
    }
    
    std::string adapter_type = comm_adapter_->get_adapter_type();
    ESP_LOGI(FACTORY_TAG, "适配器类型: '%s'", adapter_type.c_str());
    
    // 根据适配器类型发送对应的解锁命令
    bool cmd_success = false;
    if (adapter_type == "UART") {
      // UART写保护解除命令
      uint8_t uart_unlock_cmd[6] = {0xCA, 0x9E, 0x55, 0x55, 0x00, 0xB7};
      ESP_LOGI(FACTORY_TAG, "发送UART写保护解锁命令");
      cmd_success = comm_adapter_->send_raw_command(uart_unlock_cmd, 6);
    } else if (adapter_type == "SPI") {
      // SPI写保护解除命令 - 向0x9E寄存器写入0x5555（16位）
      uint8_t spi_unlock_cmd[6] = {0x81, 0x9E, 0x00, 0x55, 0x55, 0x00};
      // 计算正确的校验和
      uint8_t checksum = (spi_unlock_cmd[0] + spi_unlock_cmd[1] + spi_unlock_cmd[2] + 
                         spi_unlock_cmd[3] + spi_unlock_cmd[4]) ^ 0xFF;
      spi_unlock_cmd[5] = checksum;
      
      ESP_LOGI(FACTORY_TAG, "发送SPI写保护解锁命令（写入0x5555到0x9E）: %02X %02X %02X %02X %02X %02X (校验和: %02X)", 
               spi_unlock_cmd[0], spi_unlock_cmd[1], spi_unlock_cmd[2], 
               spi_unlock_cmd[3], spi_unlock_cmd[4], spi_unlock_cmd[5], checksum);
      cmd_success = comm_adapter_->send_raw_command(spi_unlock_cmd, 6);
      ESP_LOGI(FACTORY_TAG, "SPI写保护解锁命令发送结果: %s", cmd_success ? "成功" : "失败");
    } else {
      ESP_LOGE(FACTORY_TAG, "未知的适配器类型: '%s'", adapter_type.c_str());
      break;
    }
    
    ESP_LOGI(FACTORY_TAG, "写保护解锁命令执行结果: %s", cmd_success ? "成功" : "失败");
    
    if (!cmd_success) {
      ESP_LOGW(FACTORY_TAG, "发送写保护解锁命令失败 (尝试 %d)", attempt + 1);
      continue;
    }
    
    // 延时以确保命令被处理
    delay(10);

    // 验证写保护是否成功解除
    read_success = false;
    raw_value = comm_adapter_->read_register(WR_PROTECT_REG, &read_success);
    if (!read_success) {
      ESP_LOGE(FACTORY_TAG, "读取写保护状态失败 (尝试 %d)", attempt + 1);
      continue;
    }

    low_byte = raw_value & 0xFF;
    mid_byte = (raw_value >> 8) & 0xFF;
    reg_16bit = (mid_byte << 8) | low_byte;  // 重新组合16位值
    is_unlocked = (reg_16bit == 0x5555);  // 修正：检查16位值是否为0x5555
    
    ESP_LOGD(FACTORY_TAG, "尝试 %d 后写保护状态: 0x%06X, 16位值: 0x%04X, 状态: %s", 
             attempt + 1, raw_value & 0xFFFFFF, reg_16bit, is_unlocked ? "已解除" : "启用中");
    
    if (is_unlocked) {
      ESP_LOGI(FACTORY_TAG, "写保护已成功解除 (尝试 %d)", attempt + 1);
      success = true;
      break;
    }

    ESP_LOGW(FACTORY_TAG, "写保护解锁尝试 %d 失败，重试...", attempt + 1);
    delay(50); // 在重试前等待更长时间
  }

  if (!success) {
    ESP_LOGE(FACTORY_TAG, "关闭写保护操作失败");
    return false;
  }

  ESP_LOGI(FACTORY_TAG, "写保护关闭操作成功确认");
  return true;
}

// 添加一个辅助函数，判断寄存器是否为16位寄存器
bool BL0906Factory::is_16bit_register(uint8_t address) {
  // CHGN系列: 0xA1-0xA8, 0xAA
  // CHOS系列: 0xAC-0xAF, 0xB2-0xB3, 0xB5
  // RMSGN系列: 0x6D-0x74
  // WATTGN系列: 0xB7-0xBE
  // WATTOS系列: 0xC1-0xC8
  return (address >= 0xA1 && address <= 0xA8) ||  // CHGN 1-6
         (address == 0xAA) ||                     // CHGN_V
         (address >= 0xAC && address <= 0xAF) ||  // CHOS 1-4
         (address >= 0xB2 && address <= 0xB3) ||  // CHOS 5-6
         (address == 0xB5) ||                     // CHOS_V
         (address >= 0x6D && address <= 0x74) ||  // RMSGN 1-6
         (address >= 0xB7 && address <= 0xBE) ||  // WATTGN 1-6
         (address >= 0xC1 && address <= 0xC8);    // WATTOS 1-6
}

// 判断寄存器是否为无符号类型
bool BL0906Factory::is_unsigned_register(uint8_t address) {
  // 电压寄存器
  if (address == BL0906_V_RMS) return true;
  
  // 电流寄存器
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    if (address == BL0906_I_RMS[i]) return true;
  }
  
  // 脉冲计数寄存器
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    if (address == BL0906_CF_CNT[i]) return true;
  }
  if (address == BL0906_CF_SUM_CNT) return true;
  
  // 频率和温度寄存器
  if (address == BL0906_FREQUENCY) return true;
  if (address == BL0906_TEMPERATURE) return true;
  
  return false;  // 其他寄存器为有符号数
}



// 修改setup方法，添加统计功能初始化
void BL0906Factory::setup() {
  ESP_LOGD(FACTORY_TAG, "开始初始化BL0906...");

  // 初始化互斥锁
  this->init_mutex();

  // 初始化通信适配器
  if (!comm_adapter_) {
    ESP_LOGE(FACTORY_TAG, "通信适配器未设置！请检查配置");
    this->mark_failed();
    return;
  }

  if (!comm_adapter_->initialize()) {
    ESP_LOGE(FACTORY_TAG, "通信适配器初始化失败: %s", comm_adapter_->get_last_error().c_str());
    this->mark_failed();
    return;
  }

  ESP_LOGI(FACTORY_TAG, "通信适配器初始化成功: %s", comm_adapter_->get_adapter_type().c_str());

  // 执行适配器自检
  if (!comm_adapter_->self_test()) {
    ESP_LOGW(FACTORY_TAG, "通信适配器自检失败: %s", comm_adapter_->get_last_error().c_str());
  } else {
    ESP_LOGI(FACTORY_TAG, "通信适配器自检通过");
  }

  // 检查实例ID是否已设置（现在是必填项）
  if (instance_id_ == 0) {
      ESP_LOGE(FACTORY_TAG, "错误：instance_id未设置！请在YAML配置中设置instance_id");
      return;
  }
  ESP_LOGI(FACTORY_TAG, "使用配置的实例ID: 0x%08X", instance_id_);

  // 初始化新的统一校准存储系统
  if (!init_calibration_storage()) {
      ESP_LOGE(FACTORY_TAG, "校准存储初始化失败");
      this->mark_failed();
      return;
  }
  
  // 加载校准数据
  bool calibration_loaded = load_calibration_data();
  if (!calibration_loaded) {
      ESP_LOGW(FACTORY_TAG, "校准数据加载失败，使用默认值");
      #ifdef BL0906_CALIBRATION_MODE
          // 校准版：尝试使用YAML配置
          apply_calibration_values();
      #else
          // 量产版：如果有YAML配置的校准值，也应该应用
          if (!initial_calibration_values_.empty()) {
              ESP_LOGI(FACTORY_TAG, "量产版本检测到YAML配置的校准值，开始应用");
              apply_calibration_values();
          } else {
              ESP_LOGW(FACTORY_TAG, "量产版本没有校准数据，芯片将使用默认校准值运行");
          }
      #endif
  } else {
      ESP_LOGI(FACTORY_TAG, "成功从存储加载校准数据并应用到芯片");
  }

  // 设置电量持久化存储
  setup_energy_persistence();

  // 初始化电量统计管理器
  if (energy_statistics_enabled_) {
    energy_stats_manager_.reset(new EnergyStatisticsManager(this));
    // EnergyStatisticsManager继承自PollingComponent，会被ESPHome框架自动管理
    // 不需要手动注册为子组件
    energy_stats_manager_->setup();
    ESP_LOGI(FACTORY_TAG, "电量统计管理器初始化完成，更新间隔: 60秒");
  }

  // 测试保存功能是否正常工作
  if (energy_persistence_enabled_) {
    ESP_LOGI(FACTORY_TAG, "测试电量数据保存功能...");
    save_energy_data();  // 立即保存一次，测试功能是否正常
  }

  // 读取校准寄存器
  refresh_all_calib_numbers();
  ESP_LOGI(FACTORY_TAG, "BL0906初始化完成，已注册 %d 个校准数字组件", calib_numbers_.size());
}

// 新增方法：应用校准值
void BL0906Factory::apply_calibration_values() {
  const size_t total_values = initial_calibration_values_.size();

  if (total_values == 0) {
    ESP_LOGI(FACTORY_TAG, "没有校准值需要应用");
    return;
  }

  ESP_LOGI(FACTORY_TAG, "开始应用 %zu 个校准值", total_values);

  // 解除写保护
  if (!this->turn_off_write_protect()) {
    ESP_LOGE(FACTORY_TAG, "无法解除写保护，校准值未应用");
    return;
  }

  // 批量写入所有校准值
  int success_count = 0;
  size_t current = 0;

  for (const auto& pair : initial_calibration_values_) {
    uint8_t reg_addr = pair.first;
    int16_t value = pair.second;
    current++;

    // 每5个值或最后一个值显示进度
    if (current % 5 == 0 || current == total_values) {
      ESP_LOGI(FACTORY_TAG, "应用进度: %zu/%zu (%.1f%%)",
               current, total_values, current * 100.0f / total_values);
    }

    ESP_LOGD(FACTORY_TAG, "写入寄存器 0x%02X 值: %d", reg_addr, value);

    if (this->write_register_value(reg_addr, value)) {
      success_count++;

      // 防止看门狗复位
     arch_feed_wdt();

      // 短暂延时，避免连续写入过快
      delay(2);
    } else {
      // 写入失败时也要喂狗
     arch_feed_wdt();
    }
  }

  ESP_LOGI(FACTORY_TAG, "校准值应用完成：%d 成功，%d 失败",
          success_count, (int)(total_values - success_count));
}

// 实现register_calib_number方法
void BL0906Factory::register_calib_number(BL0906Number *number) {
  number->set_parent(this);  // 设置父指针
  calib_numbers_.push_back(number);
}

// 电量持久化存储相关方法实现

// 设置电量持久化存储（基于CF_count）
void BL0906Factory::setup_energy_persistence() {
  ESP_LOGI(FACTORY_TAG, "初始化电量持久化存储系统（基于CF_count）...");
  ESP_LOGI(FACTORY_TAG, "电量持久化状态: %s", energy_persistence_enabled_ ? "启用" : "禁用");

  if (!energy_persistence_enabled_) {
    ESP_LOGW(FACTORY_TAG, "⚠️ 电量持久化存储已禁用，断电后CF_count数据将丢失");
    return;
  }

  ESP_LOGI(FACTORY_TAG, "创建Preferences对象...");
  ESP_LOGI(FACTORY_TAG, "数据结构大小: %d 字节", sizeof(EnergyPersistenceData));
  ESP_LOGI(FACTORY_TAG, "Hash值: 0x%08X", 0x906CF0FF); // 新的hash值，避免与旧版本冲突

  // 创建preferences对象（使用新的数据结构）
  energy_pref_ = global_preferences->make_preference<EnergyPersistenceData>(
    sizeof(EnergyPersistenceData), 0x906CF0FF); // "bl0906_cf_count" hash

  ESP_LOGI(FACTORY_TAG, "✅ Preferences对象创建成功");

  // 加载已保存的CF_count数据
  load_energy_data();

  ESP_LOGI(FACTORY_TAG, "✅ 电量持久化存储初始化完成");
}

// 加载CF_count数据（修正版本）
void BL0906Factory::load_energy_data() {
  if (!energy_persistence_enabled_) {
    ESP_LOGW(FACTORY_TAG, "电量持久化存储已禁用，无法加载数据");
    return;
  }

  ESP_LOGI(FACTORY_TAG, "开始加载CF_count持久化数据...");

  EnergyPersistenceData data;
  bool load_success = energy_pref_.load(&data);
  
  ESP_LOGI(FACTORY_TAG, "Preferences加载结果: %s", load_success ? "成功" : "失败");
  
  if (load_success) {
    // 更新校验和计算，包含save_count字段
    uint32_t calculated_checksum = 0;
    // 对persistent_cf_count和last_cf_count数组计算校验和
    for (int i = 0; i < 7; i++) {
      calculated_checksum += data.persistent_cf_count[i];
      calculated_checksum += data.last_cf_count[i];
    }
    // 将save_count也加入校验和计算
    calculated_checksum += data.save_count;
    
    ESP_LOGI(FACTORY_TAG, "数据校验: 计算值=0x%08X, 存储值=0x%08X", 
             calculated_checksum, data.checksum);
    ESP_LOGI(FACTORY_TAG, "存储的总持久化CF_count: %u, 存储计数: %u", data.persistent_cf_count[6], data.save_count);
    
    if (calculated_checksum == data.checksum) {
      // 数据有效，恢复CF_count数据和存储计数
      for (int i = 0; i < ARRAY_SIZE; i++) {
        persistent_cf_count_[i] = data.persistent_cf_count[i];
        last_cf_count_[i] = data.last_cf_count[i];
        
        if (i < CHANNEL_COUNT) {
          ESP_LOGI(FACTORY_TAG, "恢复通道%d: 持久化CF_count=%u, 上次硬件CF_count=%u", 
                   i+1, persistent_cf_count_[i], last_cf_count_[i]);
        } else {
          ESP_LOGI(FACTORY_TAG, "恢复总和: 持久化CF_count=%u, 上次硬件CF_count=%u", 
                   persistent_cf_count_[i], last_cf_count_[i]);
        }
      }

      // 恢复存储计数
      current_save_count_ = data.save_count;

      ESP_LOGI(FACTORY_TAG, "✅ 成功加载CF_count数据，存储计数: %u", current_save_count_);
    } else {
      ESP_LOGE(FACTORY_TAG, "❌ CF_count数据校验失败！计算值=0x%08X, 存储值=0x%08X", 
               calculated_checksum, data.checksum);
      ESP_LOGW(FACTORY_TAG, "数据可能已损坏，重置为0并重新开始计算");
      reset_energy_data();
    }
  } else {
    ESP_LOGW(FACTORY_TAG, "⚠️ 未找到已保存的CF_count数据，这可能是首次启动");
    ESP_LOGI(FACTORY_TAG, "初始化CF_count数据，从0开始计算");
    reset_energy_data();
  }
}

// 优化的电量数据保存方法（只在BL0906重启时保存last_cf_count）
void BL0906Factory::save_energy_data() {
  if (!energy_persistence_enabled_) {
    ESP_LOGV(FACTORY_TAG, "电量持久化存储已禁用，跳过保存");
    return;
  }

  // 优化：检查是否有足够的变化值得保存
  static uint32_t last_total_cf_count = 0;
  uint32_t current_total_cf_count = persistent_cf_count_[6];
  
  // 检查是否需要保存：CF_count变化大或者last_cf_count需要保存
  const uint32_t MIN_CHANGE_THRESHOLD = 1000;  // 提高到1000，减少写入频率
  bool cf_count_changed = abs((int32_t)(current_total_cf_count - last_total_cf_count)) >= MIN_CHANGE_THRESHOLD;
  
  if (!cf_count_changed && !last_cf_count_needs_save_) {
    ESP_LOGV(FACTORY_TAG, "CF_count变化较小且last_cf_count无需保存，跳过保存 (当前变化: %d, 阈值: %d)", 
             abs((int32_t)(current_total_cf_count - last_total_cf_count)), MIN_CHANGE_THRESHOLD);
    return;
  }

  // 每次触发持久存储时，存储计数器+1
  current_save_count_++;

  ESP_LOGD(FACTORY_TAG, "开始保存CF_count数据到Flash... (CF_count变化: %s, last_cf_count需要保存: %s, 存储计数: %u)",
           cf_count_changed ? "是" : "否", last_cf_count_needs_save_ ? "是" : "否", current_save_count_);

  // 准备保存数据结构
  EnergyPersistenceData data;
  
  // 总是复制persistent_cf_count数组
  memcpy(data.persistent_cf_count, persistent_cf_count_, sizeof(persistent_cf_count_));
  
  // 只在需要时复制last_cf_count数组
  if (last_cf_count_needs_save_) {
    memcpy(data.last_cf_count, last_cf_count_, sizeof(last_cf_count_));
    ESP_LOGI(FACTORY_TAG, "保存last_cf_count数据（BL0906重启检测）");
  } else {
    // 从现有数据加载last_cf_count，避免覆盖
    EnergyPersistenceData existing_data;
    if (energy_pref_.load(&existing_data)) {
      memcpy(data.last_cf_count, existing_data.last_cf_count, sizeof(data.last_cf_count));
    } else {
      // 如果没有现有数据，使用当前值
      memcpy(data.last_cf_count, last_cf_count_, sizeof(last_cf_count_));
    }
  }
  
  // 设置存储计数
  data.save_count = current_save_count_;
  
  // 更新校验和计算，包含save_count字段
  data.checksum = 0;
  // 对persistent_cf_count和last_cf_count数组计算校验和
  for (int i = 0; i < 7; i++) {
    data.checksum += data.persistent_cf_count[i];
    data.checksum += data.last_cf_count[i];
  }
  // 将save_count也加入校验和计算
  data.checksum += data.save_count;

  // 保存到flash
  bool save_success = energy_pref_.save(&data);
  
  if (save_success) {
    last_total_cf_count = current_total_cf_count;  // 更新基准值
    last_cf_count_needs_save_ = false;  // 重置标志
    ESP_LOGI(FACTORY_TAG, "✅ CF_count数据保存成功，总持久化CF_count: %u, 存储计数: %u", persistent_cf_count_[6], current_save_count_);
  } else {
    ESP_LOGE(FACTORY_TAG, "❌ 保存CF_count数据失败！Flash可能已满或损坏");
    // 保存失败时回退存储计数器
    current_save_count_--;
  }

  last_save_time_ = millis();
}

// 重置CF_count数据（修正版本）
void BL0906Factory::reset_energy_data() {
  for (int i = 0; i < ARRAY_SIZE; i++) {
    persistent_cf_count_[i] = 0;
    last_cf_count_[i] = 0;
  }

  ESP_LOGI(FACTORY_TAG, "CF_count数据已重置，脉冲计数将重新初始化，存储计数已重置为0");

  // 立即保存重置后的数据
  save_energy_data();
}

// 统计功能相关方法实现
void BL0906Factory::set_energy_statistics_enabled(bool enabled) {
  energy_statistics_enabled_ = enabled;
  ESP_LOGI(FACTORY_TAG, "电量统计功能%s", enabled ? "已启用" : "已禁用");
}

void BL0906Factory::set_time_component(esphome_time::RealTimeClock *time_comp) {
  if (energy_stats_manager_) {
    energy_stats_manager_->set_time_component(time_comp);
  }
}

void BL0906Factory::set_statistics_sensor(StatisticsSensorType type, sensor::Sensor *sensor, int channel) {
  if (energy_stats_manager_) {
    energy_stats_manager_->set_sensor(type, sensor, channel);
  }
}

void BL0906Factory::set_statistics_update_interval(uint32_t update_interval_ms) {
  if (energy_stats_manager_) {
    energy_stats_manager_->set_update_interval(update_interval_ms);
    ESP_LOGI(FACTORY_TAG, "设置统计传感器更新间隔: %u毫秒", update_interval_ms);
  }
}

float BL0906Factory::calculate_total_energy_from_cf_count(int channel) const {
  if (channel < 0 || channel >= ARRAY_SIZE) {
    return 0.0f;
  }
  return persistent_cf_count_[channel] / Ke;  // 将CF_count转换为电量(kWh)
}

// 诊断方法实现
void BL0906Factory::diagnose_energy_persistence() {
  ESP_LOGI(FACTORY_TAG, "=== 电量持久化诊断信息（基于CF_count）===");
  ESP_LOGI(FACTORY_TAG, "持久化状态: %s", energy_persistence_enabled_ ? "启用" : "禁用");
  ESP_LOGI(FACTORY_TAG, "统计功能状态: %s", energy_statistics_enabled_ ? "启用" : "禁用");
  ESP_LOGI(FACTORY_TAG, "芯片重启检测状态: %s", chip_restart_detected_ ? "已检测到重启" : "正常运行");
  if (chip_restart_detected_) {
    ESP_LOGI(FACTORY_TAG, "上次重启检测时间: %u ms", last_restart_detection_time_);
    ESP_LOGI(FACTORY_TAG, "距离上次重启检测: %u ms", millis() - last_restart_detection_time_);
  }
  ESP_LOGI(FACTORY_TAG, "芯片重启次数: %u", chip_restart_count_);
  ESP_LOGI(FACTORY_TAG, "上次保存时间: %u ms", last_save_time_);
  ESP_LOGI(FACTORY_TAG, "距离上次保存: %u ms", millis() - last_save_time_);
  ESP_LOGI(FACTORY_TAG, "存储计数: %u 次", current_save_count_);
  ESP_LOGI(FACTORY_TAG, "数据结构大小: %d 字节", sizeof(EnergyPersistenceData));
  
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    float channel_energy = calculate_total_energy_from_cf_count(i);
    // 内联get_persistent_cf_count的代码
    uint32_t persistent_cf_count = (i >= 0 && i < CHANNEL_COUNT) ? persistent_cf_count_[i] : 0;
    ESP_LOGI(FACTORY_TAG, "通道%d: 持久化CF_count=%u, 电量=%.6f kWh, 硬件CF_count=%u, 初始化=%s", 
             i+1, persistent_cf_count, channel_energy, last_cf_count_[i]);
  }
  
  float total_energy = calculate_total_energy_from_cf_count(6);
  // 内联get_persistent_cf_count_sum的代码
  uint32_t persistent_cf_count_sum = persistent_cf_count_[6];
  ESP_LOGI(FACTORY_TAG, "总和: 持久化CF_count=%u, 电量=%.6f kWh, 硬件CF_count=%u, 初始化=%s", 
           persistent_cf_count_sum, total_energy, last_cf_count_[6]);
  ESP_LOGI(FACTORY_TAG, "========================================");
}

// 设置电量持久化开关
void BL0906Factory::set_energy_persistence_enabled(bool enabled) {
  energy_persistence_enabled_ = enabled;
  ESP_LOGI(FACTORY_TAG, "电量持久化存储%s", enabled ? "已启用" : "已禁用");
}

// 新增方法：检查并同步硬件CF_count（如果持久化CF_count小于硬件CF_count，则用硬件CF_count覆盖）
void BL0906Factory::check_and_sync_hardware_cf_count() {
  ESP_LOGI(FACTORY_TAG, "开始检查并同步硬件CF_count...");

  bool data_changed = false;  // 标记是否有数据变化

  // 通信适配器会自动处理缓冲区管理

  // 读取各通道的硬件CF_count
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    bool read_success = false;
    int32_t raw_value = send_read_command_and_receive(BL0906_CF_CNT[i], &read_success);
    if (!read_success) {
      ESP_LOGE(FACTORY_TAG, "读取通道%d硬件CF_count失败", i+1);
      continue;
    }

    uint32_t hardware_cf_count = static_cast<uint32_t>(raw_value);
    ESP_LOGD(FACTORY_TAG, "通道%d硬件CF_count: %u, 持久化CF_count: %u", 
             i+1, hardware_cf_count, persistent_cf_count_[i]);

    // 如果持久化CF_count小于硬件CF_count，则用硬件CF_count覆盖
    if (persistent_cf_count_[i] < hardware_cf_count) {
      ESP_LOGI(FACTORY_TAG, "通道%d: 持久化CF_count=%u < 硬件CF_count=%u，用硬件CF_count覆盖",
               i+1, persistent_cf_count_[i], hardware_cf_count);
      persistent_cf_count_[i] = hardware_cf_count;
      last_cf_count_[i] = hardware_cf_count;  // 同时更新基准值
      data_changed = true;
    } else {
      ESP_LOGD(FACTORY_TAG, "通道%d: 持久化CF_count=%u >= 硬件CF_count=%u，保持持久化数据",
               i+1, persistent_cf_count_[i], hardware_cf_count);
    }

    // 短暂延时，避免连续读取过快
    delay(5);
  }

  // 读取总和的硬件CF_count
  bool sum_read_success = false;
  int32_t sum_raw_value = send_read_command_and_receive(BL0906_CF_SUM_CNT, &sum_read_success);
  if (sum_read_success) {
    uint32_t hardware_cf_sum = static_cast<uint32_t>(sum_raw_value);
    ESP_LOGD(FACTORY_TAG, "总和硬件CF_count: %u, 持久化CF_count: %u", 
             hardware_cf_sum, persistent_cf_count_[6]);

    // 如果持久化CF_count小于硬件CF_count，则用硬件CF_count覆盖
    if (persistent_cf_count_[6] < hardware_cf_sum) {
      ESP_LOGI(FACTORY_TAG, "总和: 持久化CF_count=%u < 硬件CF_count=%u，用硬件CF_count覆盖",
               persistent_cf_count_[6], hardware_cf_sum);
      persistent_cf_count_[6] = hardware_cf_sum;
      last_cf_count_[6] = hardware_cf_sum;  // 同时更新基准值
      data_changed = true;
    } else {
      ESP_LOGD(FACTORY_TAG, "总和: 持久化CF_count=%u >= 硬件CF_count=%u，保持持久化数据",
               persistent_cf_count_[6], hardware_cf_sum);
    }
  } else {
    ESP_LOGE(FACTORY_TAG, "读取总和硬件CF_count失败");
  }

  // 如果有数据变化，立即保存
  if (data_changed) {
    ESP_LOGI(FACTORY_TAG, "检测到数据变化，立即保存持久化数据");
    save_energy_data();
  }

  ESP_LOGI(FACTORY_TAG, "硬件CF_count同步完成");
}

// RMSOS自动计算函数（合并简化版本）
void BL0906Factory::calculate_and_write_rmsos_all_channels() {
  ESP_LOGI(FACTORY_TAG, "开始RMSOS自动计算...");
  
  // 检查组件状态
  if (!this->is_ready()) {
    ESP_LOGE(FACTORY_TAG, "BL0906组件未就绪，无法执行RMSOS计算");
    return;
  }
  
  // 检查数据读取完整性
  if (!current_data_.read_complete) {
    ESP_LOGW(FACTORY_TAG, "数据读取未完成，使用当前可用数据进行计算");
  }
  
  // 2. 使用主循环已读取的电流原始值进行RMSOS计算
  bool success = true;
  ESP_LOGI(FACTORY_TAG, "使用主循环数据(时间戳: %u, 数据完整性: %s)", 
           current_data_.timestamp, current_data_.read_complete ? "完整" : "部分");
  
  for (int channel = 1; channel <= 6; channel++) {
    // 直接使用已读取的电流原始值（现在是uint32_t类型）
    uint32_t current_raw = current_data_.channels[channel - 1].current_raw;
    
    // 检查数据有效性
    if (current_raw == 0) {
      ESP_LOGW(FACTORY_TAG, "通道%d电流原始值为0，可能表示无负载或读取异常", channel);
    }
    
    // 内联计算RMSOS值：RMSOS[n] = (-pow(current_raw[n], 2) / 256) * 0.787
    double current_squared = pow(static_cast<double>(current_raw), 2);
    double result = (-current_squared / 256.0)* 0.787;
    
    // 限制在16位有符号整数范围内
    if (result > 32767) result = 32767;
    if (result < -32768) result = -32768;
    int16_t rmsos_value = static_cast<int16_t>(result);
    
    ESP_LOGD(FACTORY_TAG, "通道%d计算: current_raw=%u, result=%.2f, rmsos_value=%d", 
             channel, current_raw, result, rmsos_value);
    
    // 内联写入RMSOS寄存器
    uint8_t address = BL0906_RMSOS[channel - 1]; // 通道1-6对应数组索引0-5
    if (!write_register_value(address, rmsos_value)) {
      ESP_LOGE(FACTORY_TAG, "写入通道%d RMSOS寄存器(0x%02X)失败", channel, address);
      success = false;
    } else {
      ESP_LOGI(FACTORY_TAG, "通道%d: 电流原始值=%u, RMSOS值=%d, 写入成功", 
               channel, current_raw, rmsos_value);
    }
    
    // 短暂延时避免连续写入过快
    delay(10);
  }
  
  // 3. 恢复写保护（如果原来是开启的）
  // 注意：这里可能需要根据实际情况决定是否重新开启写保护
  
  if (success) {
    ESP_LOGI(FACTORY_TAG, "RMSOS自动计算完成，所有通道处理成功");
    // 刷新所有校准数字组件显示
    refresh_all_calib_numbers();
  } else {
    ESP_LOGW(FACTORY_TAG, "RMSOS自动计算完成，但部分通道处理失败");
  }
}

// 从存储加载校准值（兼容旧接口）
void BL0906Factory::load_calibration_from_flash() {
    // 使用新的统一存储接口
    load_calibration_data();
}

#ifdef BL0906_CALIBRATION_MODE
// 批量保存所有校准值到存储（由Button组件手动调用）
void BL0906Factory::save_all_calibration_to_flash() {
    ESP_LOGI(FACTORY_TAG, "开始手动保存校准数据到存储...");
    
    // 使用新的统一存储接口
    if (save_calibration_data()) {
        ESP_LOGI(FACTORY_TAG, "校准数据手动保存完成");
    } else {
        ESP_LOGE(FACTORY_TAG, "手动保存校准数据失败");
    }
}
#endif

// 实例ID相关方法实现
void BL0906Factory::set_instance_id(uint32_t id) {
    instance_id_ = id;
    ESP_LOGI(FACTORY_TAG, "设置实例ID: 0x%08X", instance_id_);
    
    // 如果校准存储已初始化，更新其实例ID
    #ifdef USE_I2C_EEPROM_CALIBRATION
    if (calibration_storage_) {
        // 对于I2C EEPROM存储，可能需要设置实例ID
        // 这里暂时不做处理，因为preference存储不需要额外的实例ID设置
        ESP_LOGD(FACTORY_TAG, "校准存储已初始化，实例ID已更新");
    }
    #endif
}

uint32_t BL0906Factory::get_instance_id() const {
    return instance_id_;
}

uint32_t BL0906Factory::generate_instance_id() const {
    // 不再自动生成ID，instance_id现在是必填项
    // 如果没有设置instance_id，返回默认值并记录警告
    if (instance_id_ == 0) {
        ESP_LOGW(FACTORY_TAG, "instance_id未设置，使用默认值0x906B0001");
        return 0x906B0001;  // 默认ID
    }
    
    return instance_id_;
}

// 新的统一存储系统实现
bool BL0906Factory::init_calibration_storage() {
    if (storage_type_ == "preference") {
        ESP_LOGI(FACTORY_TAG, "初始化preference校准存储");
        calibration_storage_.reset(new PreferenceCalibrationStorage());
    } 
#ifdef USE_I2C_EEPROM_CALIBRATION
    else if (storage_type_ == "eeprom") {
        ESP_LOGI(FACTORY_TAG, "初始化I2C EEPROM校准存储 (型号: %02X, 地址: 0x%02X)", 
                 static_cast<uint8_t>(eeprom_type_), i2c_address_);
        calibration_storage_.reset(new I2CEEPROMCalibrationStorage(
            i2c_parent_, eeprom_type_, i2c_address_));
    }
#endif
    else {
        ESP_LOGE(FACTORY_TAG, "未知的存储类型: %s", storage_type_.c_str());
        return false;
    }
    
    if (!calibration_storage_) {
        ESP_LOGE(FACTORY_TAG, "创建校准存储对象失败");
        return false;
    }
    
    if (!calibration_storage_->init()) {
        ESP_LOGE(FACTORY_TAG, "校准存储初始化失败");
        return false;
    }
    
    ESP_LOGI(FACTORY_TAG, "校准存储初始化成功 (类型: %s)", storage_type_.c_str());
    return true;
}

bool BL0906Factory::load_calibration_data() {
    if (!calibration_storage_) {
        ESP_LOGE(FACTORY_TAG, "校准存储未初始化");
        return false;
    }
    
    std::vector<CalibrationEntry> entries;
    if (!calibration_storage_->read_instance(instance_id_, entries)) {
        ESP_LOGD(FACTORY_TAG, "实例 0x%08X 的校准数据不存在", instance_id_);
        return false;
    }
    
    ESP_LOGI(FACTORY_TAG, "从存储加载 %d 个校准值", entries.size());
    
    // 解除写保护
    if (!this->turn_off_write_protect()) {
        ESP_LOGE(FACTORY_TAG, "无法解除写保护");
        return false;
    }
    
    // 应用校准值到芯片
    int success_count = 0;
    for (const auto& entry : entries) {
        if (this->write_register_value(entry.register_addr, entry.value)) {
            ESP_LOGD(FACTORY_TAG, "应用校准值: 寄存器0x%02X = %d", 
                     entry.register_addr, entry.value);
            success_count++;
        }
        
        // 防止看门狗复位
       arch_feed_wdt();
        delay(2); // 短暂延时
    }
    
    ESP_LOGI(FACTORY_TAG, "校准值应用完成：%d 成功，%d 失败", 
             success_count, (int)(entries.size() - success_count));
    
    return success_count > 0;
}

bool BL0906Factory::save_calibration_data() {
    if (!calibration_storage_) {
        ESP_LOGE(FACTORY_TAG, "校准存储未初始化");
        return false;
    }
    
#ifdef BL0906_CALIBRATION_MODE
    std::vector<CalibrationEntry> entries;
    
    // 从所有已注册的Number组件收集当前值
    for (auto* number : calib_numbers_) {
        if (number != nullptr) {
            CalibrationEntry entry;
            entry.register_addr = number->get_register_address();
            entry.value = static_cast<int16_t>(number->state);
            entries.push_back(entry);
            
            ESP_LOGD(FACTORY_TAG, "收集校准值: 寄存器0x%02X = %d", 
                     entry.register_addr, entry.value);
        }
    }
    
    if (entries.empty()) {
        ESP_LOGW(FACTORY_TAG, "没有校准值需要保存");
        return false;
    }
    
    if (calibration_storage_->write_instance(instance_id_, entries)) {
        ESP_LOGI(FACTORY_TAG, "成功保存 %d 个校准值", entries.size());
        return true;
    } else {
        ESP_LOGE(FACTORY_TAG, "保存校准数据失败");
        return false;
    }
#else
    ESP_LOGW(FACTORY_TAG, "量产版本不支持保存校准数据");
    return false;
#endif
}

// 旧的SPI方法已被通信适配器替代，不再需要

#endif  // USE_BL0906_FACTORY_SPI

}  // namespace bl0906_factory
}  // namespace esphome
