# BL0906 Factory 时间跨越测试指南

## 概述

时间跨越测试是验证电量统计功能正确性的关键测试，用于确保在日期、周、月、年变化时，电量统计数据能够正确重置和累计。

## 测试方法

### 1. 快速手动测试（推荐）

使用 `test_time_crossing_fast.yaml` 配置文件：

```bash
# 编译和上传
esphome compile test_time_crossing_fast.yaml
esphome upload test_time_crossing_fast.yaml

# 监控日志
esphome logs test_time_crossing_fast.yaml
```

**测试步骤**：
1. 设备启动后，观察初始电量统计数据
2. 通过Home Assistant或Web界面点击测试按钮：
   - "模拟跨日测试" - 测试日期跨越
   - "模拟跨周测试" - 测试周跨越  
   - "模拟跨月测试" - 测试月跨越
   - "模拟跨年测试" - 测试年跨越
3. 观察日志输出和传感器数值变化

### 2. 自动化测试

使用 `test_time_crossing_auto.yaml` 配置文件：

```bash
# 编译和上传
esphome compile test_time_crossing_auto.yaml
esphome upload test_time_crossing_auto.yaml

# 监控日志
esphome logs test_time_crossing_auto.yaml
```

**自动测试流程**：
- 每30秒自动执行一个测试阶段
- 自动循环：跨日→跨周→跨月→跨年→重置→重新开始
- 可通过按钮手动控制测试进程

### 3. 真实时间测试

对于长期验证，可以设置设备在真实环境中运行，等待真实的时间跨越：

```yaml
# 使用真实时间的配置
time:
  - platform: sntp
    id: sntp_time
    timezone: Asia/Shanghai
    # 在真实时间跨越时触发
    on_time:
      - seconds: 0
        minutes: 0
        hours: 0
        then:
          - logger.log: "检测到真实跨日事件"
```

## 测试验证点

### 1. 跨日测试验证

**预期行为**：
- 昨日电量 = 之前的今日电量
- 今日电量重置为0（从当前时刻开始计算）
- 其他周期统计保持不变

**日志关键信息**：
```
[energy_statistics] 检测到新日: 2024-01-15 -> 2024-01-16
[energy_statistics] 处理新的一天
[energy_statistics] 新日统计数据更新完成
```

### 2. 跨周测试验证

**预期行为**：
- 本周电量重置为0
- 日统计保持正常运行
- 月、年统计不受影响

**日志关键信息**：
```
[energy_statistics] 检测到新周
[energy_statistics] 处理新的一周
[energy_statistics] 新周统计数据更新完成
```

### 3. 跨月测试验证

**预期行为**：
- 本月电量重置为0
- 日、周统计正常运行
- 年统计不受影响

### 4. 跨年测试验证

**预期行为**：
- 本年电量重置为0
- 所有其他周期统计正常运行

## 测试配置说明

### 关键配置参数

```yaml
bl0906_factory:
  update_interval: 5s    # 快速更新便于测试
  energy_persistence: true
  energy_statistics: true
  time_id: sntp_time

logger:
  level: DEBUG           # 启用详细日志
  logs:
    bl0906_factory: DEBUG
    energy_statistics: DEBUG
```

### 测试传感器配置

```yaml
sensor:
  - platform: bl0906_factory
    # 基础传感器
    current_1:
      name: "通道1电流"
    
    # 统计传感器（关键测试对象）
    today_energy_1:
      name: "通道1今日电量"
      on_value:
        then:
          - logger.log: 
              format: "今日电量更新: %.3f kWh"
              args: ['x']
    
    yesterday_energy_1:
      name: "通道1昨日电量"
      on_value:
        then:
          - logger.log: 
              format: "昨日电量更新: %.3f kWh"
              args: ['x']
```

## 常见问题排查

### 1. 时间跨越未触发

**可能原因**：
- 时间组件未正确配置
- 网络时间同步失败
- `check_period_changes()` 调用频率不足

**解决方法**：
```yaml
# 确保时间组件正确配置
time:
  - platform: sntp
    id: sntp_time
    timezone: Asia/Shanghai
    servers:
      - "pool.ntp.org"
      - "time.nist.gov"
```

### 2. 统计数据未重置

**可能原因**：
- 统计管理器未正确初始化
- 持久化存储问题
- 线程安全问题

**调试方法**：
```bash
# 查看详细日志
esphome logs test_time_crossing_fast.yaml --verbose
```

### 3. 传感器数值异常

**可能原因**：
- 传感器注册问题
- 计算公式错误
- 数据类型转换问题

**验证方法**：
- 检查传感器注册日志
- 验证CF_count数据
- 对比手动计算结果

## 性能测试

### 1. 内存使用测试

```yaml
sensor:
  - platform: template
    name: "Free Heap"
    lambda: |-
      return ESP.getFreeHeap();
    update_interval: 10s
```

### 2. 响应时间测试

```yaml
button:
  - platform: template
    name: "响应时间测试"
    on_press:
      then:
        - lambda: |-
            unsigned long start = millis();
            auto bl0906 = id(bl0906_device);
            bl0906->force_check_period_changes();
            unsigned long end = millis();
            ESP_LOGI("perf", "时间跨越处理耗时: %lu ms", end - start);
```

## 测试报告模板

### 测试环境
- ESPHome版本：
- 硬件平台：ESP32
- 测试配置：test_time_crossing_fast.yaml
- 测试时间：

### 测试结果

| 测试项目 | 预期结果 | 实际结果 | 状态 |
|---------|---------|---------|------|
| 跨日测试 | 昨日电量更新，今日电量重置 | | ✅/❌ |
| 跨周测试 | 本周电量重置 | | ✅/❌ |
| 跨月测试 | 本月电量重置 | | ✅/❌ |
| 跨年测试 | 本年电量重置 | | ✅/❌ |
| 数据持久化 | 重启后数据保持 | | ✅/❌ |
| 性能表现 | 响应时间<100ms | | ✅/❌ |

### 问题记录
- 问题描述：
- 复现步骤：
- 解决方案：

## 最佳实践

1. **测试前准备**：
   - 确保网络连接正常
   - 验证时间同步成功
   - 清理之前的测试数据

2. **测试过程**：
   - 逐步执行，不要同时触发多个测试
   - 观察日志输出，记录异常信息
   - 验证传感器数值变化

3. **测试后验证**：
   - 检查数据持久化是否正常
   - 验证设备重启后的数据恢复
   - 确认长期运行的稳定性

4. **自动化集成**：
   - 将测试集成到CI/CD流程
   - 定期执行回归测试
   - 建立测试数据基线 