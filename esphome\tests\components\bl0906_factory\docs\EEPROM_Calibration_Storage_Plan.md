# BL0906 EEPROM校准数据存储方案

## 概述
使用ESPHome自带的EEPROM模拟功能来存储BL0906的校准数据，避免自定义分区表的复杂配置。

## 背景
- 当前问题：ESPHome难以正确识别和使用自定义分区表
- ESPHome默认分区表中包含EEPROM分区：`eeprom, data, 0x99, 0x390000, 0x1000`（4KB）
- EEPROM分区足够存储校准数据（每个校准条目4字节，最多128个条目 = 512字节）

## EEPROM分区特性
- **位置**：0x390000
- **大小**：4096字节（0x1000）
- **类型**：data
- **子类型**：0x99
- **访问方式**：通过ESP32的`nvs_flash` API或Arduino的`EEPROM`库

## 存储结构设计

### 1. EEPROM布局
```
偏移量    大小    描述
0x0000    4       魔数 (0xB0906CAL)
0x0004    2       版本号 (0x0001)
0x0006    2       条目数量
0x0008    4       CRC32校验和
0x000C    4       时间戳
0x0010    16      保留
0x0020    512     校准数据区（最多128个条目，每个4字节）
0x0220    3552    剩余空间（未使用）
```

### 2. 数据结构
```cpp
// EEPROM头部结构（32字节）
struct EEPROMHeader {
    uint32_t magic;           // 0xB0906CAL
    uint16_t version;         // 版本号
    uint16_t entry_count;     // 条目数量
    uint32_t crc32;          // 数据区CRC32
    uint32_t timestamp;       // Unix时间戳
    uint8_t reserved[16];     // 保留
} __attribute__((packed));

// 校准条目结构（4字节）
struct CalibrationEntry {
    uint8_t register_addr;    // 寄存器地址
    uint8_t reserved;         // 保留
    int16_t value;           // 校准值
} __attribute__((packed));
```

## 实现计划

### 第1步：创建EEPROM存储基类
创建新文件：`components/bl0906_factory/eeprom_calibration_storage.h`
- 继承自`CalibrationStorageBase`
- 实现EEPROM读写功能
- 提供与Flash存储相同的接口

### 第2步：实现EEPROM存储类
创建新文件：`components/bl0906_factory/eeprom_calibration_storage.cpp`
- 实现`init()`：初始化EEPROM，验证魔数和版本
- 实现`read_all()`：从EEPROM读取所有校准数据
- 实现`write_all()`：将校准数据写入EEPROM（仅校准版）
- 实现`erase()`：清除EEPROM数据（仅校准版）

### 第3步：修改BL0906Factory组件
修改文件：`components/bl0906_factory/bl0906_factory.cpp`
```cpp
// 在setup()方法中，根据编译标志选择存储方式
#ifdef USE_EEPROM_CALIBRATION
    #ifdef BL0906_CALIBRATION_MODE
        calib_storage_.reset(new EEPROMCalibrationStorage());
    #else
        calib_storage_.reset(new EEPROMCalibrationStorageBase());
    #endif
#else
    // 原有的Flash存储代码
#endif
```

### 第4步：添加YAML配置支持
在包文件中添加EEPROM配置：
```yaml
# 启用EEPROM存储
esphome:
  platformio_options:
    build_flags:
      - -DBL0906_CALIBRATION_MODE=1
      - -DUSE_EEPROM_CALIBRATION=1

# EEPROM组件配置
eeprom:
  size: 4096  # 4KB EEPROM大小
```

## 优势

1. **无需自定义分区表**：使用ESPHome默认分区表
2. **配置简单**：只需在YAML中启用EEPROM组件
3. **兼容性好**：ESPHome原生支持EEPROM
4. **空间充足**：4KB足够存储所有校准数据
5. **独立存储**：与NVS分开，避免冲突

## 代码修改要点

### 1. EEPROM初始化
```cpp
bool EEPROMCalibrationStorage::init() {
    // 初始化EEPROM
    EEPROM.begin(EEPROM_SIZE);
    
    // 读取并验证头部
    EEPROMHeader header;
    EEPROM.get(0, header);
    
    if (header.magic != CALIB_MAGIC) {
        ESP_LOGW(TAG, "EEPROM未初始化或数据无效");
        return false;
    }
    
    return true;
}
```

### 2. 数据读取
```cpp
bool EEPROMCalibrationStorage::read_all(std::vector<CalibrationEntry>& entries) {
    EEPROMHeader header;
    EEPROM.get(0, header);
    
    // 验证数据
    if (header.magic != CALIB_MAGIC || header.version != CALIB_VERSION) {
        return false;
    }
    
    // 读取校准条目
    entries.clear();
    for (int i = 0; i < header.entry_count; i++) {
        CalibrationEntry entry;
        EEPROM.get(HEADER_SIZE + i * sizeof(CalibrationEntry), entry);
        entries.push_back(entry);
    }
    
    // 验证CRC
    uint32_t calculated_crc = calculate_crc32(/* ... */);
    return calculated_crc == header.crc32;
}
```

### 3. 数据写入（仅校准版）
```cpp
bool EEPROMCalibrationStorage::write_all(const std::vector<CalibrationEntry>& entries) {
    // 准备头部
    EEPROMHeader header;
    header.magic = CALIB_MAGIC;
    header.version = CALIB_VERSION;
    header.entry_count = entries.size();
    header.timestamp = millis() / 1000;
    header.crc32 = calculate_crc32(/* ... */);
    
    // 写入头部
    EEPROM.put(0, header);
    
    // 写入校准数据
    for (size_t i = 0; i < entries.size(); i++) {
        EEPROM.put(HEADER_SIZE + i * sizeof(CalibrationEntry), entries[i]);
    }
    
    // 提交到Flash
    return EEPROM.commit();
}
```

## 测试计划

1. **初始化测试**：验证EEPROM能否正确初始化
2. **读写测试**：测试校准数据的保存和恢复
3. **断电测试**：验证数据持久性
4. **兼容性测试**：确保与现有功能兼容

## 迁移路径

1. 保持现有Flash存储代码不变
2. 通过编译标志选择存储方式
3. 逐步测试EEPROM存储功能
4. 确认稳定后可完全切换到EEPROM存储

## 注意事项

1. EEPROM写入次数有限（约10万次），需要合理规划写入频率
2. EEPROM.commit()会阻塞，建议在合适的时机调用
3. 需要处理EEPROM未初始化的情况
4. CRC校验确保数据完整性 