import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import number
from esphome.const import (
    CONF_ID,
    ICON_EMPTY,
    UNIT_EMPTY
)

# 从父模块导入所需类和常量
from . import BL0906Factory, BL0906Number, CONF_BL0906_FACTORY_ID

DEPENDENCIES = ["bl0906_factory"]

# 现代化校准Number配置 - 数据驱动
CHANNEL_COUNT = 6

# 校准类型配置
CALIB_TYPES = {
    "CHGN": {
        "name": "电流增益",
        "channels": list(range(1, CHANNEL_COUNT + 1)) + [-1],  # 1-6通道 + 电压通道(-1)
        "register_prefix": "BL0906_CHGN"
    },
    "CHOS": {
        "name": "电流偏置",
        "channels": list(range(1, CHANNEL_COUNT + 1)) + [-1],
        "register_prefix": "BL0906_CHOS"
    },
    "RMSGN": {
        "name": "有效值增益",
        "channels": list(range(1, CHANNEL_COUNT + 1)),
        "register_prefix": "BL0906_RMSGN"
    },
    "RMSOS": {
        "name": "有效值偏置",
        "channels": list(range(1, CHANNEL_COUNT + 1)),
        "register_prefix": "BL0906_RMSOS"
    },
    "WATTGN": {
        "name": "功率增益",
        "channels": list(range(1, CHANNEL_COUNT + 1)),
        "register_prefix": "BL0906_WATTGN"
    },
    "WATTOS": {
        "name": "功率偏置",
        "channels": list(range(1, CHANNEL_COUNT + 1)),
        "register_prefix": "BL0906_WATTOS"
    }
}

# 动态生成Number配置
NUMBER_CONFIGS = {}
for calib_type, type_config in CALIB_TYPES.items():
    for channel in type_config["channels"]:
        if channel == -1:  # 电压通道
            key = f"{calib_type.lower()}_v_decimal"
            internal_channel = -1
        else:
            key = f"{calib_type.lower()}_decimal_{channel}"
            internal_channel = channel - 1  # 内部使用0-5索引

        NUMBER_CONFIGS[key] = {
            "type": calib_type,
            "channel": internal_channel,
            "register_prefix": type_config["register_prefix"]
        }

# 现代化配置模式构建
def build_number_config_schema():
    """动态构建Number配置模式"""
    schema_dict = {cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory)}

    # 添加所有Number配置
    for key in NUMBER_CONFIGS.keys():
        schema_dict[cv.Optional(key)] = number.number_schema(
            class_=BL0906Number,
            icon=ICON_EMPTY,
            unit_of_measurement=UNIT_EMPTY,
        )

    return cv.Schema(schema_dict)

CONFIG_SCHEMA = build_number_config_schema()

async def to_code(config):
    bl0906_var = await cg.get_variable(config[CONF_BL0906_FACTORY_ID])

    # 现代化Number注册 - 数据驱动
    for number_key, number_config in NUMBER_CONFIGS.items():
        if number_key in config:
            conf = config[number_key]
            num = await number.new_number(
                conf,
                min_value=-32768,
                max_value=32767,
                step=1.0,
            )

            # 设置寄存器地址
            register_prefix = number_config["register_prefix"]
            channel = number_config["channel"]

            if channel == -1:  # 电压通道
                reg_expression = f"esphome::bl0906_factory::{register_prefix}_V"
            else:
                reg_expression = f"esphome::bl0906_factory::{register_prefix}_{channel + 1}"  # 寄存器使用1-6

            reg_addr = cg.RawExpression(reg_expression)
            cg.add(num.set_register_address(reg_addr))

            # 使用新的统一接口
            calib_type = number_config["type"]
            calib_type_enum = cg.RawExpression(f"esphome::bl0906_factory::BL0906Factory::CalibNumberType::{calib_type}")
            cg.add(bl0906_var.set_calib_number(calib_type_enum, num, channel))