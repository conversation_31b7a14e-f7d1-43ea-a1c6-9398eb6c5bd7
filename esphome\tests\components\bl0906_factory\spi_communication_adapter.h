#pragma once

#include "communication_adapter_interface.h"
#include "esphome/components/spi/spi.h"
#include "esphome/core/log.h"
#include "esphome/core/helpers.h"
#include "esphome/core/gpio.h"
#include <functional>

namespace esphome {
namespace bl0906_factory {

/**
 * SPI通信适配器实现
 * 
 * 实现基于SPI的BL0906通信协议
 */
class SpiCommunicationAdapter : public CommunicationAdapterInterface, 
                               public spi::SPIDevice<spi::BIT_ORDER_MSB_FIRST, spi::CLOCK_POLARITY_LOW, spi::CLOCK_PHASE_TRAILING, spi::DATA_RATE_1MHZ> {
public:
  SpiCommunicationAdapter() = default;
  ~SpiCommunicationAdapter() override = default;
  
  // ========== 设置方法 ==========
  
  /**
   * 设置SPI父组件
   * @param parent SPI组件指针
   */
  void set_spi_parent(spi::SPIComponent *parent);
  
  /**
   * 设置CS引脚
   * @param cs_pin CS引脚指针
   */
  void set_cs_pin(GPIOPin *cs_pin);
  
  // ========== CommunicationAdapterInterface 实现 ==========
  
  bool initialize() override;
  int32_t read_register(uint8_t address, bool* success = nullptr) override;
  bool write_register(uint8_t address, int16_t value) override;
  bool send_raw_command(const uint8_t* command, size_t length) override;
  
  bool is_available() override;
  bool is_connected() override;
  void flush_buffer() override;
  
  std::string get_last_error() const override;
  void reset_error_state() override;
  CommunicationError get_last_error_code() const override;
  
  size_t get_success_count() const override;
  size_t get_error_count() const override;
  CommunicationStats get_statistics() const override;
  void reset_statistics() override;
  
  std::string get_adapter_type() const override;
  std::string get_status_info() const override;
  bool self_test() override;

private:
  // ========== 内部状态 ==========
  
  bool initialized_ = false;
  CommunicationStats stats_;
  CommunicationError last_error_ = CommunicationError::SUCCESS;
  std::string last_error_message_;
  
  // ========== 配置参数 ==========
  
  static constexpr uint32_t SPI_RETRY_COUNT = 3;
  static constexpr uint32_t SPI_DELAY_US = 50;  // SPI操作间延时（微秒）
  static constexpr uint32_t CS_SETUP_DELAY_US = 10;  // CS建立时间（微秒）
  
  // ========== 内部方法 ==========
  
  /**
   * 发送SPI读取命令并接收响应
   * @param address 寄存器地址
   * @param success 成功标志指针
   * @return 读取到的值
   */
  int32_t send_spi_read_command(uint8_t address, bool* success);
  
  /**
   * 发送SPI写入命令
   * @param address 寄存器地址
   * @param value 要写入的值
   * @return true 写入成功，false 写入失败
   */
  bool send_spi_write_command(uint8_t address, int16_t value);
  
  /**
   * 计算SPI校验和
   * @param cmd 命令字节
   * @param addr 地址字节
   * @param data 数据（24位）
   * @return 校验和
   */
  uint8_t calculate_spi_checksum(uint8_t cmd, uint8_t addr, uint32_t data);
  
  /**
   * 判断寄存器是否为16位寄存器
   * @param address 寄存器地址
   * @return true 是16位寄存器，false 是24位寄存器
   */
  bool is_16bit_register(uint8_t address);
  
  /**
   * 判断寄存器是否为无符号类型
   * @param address 寄存器地址
   * @return true 无符号，false 有符号
   */
  bool is_unsigned_register(uint8_t address);
  
  /**
   * 设置错误状态
   * @param error 错误码
   * @param message 错误消息
   */
  void set_error(CommunicationError error, const std::string& message);
  
  /**
   * 更新统计信息
   * @param success 操作是否成功
   * @param error 错误码（如果失败）
   */
  void update_statistics(bool success, CommunicationError error = CommunicationError::SUCCESS);
  
  /**
   * 执行带重试的操作
   * @param operation 要执行的操作函数
   * @param max_retries 最大重试次数
   * @return 操作结果
   */
  template<typename T>
  T execute_with_retry(std::function<T()> operation, int max_retries = SPI_RETRY_COUNT);
  
  /**
   * 安全的SPI操作包装器
   * @param operation SPI操作函数
   * @return 操作是否成功
   */
  bool safe_spi_operation(std::function<void()> operation);
};

} // namespace bl0906_factory
} // namespace esphome 