# BL0906Factory 数据读取重构计划

## 1. 总体目标

将数据读取和数据处理完全分离，实现清晰的数据流水线：
```
数据读取阶段 → 芯片重启检测 → 持久化存储处理 → 能量统计更新 → 传感器发布
```

## 2. 状态机重构

### 2.1 新的状态机设计
每个loop()周期读取所有数据，但按通道分批进行以减少阻塞：

```cpp
enum class State {
  IDLE,
  READ_BASIC_SENSORS,      // 读取温度、频率、电压原始数据
  READ_CHANNEL_1,          // 读取通道1数据（电流、功率、电量）
  READ_CHANNEL_2,          // 读取通道2数据
  READ_CHANNEL_3,          // 读取通道3数据
  READ_CHANNEL_4,          // 读取通道4数据
  READ_CHANNEL_5,          // 读取通道5数据
  READ_CHANNEL_6,          // 读取通道6数据
  READ_TOTAL_DATA,         // 读取总功率和总电量
  CHECK_CHIP_RESTART,      // 检测芯片重启（基于完整数据集）
  PROCESS_PERSISTENCE,     // 处理持久化存储
  UPDATE_STATISTICS,       // 更新能量统计
  PUBLISH_SENSORS,         // 发布所有传感器数据
  HANDLE_ACTIONS          // 处理动作队列
};
```

### 2.2 执行流程
```
每个loop()调用完成一个完整的数据读取和处理周期：
IDLE 
→ READ_BASIC_SENSORS (读取基础传感器)
→ READ_CHANNEL_1 (读取通道1数据)
→ READ_CHANNEL_2 (读取通道2数据)
→ READ_CHANNEL_3 (读取通道3数据)
→ READ_CHANNEL_4 (读取通道4数据)
→ READ_CHANNEL_5 (读取通道5数据)
→ READ_CHANNEL_6 (读取通道6数据)
→ READ_TOTAL_DATA (读取总和数据)
→ CHECK_CHIP_RESTART (检测芯片重启)
→ PROCESS_PERSISTENCE (处理持久化存储)
→ UPDATE_STATISTICS (更新统计数据)
→ PUBLISH_SENSORS (发布传感器数据)
→ HANDLE_ACTIONS (处理动作队列)
→ IDLE (等待下次update()调用)
```

## 3. 数据结构重构

### 3.1 原始数据存储结构
```cpp
struct RawSensorData {
  // 基础传感器原始数据
  int32_t temperature_raw;
  int32_t frequency_raw;
  int32_t voltage_raw;
  
  // 通道数据（6个通道）
  struct ChannelData {
    int32_t current_raw;
    int32_t power_raw;
    int32_t energy_raw;
  } channels[CHANNEL_COUNT];
  
  // 总和数据
  int32_t power_sum_raw;
  int32_t energy_sum_raw;
  
  // 数据读取时间戳
  uint32_t timestamp;
  bool read_complete;
};
```

### 3.2 类成员变量
```cpp
private:
  RawSensorData current_data_;        // 当前周期的原始数据
  bool data_collection_complete_;     // 数据收集完成标志
  uint32_t data_read_start_time_;     // 数据读取开始时间
```

## 4. 核心方法重构

### 4.1 统一数据读取函数
```cpp
// 统一的原始数据读取函数（只读取，不处理）
// 这是唯一的数据读取函数，所有数据读取都通过此函数
int32_t read_raw_register_data(uint8_t address);

// 注意：不再有其他数据读取函数
// read_basic_sensors_raw()、read_channel_raw_data()、read_total_raw_data()
// 这些函数内部都只调用 read_raw_register_data() 来获取数据
```

### 4.2 数据处理流水线
```cpp
// 芯片重启检测（基于完整数据集）
void detect_chip_restart(const RawSensorData& data);

// 持久化存储处理
void process_energy_persistence(const RawSensorData& data);

// 能量统计更新
void update_energy_statistics(const RawSensorData& data);

// 传感器数据发布
void publish_all_sensors(const RawSensorData& data);
```

### 4.3 数据转换方法
```cpp
// 统一的数据转换函数（根据寄存器地址和原始值转换为实际值）
// 这是唯一的数据转换函数，所有数据转换都通过此函数
float convert_raw_to_value(uint8_t address, int32_t raw_value);
```

## 5. 重构后的loop()方法

```cpp
void BL0906Factory::loop() {
  // 清空UART缓冲区
  while (this->available()) this->flush();

  switch (this->current_state_) {
    case State::IDLE:
      // 初始化新的数据读取周期
      current_data_ = {};
      current_data_.timestamp = millis();
      data_collection_complete_ = false;
      data_read_start_time_ = millis();
      this->current_state_ = State::READ_BASIC_SENSORS;
      break;

    case State::READ_BASIC_SENSORS:
      // 直接调用统一读取函数读取基础传感器数据
      current_data_.temperature_raw = read_raw_register_data(BL0906_TEMPERATURE);
      current_data_.frequency_raw = read_raw_register_data(BL0906_FREQUENCY);
      current_data_.voltage_raw = read_raw_register_data(BL0906_V_RMS);
      this->current_state_ = State::READ_CHANNEL_1;
      break;

    case State::READ_CHANNEL_1:
      // 直接调用统一读取函数读取通道1数据
      current_data_.channels[0].current_raw = read_raw_register_data(BL0906_I_RMS[0]);
      current_data_.channels[0].power_raw = read_raw_register_data(BL0906_WATT[0]);
      current_data_.channels[0].energy_raw = read_raw_register_data(BL0906_CF_CNT[0]);
      this->current_state_ = State::READ_CHANNEL_2;
      break;

    case State::READ_CHANNEL_2:
      // 直接调用统一读取函数读取通道2数据
      current_data_.channels[1].current_raw = read_raw_register_data(BL0906_I_RMS[1]);
      current_data_.channels[1].power_raw = read_raw_register_data(BL0906_WATT[1]);
      current_data_.channels[1].energy_raw = read_raw_register_data(BL0906_CF_CNT[1]);
      this->current_state_ = State::READ_CHANNEL_3;
      break;

    case State::READ_CHANNEL_3:
      // 直接调用统一读取函数读取通道3数据
      current_data_.channels[2].current_raw = read_raw_register_data(BL0906_I_RMS[2]);
      current_data_.channels[2].power_raw = read_raw_register_data(BL0906_WATT[2]);
      current_data_.channels[2].energy_raw = read_raw_register_data(BL0906_CF_CNT[2]);
      this->current_state_ = State::READ_CHANNEL_4;
      break;

    case State::READ_CHANNEL_4:
      // 直接调用统一读取函数读取通道4数据
      current_data_.channels[3].current_raw = read_raw_register_data(BL0906_I_RMS[3]);
      current_data_.channels[3].power_raw = read_raw_register_data(BL0906_WATT[3]);
      current_data_.channels[3].energy_raw = read_raw_register_data(BL0906_CF_CNT[3]);
      this->current_state_ = State::READ_CHANNEL_5;
      break;

    case State::READ_CHANNEL_5:
      // 直接调用统一读取函数读取通道5数据
      current_data_.channels[4].current_raw = read_raw_register_data(BL0906_I_RMS[4]);
      current_data_.channels[4].power_raw = read_raw_register_data(BL0906_WATT[4]);
      current_data_.channels[4].energy_raw = read_raw_register_data(BL0906_CF_CNT[4]);
      this->current_state_ = State::READ_CHANNEL_6;
      break;

    case State::READ_CHANNEL_6:
      // 直接调用统一读取函数读取通道6数据
      current_data_.channels[5].current_raw = read_raw_register_data(BL0906_I_RMS[5]);
      current_data_.channels[5].power_raw = read_raw_register_data(BL0906_WATT[5]);
      current_data_.channels[5].energy_raw = read_raw_register_data(BL0906_CF_CNT[5]);
      this->current_state_ = State::READ_TOTAL_DATA;
      break;

    case State::READ_TOTAL_DATA:
      // 直接调用统一读取函数读取总和数据
      current_data_.power_sum_raw = read_raw_register_data(BL0906_WATT_SUM);
      current_data_.energy_sum_raw = read_raw_register_data(BL0906_CF_SUM_CNT);
      current_data_.read_complete = true;
      data_collection_complete_ = true;
      this->current_state_ = State::CHECK_CHIP_RESTART;
      break;

    case State::CHECK_CHIP_RESTART:
      detect_chip_restart(current_data_);
      this->current_state_ = State::PROCESS_PERSISTENCE;
      break;

    case State::PROCESS_PERSISTENCE:
      process_energy_persistence(current_data_);
      this->current_state_ = State::UPDATE_STATISTICS;
      break;

    case State::UPDATE_STATISTICS:
      update_energy_statistics(current_data_);
      this->current_state_ = State::PUBLISH_SENSORS;
      break;

    case State::PUBLISH_SENSORS:
      publish_all_sensors(current_data_);
      this->current_state_ = State::HANDLE_ACTIONS;
      break;

    case State::HANDLE_ACTIONS:
      this->handle_actions_();
      // 完成一个完整周期，回到IDLE等待下次update()调用
      this->current_state_ = State::IDLE;
      break;
  }
}
```

## 6. 关键优化点

### 6.1 数据一致性保证
- 所有数据在同一个update()周期内读取完成
- 芯片重启检测基于完整的数据集
- 持久化存储基于一致的数据状态

### 6.2 性能优化
- 每个状态只执行最小必要的操作
- 通道数据分状态读取，避免长时间阻塞
- 原始数据读取与处理完全分离

### 6.3 错误处理
- 数据读取失败时返回默认值（0）
- 单个通道读取失败不影响其他通道
- 简化的错误处理逻辑，专注于数据流水线的稳定性

### 6.4 调试和维护
- 清晰的数据流向：读取→检测→存储→统计→发布
- 每个阶段独立，便于单独测试和调试
- 原始数据可以完整记录用于问题分析

## 7. 移除的功能

### 7.1 不再需要的方法
- `read_data_()` - 被拆分为读取和处理两部分
- `read_basic_sensors()` - 直接在状态机中调用 `read_raw_register_data()`
- `read_channel_data()` - 直接在状态机中调用 `read_raw_register_data()`
- `read_total_data()` - 直接在状态机中调用 `read_raw_register_data()`
- `read_basic_sensors_raw()` - 不再需要，直接在状态机中读取
- `read_channel_raw_data()` - 不再需要，直接在状态机中读取
- `read_total_raw_data()` - 不再需要，直接在状态机中读取

### 7.2 简化的变量
- 移除 `current_channel_` - 不再需要通道循环变量
- 简化状态机逻辑 - 每个通道都有独立状态

## 8. 实施步骤

1. **第一阶段**：重构数据结构和状态机枚举
2. **第二阶段**：实现统一的数据读取函数 `read_raw_register_data()`
3. **第三阶段**：实现统一的数据转换函数 `convert_raw_to_value()`
4. **第四阶段**：实现数据处理流水线方法
5. **第五阶段**：重构loop()方法
6. **第六阶段**：测试和优化

## 8.1 核心函数设计

### 8.1.1 统一数据读取函数
```cpp
int32_t BL0906Factory::read_raw_register_data(uint8_t address) {
  // 清空缓冲区
  flush_rx_buffer();
  
  // 发送读命令并接收数据
  DataPacket data;
  if (!send_read_command_and_receive(address, data)) {
    ESP_LOGE(FACTORY_TAG, "读取寄存器失败，地址:0x%02X", address);
    return 0;  // 失败时返回0
  }
  
  // 组合原始数据
  int32_t raw = (data.h << 16) | (data.m << 8) | data.l;
  ESP_LOGV(FACTORY_TAG, "读取寄存器 0x%02X: 原始值=%d", address, raw);
  
  return raw;
}
```

### 8.1.2 统一数据转换函数
```cpp
float BL0906Factory::convert_raw_to_value(uint8_t address, int32_t raw_value) {
  switch (address) {
    case BL0906_FREQUENCY:
      return (raw_value > 0) ? 10000000.0f / raw_value : 0;
      
    case BL0906_TEMPERATURE:
      return (raw_value - 64) * 12.5f / 59.0f - 40.0f;
      
    case BL0906_V_RMS:
      return raw_value / Kv;
      
    // 电流寄存器
    case BL0906_I_RMS[0]:
    case BL0906_I_RMS[1]:
    case BL0906_I_RMS[2]:
    case BL0906_I_RMS[3]:
    case BL0906_I_RMS[4]:
    case BL0906_I_RMS[5]:
      return raw_value / Ki;
      
    // 功率寄存器
    case BL0906_WATT[0]:
    case BL0906_WATT[1]:
    case BL0906_WATT[2]:
    case BL0906_WATT[3]:
    case BL0906_WATT[4]:
    case BL0906_WATT[5]:
      return raw_value / Kp;
      
    case BL0906_WATT_SUM:
      return raw_value / Kp_sum;
      
    // 电量寄存器
    case BL0906_CF_CNT[0]:
    case BL0906_CF_CNT[1]:
    case BL0906_CF_CNT[2]:
    case BL0906_CF_CNT[3]:
    case BL0906_CF_CNT[4]:
    case BL0906_CF_CNT[5]:
      return raw_value / Ke;
      
    case BL0906_CF_SUM_CNT:
      return raw_value / Ke_sum;
      
    default:
      ESP_LOGW(FACTORY_TAG, "未知寄存器地址: 0x%02X", address);
      return raw_value;  // 未知寄存器直接返回原始值
  }
}
```

## 9. 预期效果

- **极简的数据读取架构**：只有一个函数 `read_raw_register_data()` 负责所有数据读取
- **统一的数据转换**：只有一个函数 `convert_raw_to_value()` 负责所有数据转换
- **清晰的职责分离**：数据读取、处理、存储、统计、发布各司其职
- **完整的数据一致性**：基于完整数据集进行所有处理决策
- **更好的可维护性**：每个阶段独立，便于调试和扩展
- **稳定的性能表现**：每个状态执行时间可控，避免长时间阻塞
- **简化的API设计**：只需要维护两个核心函数，大大降低复杂度 