# BL0906Factory 存储计数功能说明

## 功能概述

存储计数功能为BL0906Factory组件的持久存储系统添加了计数器，用于跟踪持久存储操作的触发次数。每次调用`save_energy_data()`函数时，存储计数器会自动递增1。

## 主要特性

### 1. 自动计数
- 每次触发持久存储时，存储计数器自动+1
- 计数器值会与其他持久化数据一起保存到Flash存储器
- 重启后可以从Flash中恢复存储计数

### 2. 数据完整性
- 存储计数被包含在校验和计算中，确保数据完整性
- 如果数据损坏，存储计数会随其他数据一起重置为0

### 3. 故障处理
- 如果保存失败，存储计数器会自动回退，保持准确性
- 支持手动重置存储计数器

## API接口

### 获取存储计数
```cpp
uint32_t count = factory.get_save_count();
```

### 重置存储计数
```cpp
factory.reset_save_count();
```

### 强制保存数据（会增加存储计数）
```cpp
factory.force_save_energy_data();
```

## 数据结构变化

### EnergyPersistenceData结构体
```cpp
struct EnergyPersistenceData {
  uint32_t persistent_cf_count[7];    // CF_count数据
  uint32_t last_cf_count[7];          // 上次硬件CF_count
  uint32_t save_count;                // 存储计数器（新增）
  uint32_t checksum;                  // 校验和
};
```

## 使用场景

1. **调试和监控**：了解系统的存储频率和行为
2. **性能分析**：评估Flash写入次数，优化存储策略
3. **故障诊断**：通过存储计数变化判断系统是否正常工作
4. **数据统计**：为系统维护提供参考数据

## 日志输出示例

```
[I][bl0906_factory:xxx] ✅ CF_count数据保存成功，总持久化CF_count: 12345, 存储计数: 42
[I][bl0906_factory:xxx] ✅ 成功加载CF_count数据，存储计数: 42
[I][bl0906_factory:xxx] 存储计数: 42 次
```

## 兼容性说明

- 新版本代码可以正确处理旧版本的数据（没有save_count字段）
- 如果检测到数据格式不匹配，会自动重置所有数据
- 建议在升级后检查存储计数是否从0开始

## 注意事项

1. 存储计数器是32位无符号整数，理论上可以计数到约42亿次
2. 存储计数会随着`reset_energy_data()`一起重置
3. 只有实际写入Flash的操作才会增加计数，跳过的保存操作不会计数
4. 存储计数的准确性依赖于校验和验证，数据损坏时会重置 