#pragma once

#ifdef USE_UART_COMMUNICATION_ADAPTER

#include "communication_adapter_interface.h"
#include "esphome/components/uart/uart.h"
#include "esphome/components/uart/uart_component.h"
#include "esphome/core/log.h"
#include "esphome/core/helpers.h"
#include <vector>
#include <functional>

namespace esphome {
namespace bl0906_factory {

/**
 * UART通信适配器实现
 * 
 * 实现基于UART的BL0906通信协议
 */
class UartCommunicationAdapter : public CommunicationAdapterInterface, public uart::UARTDevice {
public:
  UartCommunicationAdapter() = default;
  ~UartCommunicationAdapter() override = default;
  
  // ========== 设置方法 ==========
  
  /**
   * 设置UART父组件
   * @param parent UART组件指针
   */
  void set_uart_parent(uart::UARTComponent *parent);
  
  // ========== CommunicationAdapterInterface 实现 ==========
  
  bool initialize() override;
  int32_t read_register(uint8_t address, bool* success = nullptr) override;
  bool write_register(uint8_t address, int16_t value) override;
  bool send_raw_command(const uint8_t* command, size_t length) override;
  
  bool is_available() override;
  bool is_connected() override;
  void flush_buffer() override;
  
  std::string get_last_error() const override;
  void reset_error_state() override;
  CommunicationError get_last_error_code() const override;
  
  size_t get_success_count() const override;
  size_t get_error_count() const override;
  CommunicationStats get_statistics() const override;
  void reset_statistics() override;
  
  std::string get_adapter_type() const override;
  std::string get_status_info() const override;
  bool self_test() override;

private:
  // ========== 内部数据结构 ==========
  
  /**
   * UART数据包结构体
   */
  struct DataPacket {
    uint8_t l;            // low byte
    uint8_t m;            // middle byte
    uint8_t h;            // high byte
    uint8_t checksum;     // checksum
  };
  
  /**
   * BL0906 24位无符号数据类型
   */
  struct ube24_t {
    uint8_t l;            // low byte
    uint8_t m;            // middle byte
    uint8_t h;            // high byte
  };
  
  /**
   * BL0906 24位有符号数据类型
   */
  struct sbe24_t {
    uint8_t l;            // low byte
    uint8_t m;            // middle byte
    int8_t h;             // high byte, signed for proper sign extension
  };
  
  // ========== 内部状态 ==========
  
  bool initialized_ = false;
  CommunicationStats stats_;
  CommunicationError last_error_ = CommunicationError::SUCCESS;
  std::string last_error_message_;
  
  // ========== 配置参数 ==========
  
  static constexpr uint32_t UART_TIMEOUT_MS = 200;
  static constexpr uint32_t UART_RETRY_COUNT = 3;
  static constexpr size_t MAX_RESPONSE_LENGTH = 6;
  
  // ========== 内部方法 ==========
  
  /**
   * 等待UART数据可用
   * @param len 期望的数据长度
   * @param timeout_ms 超时时间（毫秒）
   * @return true 数据可用，false 超时
   */
  bool wait_until_available(size_t len, uint32_t timeout_ms);
  
  /**
   * 发送读取命令并接收响应
   * @param address 寄存器地址
   * @param success 成功标志指针
   * @return 读取到的值
   */
  int32_t send_read_command_and_receive(uint8_t address, bool* success);
  
  /**
   * 发送写入命令
   * @param address 寄存器地址
   * @param value 要写入的值
   * @return true 写入成功，false 写入失败
   */
  bool send_write_command(uint8_t address, int16_t value);
  
  /**
   * 计算校验和
   * @param data 数据指针
   * @param len 数据长度
   * @return 校验和
   */
  uint8_t calculate_checksum(const uint8_t* data, size_t len);
  
  /**
   * 验证响应数据的校验和
   * @param data 响应数据
   * @return true 校验和正确，false 校验和错误
   */
  bool verify_checksum(const std::vector<uint8_t>& data);
  
  /**
   * 转换24位数据为32位整数（无符号）
   * @param data 24位数据
   * @return 32位整数值
   */
  uint32_t to_uint32_t(const ube24_t& data);
  
  /**
   * 转换24位数据为32位整数（有符号）
   * @param data 24位数据
   * @return 32位整数值
   */
  int32_t to_int32_t(const sbe24_t& data);
  
  /**
   * 判断寄存器是否为16位寄存器
   * @param address 寄存器地址
   * @return true 是16位寄存器，false 是24位寄存器
   */
  bool is_16bit_register(uint8_t address);
  
  /**
   * 判断寄存器是否为无符号类型
   * @param address 寄存器地址
   * @return true 无符号，false 有符号
   */
  bool is_unsigned_register(uint8_t address);
  
  /**
   * 设置错误状态
   * @param error 错误码
   * @param message 错误消息
   */
  void set_error(CommunicationError error, const std::string& message);
  
  /**
   * 更新统计信息
   * @param success 操作是否成功
   * @param error 错误码（如果失败）
   */
  void update_statistics(bool success, CommunicationError error = CommunicationError::SUCCESS);
  
  /**
   * 执行带重试的操作
   * @param operation 要执行的操作函数
   * @param max_retries 最大重试次数
   * @return 操作结果
   */
  template<typename T>
  T execute_with_retry(std::function<T()> operation, int max_retries = UART_RETRY_COUNT);
};

} // namespace bl0906_factory
} // namespace esphome

#endif // USE_UART_COMMUNICATION_ADAPTER 