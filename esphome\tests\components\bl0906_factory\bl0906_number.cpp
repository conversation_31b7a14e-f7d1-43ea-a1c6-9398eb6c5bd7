#include "bl0906_number.h"
#include "bl0906_factory.h"
#include "esphome/core/log.h"

namespace esphome {
namespace bl0906_factory {

static const char *const NUMBER_TAG = "bl0906_number";

void BL0906Number::set_register_address(uint8_t address) {
  ESP_LOGV(NUMBER_TAG, "设置校准数字组件寄存器地址: 0x%02X", address);
  if (address == 0) {
    ESP_LOGW(NUMBER_TAG, "警告：尝试设置无效的寄存器地址(0x00)");
  }
  register_address_ = address;
}

void BL0906Number::update_from_register() {
  if (register_address_ == 0) {
    ESP_LOGE(NUMBER_TAG, "错误：寄存器地址未设置或无效(0x00)！");
    return;
  }

  if (parent_ != nullptr) {
    int32_t reg_value = parent_->read_register_value(register_address_);
    ESP_LOGD(NUMBER_TAG, "从寄存器 0x%02X 读取值 %d", register_address_, reg_value);
    this->publish_state(reg_value);
  } else {
    ESP_LOGE(NUMBER_TAG, "父BL0906实例未设置!");
  }
}

void BL0906Number::setup() {
  ESP_LOGCONFIG(NUMBER_TAG, "Setting up BL0906 number '%s'...", this->get_name().c_str());

  // 检查父指针是否已设置
  if (parent_ == nullptr) {
    ESP_LOGW(NUMBER_TAG, "父BL0906实例尚未设置，跳过初始读取");
    return;
  }

  // 检查寄存器地址是否有效
  if (register_address_ == 0) {
    ESP_LOGW(NUMBER_TAG, "寄存器地址未设置，跳过初始读取");
    return;
  }

  update_from_register();
}

void BL0906Number::dump_config() {
  ESP_LOGCONFIG(NUMBER_TAG, "BL0906 Number:");
  ESP_LOGCONFIG(NUMBER_TAG, "  Number '%s'", this->get_name().c_str());
  ESP_LOGCONFIG(NUMBER_TAG, "  Register: 0x%02X", this->register_address_);
}

void BL0906Number::control(float value) {
  if (!parent_) {
    ESP_LOGE(NUMBER_TAG, "错误：父BL0906实例未设置！无法写入寄存器。");
    return;
  }

  uint8_t addr = this->get_register_address();
  if (addr == 0) {
    ESP_LOGE(NUMBER_TAG, "错误：校准数字组件的寄存器地址未设置！");
    return;
  }

  // 使用父实例API锁定资源
  if (parent_->lock()) {
    int16_t int_value = static_cast<int16_t>(value);
    ESP_LOGD(NUMBER_TAG, "控制校准数字组件 '%s'：地址=0x%02X，写入值=%d (float: %.2f)", this->get_name().c_str(), addr, int_value, value);

    if (parent_->write_register_value(addr, int_value)) {
      ESP_LOGD(NUMBER_TAG, "寄存器 0x%02X 写入成功 ('%s')，发布状态: %.2f", addr, this->get_name().c_str(), value);
      this->publish_state(value);
    } else {
      ESP_LOGE(NUMBER_TAG, "写入寄存器 0x%02X 失败 ('%s')", addr, this->get_name().c_str());
      this->update_from_register();
    }

    // 使用父实例API释放锁
    parent_->unlock();
  } else {
    ESP_LOGE(NUMBER_TAG, "无法获取锁，放弃写入寄存器 0x%02X", addr);
    this->update_from_register();
  }
}

}  // namespace bl0906_factory
}  // namespace esphome
