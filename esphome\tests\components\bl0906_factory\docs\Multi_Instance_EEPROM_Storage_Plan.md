# 多BL0906实例EEPROM存储方案

## 概述
当系统中配置多个BL0906实例时，EEPROM存储方案的容量分析和实现策略。

## 单个BL0906实例存储需求

### 校准寄存器分析
```
通道相关校准寄存器（每个通道都需要）：
- CHGN   (电流增益)     - 6个通道
- CHOS   (电流偏置)     - 6个通道
- RMSGN  (有效值增益)   - 6个通道
- RMSOS  (有效值偏置)   - 6个通道
- WATTGN (功率增益)     - 6个通道
- WATTOS (功率偏置)     - 6个通道
小计: 6 × 6 = 36个寄存器

电压相关校准寄存器（全局）：
- CHGN_V (电压增益)     - 1个
- CHOS_V (电压偏置)     - 1个
小计: 2个寄存器

总计: 38个校准寄存器
```

### 存储空间计算
```
每个BL0906实例：
- 校准数据: 38个 × 4字节 = 152字节
- 设备标识: 4字节（用于区分不同实例）
- 总计: 156字节
```

## EEPROM容量分析

### 可用空间
- EEPROM总容量：4096字节
- 头部结构：32字节
- 可用数据区：4064字节

### 理论最大实例数
- 共享头部方案：4064 ÷ 156 ≈ 26个实例
- 独立分区方案：4096 ÷ 512 = 8个实例

## 推荐存储方案

### 方案1：共享头部存储（推荐用于≤8个实例）

#### 存储结构
```
偏移量    大小    描述
0x0000    4      魔数 (0xB0906CAL)
0x0004    2      版本号 (0x0002) // 支持多实例
0x0006    1      实例数量
0x0007    1      保留
0x0008    4      CRC32校验和
0x000C    4      时间戳
0x0010    16     保留
0x0020    N×156  实例数据区
```

#### 实例数据结构（156字节）
```cpp
struct InstanceCalibData {
    uint32_t instance_id;              // 实例标识（如UART地址）
    CalibrationEntry entries[38];      // 38个校准条目
} __attribute__((packed));
```

### 方案2：分区存储（推荐用于>8个实例）

#### 存储布局
```
将4KB EEPROM分为8个独立分区，每个512字节：
分区0: 0x000-0x1FF (实例0)
分区1: 0x200-0x3FF (实例1)
分区2: 0x400-0x5FF (实例2)
...
分区7: 0xE00-0xFFF (实例7)
```

#### 分区结构（512字节）
```
偏移量    大小    描述
0x000     4      魔数
0x004     4      实例ID
0x008     2      版本号
0x00A     2      条目数量
0x00C     4      CRC32
0x010     4      时间戳
0x014     12     保留
0x020     152    校准数据（38×4）
0x0B8     328    未使用空间
```

## 实现策略

### 1. 实例标识
```cpp
// 使用UART地址或自定义ID作为实例标识
uint32_t get_instance_id() {
    // 方案A: 使用UART引脚组合
    return (uart_rx_pin << 16) | (uart_tx_pin << 8) | uart_id;
    
    // 方案B: 使用YAML配置的ID
    return configured_instance_id;
}
```

### 2. 多实例读写
```cpp
class MultiInstanceEEPROMStorage : public CalibrationStorageBase {
private:
    uint32_t instance_id_;
    int partition_index_;  // -1表示共享存储，0-7表示分区索引
    
public:
    bool init() override {
        instance_id_ = get_instance_id();
        
        // 查找或分配存储位置
        if (total_instances <= 8) {
            // 使用共享头部方案
            return init_shared_storage();
        } else {
            // 使用分区方案
            partition_index_ = allocate_partition();
            return init_partition_storage();
        }
    }
    
    bool read_all(std::vector<CalibrationEntry>& entries) override {
        if (partition_index_ >= 0) {
            return read_from_partition(entries);
        } else {
            return read_from_shared_storage(entries);
        }
    }
};
```

### 3. YAML配置示例
```yaml
# 多BL0906实例配置
bl0906_factory:
  - id: bl0906_1
    uart_id: uart_bus1
    instance_id: 1  # 可选，用于区分实例
    
  - id: bl0906_2
    uart_id: uart_bus2
    instance_id: 2
    
# EEPROM配置
eeprom:
  size: 4096
  # 多实例模式：shared 或 partitioned
  multi_instance_mode: shared
```

## 实际应用场景分析

### 典型应用
1. **单相6路监测**：1个BL0906实例，152字节
2. **三相18路监测**：3个BL0906实例，456字节
3. **配电柜监测**：最多8个BL0906实例，1248字节

### 容量评估
- **1-8个实例**：推荐共享头部方案，空间利用率高
- **9-26个实例**：需要优化存储结构，考虑压缩
- **>26个实例**：超出EEPROM容量，需要外部存储

## 优化建议

### 1. 数据压缩
- 使用差分编码存储校准值
- 只存储非默认值的寄存器

### 2. 选择性存储
- 允许配置哪些寄存器需要持久化
- 某些寄存器可能使用固定值

### 3. 外部存储扩展
- 使用SPI Flash或I2C EEPROM
- 支持更大容量的校准数据存储

## 结论

EEPROM存储方案可以满足大多数多BL0906实例的应用场景：
- **常规应用（1-8个实例）**：完全满足，有充足余量
- **扩展应用（9-26个实例）**：理论可行，需要优化
- **大型系统（>26个实例）**：需要外部存储方案

推荐在实际应用中限制为最多8个BL0906实例，以确保存储可靠性和管理简便性。 