#pragma once

namespace esphome {
namespace bl0906_factory {

// 操作命令 - 条件编译支持
#ifdef USE_BL0906_FACTORY_SPI
static constexpr uint8_t BL0906_READ_COMMAND = 0x82;   // SPI读操作命令
static constexpr uint8_t BL0906_WRITE_COMMAND = 0x81;  // SPI写操作命令
#else
static constexpr uint8_t BL0906_READ_COMMAND = 0x35;   // UART读操作命令
static constexpr uint8_t BL0906_WRITE_COMMAND = 0xCA;  // UART写操作命令
#endif

// 电流有效值寄存器
static constexpr uint8_t BL0906_I_1_RMS = 0x0D;  // 电流 1 通道有效值寄存器地址
static constexpr uint8_t BL0906_I_2_RMS = 0x0E;  // 电流 2 通道有效值寄存器地址
static constexpr uint8_t BL0906_I_3_RMS = 0x0F;  // 电流 3 通道有效值寄存器地址
static constexpr uint8_t BL0906_I_4_RMS = 0x10;  // 电流 4 通道有效值寄存器地址
static constexpr uint8_t BL0906_I_5_RMS = 0x13;  // 电流 5 通道有效值寄存器地址
static constexpr uint8_t BL0906_I_6_RMS = 0x14;  // 电流 6 通道有效值寄存器地址

// 电压有效值寄存器
static constexpr uint8_t BL0906_V_RMS = 0x16;    // 电压通道有效值寄存器

// 功率寄存器
static constexpr uint8_t BL0906_WATT_1 = 0X23;   // 通道 1 有功功率寄存器
static constexpr uint8_t BL0906_WATT_2 = 0X24;   // 通道 2 有功功率寄存器
static constexpr uint8_t BL0906_WATT_3 = 0X25;   // 通道 3 有功功率寄存器
static constexpr uint8_t BL0906_WATT_4 = 0X26;   // 通道 4 有功功率寄存器
static constexpr uint8_t BL0906_WATT_5 = 0X29;   // 通道 5 有功功率寄存器
static constexpr uint8_t BL0906_WATT_6 = 0X2A;   // 通道 6 有功功率寄存器
static constexpr uint8_t BL0906_WATT_SUM = 0X2C; // 总有功功率寄存器

// 能量脉冲计数寄存器
static constexpr uint8_t BL0906_CF_1_CNT = 0X30; // 通道 1 有功脉冲计数，无符号
static constexpr uint8_t BL0906_CF_2_CNT = 0X31; // 通道 2 有功脉冲计数，无符号
static constexpr uint8_t BL0906_CF_3_CNT = 0X32; // 通道 3 有功脉冲计数，无符号
static constexpr uint8_t BL0906_CF_4_CNT = 0X33; // 通道 4 有功脉冲计数，无符号
static constexpr uint8_t BL0906_CF_5_CNT = 0X36; // 通道 5 有功脉冲计数，无符号
static constexpr uint8_t BL0906_CF_6_CNT = 0X37; // 通道 6 有功脉冲计数，无符号
static constexpr uint8_t BL0906_CF_SUM_CNT = 0X39; // 总有功脉冲计数，无符号

// 其他测量寄存器
static constexpr uint8_t BL0906_FREQUENCY = 0X4E;   // 线电压频率周期，无符号
static constexpr uint8_t BL0906_TEMPERATURE = 0X5E; // 内部温度值，无符号

// 有效值增益校准寄存器
static constexpr uint8_t BL0906_RMSGN_1 = 0x6D; // 有效值增益调整寄存器地址,通道1
static constexpr uint8_t BL0906_RMSGN_2 = 0x6E; // 有效值增益调整寄存器地址,通道2
static constexpr uint8_t BL0906_RMSGN_3 = 0x6F; // 有效值增益调整寄存器地址,通道3
static constexpr uint8_t BL0906_RMSGN_4 = 0x70; // 有效值增益调整寄存器地址,通道4
static constexpr uint8_t BL0906_RMSGN_5 = 0x73; // 有效值增益调整寄存器地址,通道5
static constexpr uint8_t BL0906_RMSGN_6 = 0x74; // 有效值增益调整寄存器地址,通道6
static constexpr uint8_t BL0906_RMSGN_V = 0x76; // 电压通道有效值增益调整寄存器

// 有效值偏置校准寄存器
static constexpr uint8_t BL0906_RMSOS_1 = 0x78; // 有效值偏置校正寄存器地址,通道1
static constexpr uint8_t BL0906_RMSOS_2 = 0x79; // 有效值偏置校正寄存器地址,通道2
static constexpr uint8_t BL0906_RMSOS_3 = 0x7A; // 有效值偏置校正寄存器地址,通道3
static constexpr uint8_t BL0906_RMSOS_4 = 0x7B; // 有效值偏置校正寄存器地址,通道4
static constexpr uint8_t BL0906_RMSOS_5 = 0x7E; // 有效值偏置校正寄存器地址,通道5
static constexpr uint8_t BL0906_RMSOS_6 = 0x7F; // 有效值偏置校正寄存器地址,通道6
static constexpr uint8_t BL0906_RMSOS_V = 0x81; // 电压通道有效值偏置校正寄存器

// 电流通道增益校准寄存器
static constexpr uint8_t BL0906_CHGN_1 = 0xA1; // 电流 1 通道增益调整寄存器，补码
static constexpr uint8_t BL0906_CHGN_2 = 0xA2; // 电流 2 通道增益调整寄存器，补码
static constexpr uint8_t BL0906_CHGN_3 = 0xA3; // 电流 3 通道增益调整寄存器，补码
static constexpr uint8_t BL0906_CHGN_4 = 0xA4; // 电流 4 通道增益调整寄存器，补码
static constexpr uint8_t BL0906_CHGN_5 = 0xA7; // 电流 5 通道增益调整寄存器，补码
static constexpr uint8_t BL0906_CHGN_6 = 0xA8; // 电流 6 通道增益调整寄存器，补码
static constexpr uint8_t BL0906_CHGN_V = 0xAA; // 电压通道增益调整寄存器，补码

// 电流通道偏置校准寄存器
static constexpr uint8_t BL0906_CHOS_1 = 0xAC; // 电流 1 通道通道偏置调整寄存器，补码
static constexpr uint8_t BL0906_CHOS_2 = 0xAD; // 电流 2 通道通道偏置调整寄存器，补码
static constexpr uint8_t BL0906_CHOS_3 = 0xAE; // 电流 3 通道通道偏置调整寄存器，补码
static constexpr uint8_t BL0906_CHOS_4 = 0xAF; // 电流 4 通道通道偏置调整寄存器，补码
static constexpr uint8_t BL0906_CHOS_5 = 0xB2; // 电流 5 通道通道偏置调整寄存器，补码
static constexpr uint8_t BL0906_CHOS_6 = 0xB3; // 电流 6 通道通道偏置调整寄存器，补码
static constexpr uint8_t BL0906_CHOS_V = 0xB5; // 电压通道偏置调整寄存器，补码

// 功率增益校准寄存器
static constexpr uint8_t BL0906_WATTGN_1 = 0xB7; // 通道 1 有功功率增益调整寄存器
static constexpr uint8_t BL0906_WATTGN_2 = 0xB8; // 通道 2 有功功率增益调整寄存器
static constexpr uint8_t BL0906_WATTGN_3 = 0xB9; // 通道 3 有功功率增益调整寄存器
static constexpr uint8_t BL0906_WATTGN_4 = 0xBA; // 通道 4 有功功率增益调整寄存器
static constexpr uint8_t BL0906_WATTGN_5 = 0xBD; // 通道 5 有功功率增益调整寄存器
static constexpr uint8_t BL0906_WATTGN_6 = 0xBE; // 通道 6 有功功率增益调整寄存器

// 功率偏置校准寄存器
static constexpr uint8_t BL0906_WATTOS_1 = 0xC1; // 通道 1 有功功率偏置调整寄存器，补码
static constexpr uint8_t BL0906_WATTOS_2 = 0xC2; // 通道 2 有功功率偏置调整寄存器，补码
static constexpr uint8_t BL0906_WATTOS_3 = 0xC3; // 通道 3 有功功率偏置调整寄存器，补码
static constexpr uint8_t BL0906_WATTOS_4 = 0xC4; // 通道 4 有功功率偏置调整寄存器，补码
static constexpr uint8_t BL0906_WATTOS_5 = 0xC7; // 通道 5 有功功率偏置调整寄存器，补码
static constexpr uint8_t BL0906_WATTOS_6 = 0xC8; // 通道 6 有功功率偏置调整寄存器，补码

// 写保护控制指令
static constexpr uint8_t USR_WRPROT_ENABLE = 0x55;  // 用户寄存器可操作指令的一部分
static constexpr uint8_t USR_WRPROT_DISABLE = 0x00; // 用户寄存器只读指令的一部分

// 数组常量
static constexpr uint8_t CHANNEL_COUNT = 6;  // 通道数量

// 创建寄存器数组，方便通过通道索引访问
static constexpr uint8_t BL0906_I_RMS[CHANNEL_COUNT] = {
    BL0906_I_1_RMS, BL0906_I_2_RMS, BL0906_I_3_RMS,
    BL0906_I_4_RMS, BL0906_I_5_RMS, BL0906_I_6_RMS
};

static constexpr uint8_t BL0906_WATT[CHANNEL_COUNT] = {
    BL0906_WATT_1, BL0906_WATT_2, BL0906_WATT_3,
    BL0906_WATT_4, BL0906_WATT_5, BL0906_WATT_6
};

static constexpr uint8_t BL0906_CF_CNT[CHANNEL_COUNT] = {
    BL0906_CF_1_CNT, BL0906_CF_2_CNT, BL0906_CF_3_CNT,
    BL0906_CF_4_CNT, BL0906_CF_5_CNT, BL0906_CF_6_CNT
};

static constexpr uint8_t BL0906_RMSGN[CHANNEL_COUNT] = {
    BL0906_RMSGN_1, BL0906_RMSGN_2, BL0906_RMSGN_3,
    BL0906_RMSGN_4, BL0906_RMSGN_5, BL0906_RMSGN_6
};

static constexpr uint8_t BL0906_RMSOS[CHANNEL_COUNT] = {
    BL0906_RMSOS_1, BL0906_RMSOS_2, BL0906_RMSOS_3,
    BL0906_RMSOS_4, BL0906_RMSOS_5, BL0906_RMSOS_6
};

static constexpr uint8_t BL0906_CHGN[CHANNEL_COUNT] = {
    BL0906_CHGN_1, BL0906_CHGN_2, BL0906_CHGN_3,
    BL0906_CHGN_4, BL0906_CHGN_5, BL0906_CHGN_6
};

static constexpr uint8_t BL0906_CHOS[CHANNEL_COUNT] = {
    BL0906_CHOS_1, BL0906_CHOS_2, BL0906_CHOS_3,
    BL0906_CHOS_4, BL0906_CHOS_5, BL0906_CHOS_6
};

static constexpr uint8_t BL0906_WATTGN[CHANNEL_COUNT] = {
    BL0906_WATTGN_1, BL0906_WATTGN_2, BL0906_WATTGN_3,
    BL0906_WATTGN_4, BL0906_WATTGN_5, BL0906_WATTGN_6
};

static constexpr uint8_t BL0906_WATTOS[CHANNEL_COUNT] = {
    BL0906_WATTOS_1, BL0906_WATTOS_2, BL0906_WATTOS_3,
    BL0906_WATTOS_4, BL0906_WATTOS_5, BL0906_WATTOS_6
};

}  // namespace bl0906_factory
}  // namespace esphome 