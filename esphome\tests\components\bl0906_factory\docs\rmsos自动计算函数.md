### RMSOS寄存器自动计算函数

1. 此函数通过esphome的button组件手动调用。
2. 此函数通过当前各通道的电流值计算相应通道的RMSOS寄存器的值，然后写入RMSOS寄存器
3. RMSOS[n]=(-pow(current_raw[n],2)/256)*0.787

## 修改方案

### 概述
通过lambda调用方式实现RMSOS自动计算函数，无需添加额外的button组件。用户可以在YAML配置中通过lambda直接调用C++函数。

### 1. 头文件修改 (`bl0906_factory.h`)

#### 添加函数声明
在BL0906Factory类的public部分添加以下函数声明：

```cpp
// RMSOS自动计算函数（简化版）
void calculate_and_write_rmsos_all_channels();
```

### 2. 实现文件修改 (`bl0906_factory.cpp`)

#### 2.1 添加包含文件
在文件顶部添加数学库支持：
```cpp
#include <cmath>    // 已存在，用于pow函数
```

#### 2.2 实现RMSOS计算和写入函数（合并简化版本）
```cpp
void BL0906Factory::calculate_and_write_rmsos_all_channels() {
  ESP_LOGI(FACTORY_TAG, "开始RMSOS自动计算...");
  
  // 检查组件状态
  if (!this->is_ready()) {
    ESP_LOGE(FACTORY_TAG, "BL0906组件未就绪，无法执行RMSOS计算");
    return;
  }
  
  // 检查数据读取完整性
  if (!current_data_.read_complete) {
    ESP_LOGW(FACTORY_TAG, "数据读取未完成，使用当前可用数据进行计算");
  }
  
  // 2. 使用主循环已读取的电流原始值进行RMSOS计算
  bool success = true;
  ESP_LOGI(FACTORY_TAG, "使用主循环数据(时间戳: %u, 数据完整性: %s)", 
           current_data_.timestamp, current_data_.read_complete ? "完整" : "部分");
  
  for (int channel = 1; channel <= 6; channel++) {
    // 直接使用已读取的电流原始值
    int32_t current_raw = current_data_.channels[channel - 1].current_raw;
    
    // 检查数据有效性
    if (current_raw == 0) {
      ESP_LOGW(FACTORY_TAG, "通道%d电流原始值为0，可能表示无负载或读取异常", channel);
    }
    
    // 内联计算RMSOS值：RMSOS[n] = (-pow(current_raw[n], 2) / 256) * 0.787
    double current_squared = pow(static_cast<double>(current_raw), 2);
    double result = (-current_squared / 256.0) * 0.787;
    
    // 限制在16位有符号整数范围内
    if (result > 32767) result = 32767;
    if (result < -32768) result = -32768;
    int16_t rmsos_value = static_cast<int16_t>(result);
    
    ESP_LOGD(FACTORY_TAG, "通道%d计算: current_raw=%d, result=%.2f, rmsos_value=%d", 
             channel, current_raw, result, rmsos_value);
    
    // 内联写入RMSOS寄存器
    uint8_t address = BL0906_RMSOS[channel - 1]; // 通道1-6对应数组索引0-5
    if (!write_register_value(address, rmsos_value)) {
      ESP_LOGE(FACTORY_TAG, "写入通道%d RMSOS寄存器(0x%02X)失败", channel, address);
      success = false;
    } else {
      ESP_LOGI(FACTORY_TAG, "通道%d: 电流原始值=%d, RMSOS值=%d, 写入成功", 
               channel, current_raw, rmsos_value);
    }
    
    // 短暂延时避免连续写入过快
    delay(10);
  }
  
  // 3. 恢复写保护（如果原来是开启的）
  // 注意：这里可能需要根据实际情况决定是否重新开启写保护
  
  if (success) {
    ESP_LOGI(FACTORY_TAG, "RMSOS自动计算完成，所有通道处理成功");
    // 刷新所有校准数字组件显示
    refresh_all_calib_numbers();
  } else {
    ESP_LOGW(FACTORY_TAG, "RMSOS自动计算完成，但部分通道处理失败");
  }
}
```

### 3. YAML配置使用方法

#### 3.1 基本配置
在ESPHome YAML配置文件中，可以通过以下方式调用RMSOS计算函数：

```yaml
# 通过button组件调用
button:
  - platform: template
    name: "计算RMSOS"
    on_press:
      - lambda: |-
          auto bl0906 = id(your_bl0906_factory_id);
          bl0906->enqueue_action_(&bl0906_factory::BL0906Factory::calculate_and_write_rmsos_all_channels);

# 通过自动化调用
automation:
  - alias: "定时计算RMSOS"
    trigger:
      - platform: time
        at: "02:00:00"  # 每天凌晨2点执行
    action:
      - lambda: |-
          auto bl0906 = id(your_bl0906_factory_id);
          bl0906->enqueue_action_(&bl0906_factory::BL0906Factory::calculate_and_write_rmsos_all_channels);

# 通过API服务调用
api:
  services:
    - service: calculate_rmsos
      then:
        - lambda: |-
            auto bl0906 = id(your_bl0906_factory_id);
            bl0906->enqueue_action_(&bl0906_factory::BL0906Factory::calculate_and_write_rmsos_all_channels);
```

#### 3.2 高级用法
```yaml
# 结合条件判断
button:
  - platform: template
    name: "智能RMSOS计算"
    on_press:
      - lambda: |-
          auto bl0906 = id(your_bl0906_factory_id);
          // 检查是否所有通道都有电流
          bool has_current = false;
          for (int i = 0; i < 6; i++) {
            auto sensor = bl0906->get_sensor(bl0906_factory::BL0906Factory::SensorType::CURRENT, i);
            if (sensor && sensor->get_state() > 0.1) {
              has_current = true;
              break;
            }
          }
          
          if (has_current) {
            ESP_LOGI("main", "检测到电流，开始RMSOS计算");
            bl0906->enqueue_action_(&bl0906_factory::BL0906Factory::calculate_and_write_rmsos_all_channels);
          } else {
            ESP_LOGI("main", "未检测到电流，跳过RMSOS计算");
          }
```

### 4. 实施注意事项

#### 4.1 线程安全
- 直接使用`enqueue_action_`确保RMSOS计算在主循环中执行
- 避免在中断或其他线程中直接调用计算函数

#### 4.2 数据优化
- **使用已读取数据**：直接使用主循环中`current_data_`结构里的电流原始值，避免重复UART通信
- **数据时效性检查**：检查数据时间戳，确保使用的是较新的数据
- **数据完整性验证**：检查`current_data_.read_complete`标志确保数据的完整性

#### 4.3 错误处理
- 函数内部包含完整的错误检查和日志记录
- 计算失败时会记录详细的错误信息
- 对零电流值进行特殊提示（可能是无负载或读取异常）

#### 4.4 性能考虑
- **避免重复读取**：不再重新读取电流值，直接使用已有数据，大幅提升执行效率
- 计算过程中包含适当的延时避免过快的寄存器操作
- 使用异步队列避免阻塞主循环

#### 4.5 调试支持
- 提供详细的日志输出便于调试
- 记录数据来源和时效性信息
- 支持单独测试每个通道的计算结果

### 5. 测试验证

#### 5.1 功能测试
1. 确保各通道电流读取正常
2. 验证RMSOS计算公式的正确性
3. 测试寄存器写入功能
4. 验证lambda调用的响应性

#### 5.2 边界条件测试
1. 电流为0时的计算结果
2. 极大电流值的计算结果
3. 通道故障时的错误处理
4. 写保护状态下的处理
5. 数据时效性测试（使用过旧数据时的行为）
6. 数据不完整时的处理

## 优化亮点

### 性能优化
- **避免重复UART通信**：直接使用主循环已读取的`current_data_.channels[n].current_raw`值
- **减少执行时间**：无需等待6次UART读取操作，执行速度大幅提升  
- **降低通信错误风险**：减少UART操作次数，降低通信失败的可能性

### 代码简洁性
- **函数合并**：将原本4个函数合并为1个主函数，减少了函数调用开销
- **内联计算**：RMSOS计算和寄存器写入逻辑直接内联，代码更紧凑
- **减少头文件声明**：只需要声明一个主函数，简化了接口

### 数据一致性  
- **时间同步**：使用的是同一时间点读取的所有通道数据，保证数据一致性
- **完整性验证**：检查`read_complete`标志确保数据的完整性

### 调用简化
- **直接队列调用**：lambda中直接调用`enqueue_action_`，无需额外的包装函数
- **减少函数层级**：从原来的3层调用减少到1层，执行路径更直接

这个合并简化后的方案在保持所有功能的同时，大幅提升了代码的简洁性和执行效率。