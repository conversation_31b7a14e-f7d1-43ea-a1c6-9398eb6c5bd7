# BL0906Factory 电量统计功能实现方案（基于持久化CF_count）

## 概述
为BL0906Factory组件增加电量统计功能，支持每通道的昨日、今日、本周、本月、本年电量统计。通过ESPHome的时间组件获取日期时间信息，使用独立的电量统计类管理统计逻辑，**基于持久化保存的CF_count数据进行统计，而不是直接使用BL0906实时读取的CF_count，避免断电后CF_count清零对统计的影响**。昨日电量为固定值，只在持久化时更新统计数据。

## 核心技术改进：解决断电数据丢失问题

### 问题分析
**原始问题**：BL0906芯片在断电后，所有内部寄存器包括CF_count脉冲计数器都会清零重置。如果电量统计直接基于CF_count进行周期计算，断电重启后会导致统计数据不连续或错误。

### 解决方案
**数据源优化**：将电量统计的数据源从BL0906实时读取的CF_count改为BL0906Factory已持久化保存的CF_count数据。通过维护持久化的累计CF_count，确保断电重启后统计数据的连续性。

#### 技术优势对比

| 方面 | 基于实时CF_count | 基于持久化CF_count |
|------|----------------|------------------|
| **断电影响** | ❌ BL0906 CF_count清零，统计中断 | ✅ 持久化CF_count保持，统计连续 |
| **数据来源** | ❌ 依赖BL0906硬件寄存器状态 | ✅ 基于软件持久化存储 |
| **计算方式** | ❌ 直接使用硬件计数值 | ✅ 使用软件维护的累计计数值 |
| **系统依赖** | ❌ 依赖BL0906硬件状态 | ✅ 基于可靠的Flash存储 |
| **恢复能力** | ❌ 断电后需要重新积累 | ✅ 立即恢复到断电前状态 |
| **数据精度** | ✅ 保持CF_count的整数精度 | ✅ 保持CF_count的整数精度 |

#### 实现核心
```cpp
// 原方案：基于BL0906实时CF_count差值
uint32_t current_cf_count = read_from_bl0906(BL0906_CF_CNT[channel]);
uint32_t count_diff = current_cf_count - start_cf_count;
float energy = count_diff / Ke;  // BL0906断电后current_cf_count从0开始

// 新方案：基于持久化CF_count差值  
uint32_t persistent_cf_count = get_persistent_cf_count(channel);  // 从持久化数据获取
uint32_t count_diff = persistent_cf_count - start_cf_count;
float energy = count_diff / Ke;  // 持久化数据不受断电影响
```

这种改进确保了即使BL0906芯片断电重启，电量统计功能也能通过持久化的CF_count数据完全恢复，提供连续准确的周期电量数据。

### 核心实现机制
1. **双重CF_count维护**：
   - `last_cf_count[]`：从BL0906硬件读取的实时CF_count（断电清零）
   - `persistent_cf_count[]`：软件维护的累计CF_count（持久化保存）

2. **增量更新策略**：
   ```cpp
   // 检测到CF_count增量时
   uint32_t cf_increment = current_hardware_cf_count - last_cf_count[channel];
   persistent_cf_count[channel] += cf_increment;  // 累加到持久化CF_count
   ```

3. **统计数据来源**：
   - 统计计算完全基于`persistent_cf_count[]`
   - 不依赖BL0906硬件寄存器状态
   - 断电重启后持久化数据自动恢复

这样既保持了传统CF_count计算方式，又解决了断电数据丢失问题。

## 1. 架构设计

### 1.1 独立的电量统计类
创建一个专门的 `EnergyStatisticsManager` 类来处理所有电量统计逻辑，与BL0906Factory解耦：

```cpp
// 前向声明
class BL0906Factory;

class EnergyStatisticsManager {
public:
  EnergyStatisticsManager(BL0906Factory* parent);
  
  // 初始化和设置
  void setup();
  void set_time_component(time::RealTimeClock *time_comp);
  
  // 核心功能
  void update_persistent_cf_count(int channel, uint32_t cf_count_increment);
  void update_persistent_cf_count_sum(uint32_t cf_count_increment);
  void check_period_changes();
  void update_sensors_on_save();  // 只在保存时更新传感器
  
  // 传感器管理
  void set_sensor(SensorType type, sensor::Sensor *sensor, int channel = 0);
  
private:
  BL0906Factory* parent_;
  time::RealTimeClock *time_component_{nullptr};
  
  // 统计数据和传感器
  // ... 详见下文
};
```

### 1.2 基于持久化CF_count的统计方案
**核心改进：使用软件维护的持久化CF_count数据作为统计基础，而不是直接使用BL0906实时读取的CF_count。**

**优势说明：**
- **断电保护**：BL0906断电后硬件CF_count会清零，但软件维护的持久化CF_count不受影响
- **数据连续性**：基于持久化CF_count的统计数据在设备重启后仍然准确
- **计算一致性**：保持基于CF_count的传统计算方式，但数据来源更可靠

只存储关键时间点的持久化CF_count值，周期电量通过CF_count差值计算。统计管理器的CF_count只在持久化或日期变更时更新：

```cpp
// 优化的时间点快照数据结构（内存优化版）
struct CompactTimeSnapshot {
  time_t timestamp;                    // 使用ESPTime的timestamp字段
  uint16_t year;                       // 年份（用于跨年检查）
  uint8_t month;                       // 月份（1-12）
  uint8_t day_of_month;                // 日期（1-31）
  uint8_t day_of_week;                 // 星期（0=Sunday）
};

// 基于持久化CF_count的统计数据结构
struct OptimizedEnergyStatistics {
  // 所有通道共享的时间戳（减少重复存储）
  CompactTimeSnapshot period_times[5]; // [0]昨日开始 [1]今日开始 [2]本周开始 [3]本月开始 [4]本年开始
  
  // 各通道的持久化CF_count快照 [通道][周期]
  uint32_t period_persistent_cf_count[7][5];     // [0-5]通道+[6]总和，[0-4]对应上述5个周期
  
  // 当前状态（仅在持久化或日期变更时更新）
  uint32_t current_persistent_cf_count[7];       // [0-5]为6个通道，[6]为总和
  time_t last_update_timestamp;                  // 上次更新时间戳
  
  // 线程安全标志
  std::atomic<bool> updating{false};             // 原子操作标志，防止并发更新
};
```

## 2. 数据结构设计

### 2.1 新增枚举类型
```cpp
// 电量统计周期枚举
enum class EnergyPeriod {
  YESTERDAY,
  TODAY,
  THIS_WEEK,
  THIS_MONTH,
  THIS_YEAR
};

// 扩展传感器类型枚举（在BL0906Factory中）
enum class SensorType {
  // ... 原有类型 ...
  YESTERDAY_ENERGY,        // 昨日电量（各通道）
  TODAY_ENERGY,           // 今日电量（各通道）
  WEEK_ENERGY,            // 本周电量（各通道）
  MONTH_ENERGY,           // 本月电量（各通道）
  YEAR_ENERGY,            // 本年电量（各通道）
  YESTERDAY_TOTAL_ENERGY, // 昨日总电量
  TODAY_TOTAL_ENERGY,     // 今日总电量
  WEEK_TOTAL_ENERGY,      // 本周总电量
  MONTH_TOTAL_ENERGY,     // 本月总电量
  YEAR_TOTAL_ENERGY       // 本年总电量
};
```

### 2.2 持久化数据结构
```cpp
// 基于持久化CF_count的数据结构（内存优化+线程安全）
struct OptimizedEnergyPersistenceData {
  // 基础累计电量数据（用于实时显示）
  float total_energy[7];           // [0-5]为各通道，[6]为总和 (单位: kWh)
  
  // 持久化CF_count数据（统计功能的核心数据源）
  uint32_t persistent_cf_count[7]; // [0-5]为各通道，[6]为总和 - 软件维护的累计CF_count
  
  // BL0906硬件CF_count相关数据（用于实时电量计算和增量更新）
  uint32_t last_cf_count[7];       // [0-5]为各通道，[6]为总和 - 上次读取的硬件CF_count
  bool cf_count_initialized[7];    // cf_count是否已初始化
  
  // 优化的统计数据（基于持久化CF_count，所有通道统一管理）
  OptimizedEnergyStatistics unified_statistics;  // 统一的统计数据结构
  
  uint32_t checksum;               // 数据校验和
};
```

## 3. EnergyStatisticsManager 类详细设计

### 3.1 类成员变量
```cpp
class EnergyStatisticsManager {
private:
  BL0906Factory* parent_;
  time::RealTimeClock *time_component_{nullptr};
  
  // 线程安全的互斥锁
  mutable std::mutex statistics_mutex_;
  
  // 优化的统计数据（统一管理所有通道）
  OptimizedEnergyStatistics unified_statistics_;
  
  // 各通道统计传感器指针
  sensor::Sensor *yesterday_energy_sensors_[7]{nullptr};  // [0-5]通道，[6]总和
  sensor::Sensor *today_energy_sensors_[7]{nullptr};
  sensor::Sensor *week_energy_sensors_[7]{nullptr};
  sensor::Sensor *month_energy_sensors_[7]{nullptr};
  sensor::Sensor *year_energy_sensors_[7]{nullptr};
  
  // 状态管理
  std::atomic<bool> initialized_{false};
  std::atomic<time_t> last_check_timestamp_{0};  // 上次检查的时间戳
  
  // 当前持久化CF_count存储（从BL0906Factory获取）- 使用原子操作确保线程安全
  std::atomic<uint32_t> current_persistent_cf_count_[7];  // 当前的持久化CF_count值
};
```

### 3.2 核心方法
```cpp
public:
  // 初始化
  void setup();
  void set_time_component(time::RealTimeClock *time_comp) { time_component_ = time_comp; }
  
  // 数据更新（由BL0906Factory调用）
  void update_from_cf_count_increment(int channel, uint32_t cf_count_increment);
  void update_from_cf_count_sum_increment(uint32_t cf_count_increment);
  void update_statistics_on_save();     // 在持久化时更新统计数据
  void update_statistics_on_date_change();  // 在日期变更时更新统计数据
  
  // 周期检查（在BL0906Factory的update中调用）
  void check_period_changes();
  
  // 传感器更新（只在持久化时调用）
  void update_sensors_on_save();
  
  // 传感器管理
  void set_sensor(SensorType type, sensor::Sensor *sensor, int channel = 0);

private:
  // 时间管理（基于ESPTime优化）
  ESPTime get_current_time() const;
  bool is_time_valid() const;
  CompactTimeSnapshot create_time_snapshot(const ESPTime &time) const;
  bool is_new_day(const ESPTime &last_time, const ESPTime &current_time) const;
  bool is_new_week(const ESPTime &last_time, const ESPTime &current_time) const;
  bool is_new_month(const ESPTime &last_time, const ESPTime &current_time) const;
  bool is_new_year(const ESPTime &last_time, const ESPTime &current_time) const;
  
  // 周期处理
  void handle_new_day();
  void handle_new_week();
  void handle_new_month();
  void handle_new_year();
  
  // 快照管理（线程安全）
  void create_current_snapshot_safe();
  void update_period_start_snapshot_safe(EnergyPeriod period);
  
  // 电量计算
  float calculate_energy_for_period(int channel, EnergyPeriod period) const;
  float calculate_total_energy_for_period(EnergyPeriod period) const;
  
  // 数据持久化（通过parent_调用）- 线程安全版本
  void save_statistics_data_safe();
  void load_statistics_data_safe();
  
  // 线程安全的数据访问方法
  void get_statistics_data_safe(OptimizedEnergyStatistics& out_data) const;
  void set_statistics_data_safe(const OptimizedEnergyStatistics& in_data);
};
```

## 4. BL0906Factory 类修改

### 4.1 新增成员变量
```cpp
private:
  // 电量统计管理器
  std::unique_ptr<EnergyStatisticsManager> energy_stats_manager_;
  
  // 统计功能开关
  bool energy_statistics_enabled_{false};
```

### 4.2 新增公共方法
```cpp
public:
  // 设置统计功能
  void set_energy_statistics_enabled(bool enabled) { energy_statistics_enabled_ = enabled; }
  void set_time_component(time::RealTimeClock *time_comp);
  
  // 传感器设置（扩展现有方法）
  void set_sensor(SensorType type, sensor::Sensor *sensor, int channel = 0);
  
  // 获取统计管理器（用于高级配置）
  EnergyStatisticsManager* get_energy_statistics_manager() const {
    return energy_stats_manager_.get();
  }
```

### 4.3 修改现有方法
```cpp
// setup()方法修改
void BL0906Factory::setup() {
  // ... 原有初始化代码 ...
  
  // 初始化电量统计管理器
  if (energy_statistics_enabled_) {
    energy_stats_manager_ = std::make_unique<EnergyStatisticsManager>(this);
    energy_stats_manager_->setup();
  }
  
  // ... 其余代码 ...
}

// update()方法修改
void BL0906Factory::update() {
  // ... 原有代码 ...
  
  // 检查统计周期变化（不更新传感器）
  if (energy_statistics_enabled_ && energy_stats_manager_) {
    energy_stats_manager_->check_period_changes();
  }
}

// read_data_()方法修改
void BL0906Factory::read_data_(uint8_t address, float reference, sensor::Sensor *sensor) {
  // ... 原有代码 ...
  
  // 处理CF_count增量更新持久化数据（不立即更新统计数据）
  if (energy_statistics_enabled_ && energy_stats_manager_) {
    // 检查是否为脉冲计数寄存器，计算增量并更新持久化CF_count
    for (int i = 0; i < CHANNEL_COUNT; i++) {
      if (address == BL0906_CF_CNT[i]) {
        uint32_t current_cf_count = static_cast<uint32_t>(raw);
        if (cf_count_initialized_[i] && current_cf_count >= last_cf_count_[i]) {
          uint32_t cf_increment = current_cf_count - last_cf_count_[i];
          if (cf_increment > 0) {
            energy_stats_manager_->update_from_cf_count_increment(i, cf_increment);
          }
        }
        break;
      }
    }
    
    // 检查是否为总脉冲计数寄存器
    if (address == BL0906_CF_SUM_CNT) {
      uint32_t current_cf_count = static_cast<uint32_t>(raw);
      if (cf_count_sum_initialized_ && current_cf_count >= last_cf_count_sum_) {
        uint32_t cf_increment = current_cf_count - last_cf_count_sum_;
        if (cf_increment > 0) {
          energy_stats_manager_->update_from_cf_count_sum_increment(cf_increment);
        }
      }
    }
  }
  
  // ... 原有传感器更新代码 ...
}

// save_energy_data()方法修改
void BL0906Factory::save_energy_data() {
  if (!energy_persistence_enabled_) {
    return;
  }
  
  // 在保存前更新统计数据
  if (energy_statistics_enabled_ && energy_stats_manager_) {
    energy_stats_manager_->update_statistics_on_save();
  }
  
  // ... 原有保存代码 ...
  
  // 在保存时更新统计传感器
  if (energy_statistics_enabled_ && energy_stats_manager_) {
    energy_stats_manager_->update_sensors_on_save();
  }
}
```

## 5. 电量计算逻辑

### 5.1 基于持久化CF_count差值的计算（线程安全优化版）
```cpp
float EnergyStatisticsManager::calculate_energy_for_period(int channel, EnergyPeriod period) const {
  if (channel < 0 || channel >= 7) return 0.0f;
  
  // 线程安全地读取统计数据
  std::lock_guard<std::mutex> lock(statistics_mutex_);
  
  uint32_t start_cf_count = 0;
  uint32_t current_cf_count = unified_statistics_.current_persistent_cf_count[channel];
  
  // 获取对应周期的起始持久化CF_count
  switch (period) {
    case EnergyPeriod::YESTERDAY:
      // 昨日电量 = (今日开始CF_count - 昨日开始CF_count) / Ke
      start_cf_count = unified_statistics_.period_persistent_cf_count[channel][0];  // 昨日开始
      current_cf_count = unified_statistics_.period_persistent_cf_count[channel][1]; // 今日开始
      break;
    case EnergyPeriod::TODAY:
      start_cf_count = unified_statistics_.period_persistent_cf_count[channel][1];   // 今日开始
      break;
    case EnergyPeriod::THIS_WEEK:
      start_cf_count = unified_statistics_.period_persistent_cf_count[channel][2];   // 本周开始
      break;
    case EnergyPeriod::THIS_MONTH:
      start_cf_count = unified_statistics_.period_persistent_cf_count[channel][3];   // 本月开始
      break;
    case EnergyPeriod::THIS_YEAR:
      start_cf_count = unified_statistics_.period_persistent_cf_count[channel][4];   // 本年开始
      break;
  }
  
  // 计算CF_count差值
  uint32_t count_diff;
  if (current_cf_count >= start_cf_count) {
    count_diff = current_cf_count - start_cf_count;
  } else {
    // 处理软件维护的CF_count溢出（使用32位范围）
    count_diff = (0xFFFFFFFF - start_cf_count) + current_cf_count + 1;
    ESP_LOGD("energy_stats", "通道%d检测到CF_count溢出: %u -> %u", 
             channel + 1, start_cf_count, current_cf_count);
  }
  
  // 转换为电量（kWh）
  constexpr float Ke = 1638.4f;  // BL0906的电量转换系数
  float period_energy = count_diff / Ke;
  
  // 确保电量值不为负（理论上不应该发生，但作为安全保护）
  if (period_energy < 0.0f) {
    ESP_LOGW("energy_stats", "通道%d的%d周期电量为负值: %.6f，重置为0", 
             channel + 1, static_cast<int>(period), period_energy);
    period_energy = 0.0f;
  }
  
  return period_energy;
}
```

**核心优势改进：**
- **持久化保护**：基于软件维护的持久化CF_count，断电重启后数据不丢失
- **计算一致性**：保持传统的CF_count转换电量的计算方式
- **溢出处理**：处理软件CF_count的32位溢出情况
- **数据可靠**：不依赖BL0906硬件状态，基于可靠的Flash存储

### 5.2 周期变化处理（基于ESPTime优化+线程安全）
```cpp
void EnergyStatisticsManager::handle_new_day() {
  ESP_LOGI("energy_stats", "检测到新的一天");
  
  // 线程安全地更新统计数据
  std::lock_guard<std::mutex> lock(statistics_mutex_);
  
  // 防止并发更新
  if (unified_statistics_.updating.exchange(true)) {
    ESP_LOGW("energy_stats", "统计数据正在更新中，跳过本次处理");
    return;
  }
  
  // 更新统计数据（使用最新的临时cf_count）
  update_statistics_on_date_change();
  
  // 创建当前时刻的时间快照
  ESPTime current_time = get_current_time();
  CompactTimeSnapshot time_snapshot = create_time_snapshot(current_time);
  
  // 更新时间点快照（所有通道共享）
  // 昨日开始 = 之前的今日开始
  unified_statistics_.period_times[0] = unified_statistics_.period_times[1]; // 昨日开始
  // 今日开始 = 当前时刻
  unified_statistics_.period_times[1] = time_snapshot; // 今日开始
  
  // 更新各通道的持久化CF_count快照
  for (int channel = 0; channel < 7; channel++) {
    // 昨日开始CF_count = 之前的今日开始CF_count
    unified_statistics_.period_persistent_cf_count[channel][0] = unified_statistics_.period_persistent_cf_count[channel][1];
    // 今日开始CF_count = 当前持久化CF_count
    unified_statistics_.period_persistent_cf_count[channel][1] = current_persistent_cf_count_[channel].load();
  }
  
  // 更新完成标志
  unified_statistics_.updating.store(false);
  
  // 保存数据
  save_statistics_data_safe();
}

// 基于ESPTime的周期判断方法实现
bool EnergyStatisticsManager::is_new_day(const ESPTime &last_time, const ESPTime &current_time) const {
  return (last_time.day_of_month != current_time.day_of_month) ||
         (last_time.month != current_time.month) ||
         (last_time.year != current_time.year);
}

bool EnergyStatisticsManager::is_new_week(const ESPTime &last_time, const ESPTime &current_time) const {
  // 使用day_of_year和year来判断是否跨周
  // 如果是新的一年，或者day_of_year的差值跨越了周边界
  if (last_time.year != current_time.year) {
    return true;  // 跨年必然是新周
  }
  
  // 计算上次时间是周几（0=周日）
  int last_week_start_day = last_time.day_of_year - last_time.day_of_week;
  int current_week_start_day = current_time.day_of_year - current_time.day_of_week;
  
  return last_week_start_day != current_week_start_day;
}

bool EnergyStatisticsManager::is_new_month(const ESPTime &last_time, const ESPTime &current_time) const {
  return (last_time.month != current_time.month) || (last_time.year != current_time.year);
}

bool EnergyStatisticsManager::is_new_year(const ESPTime &last_time, const ESPTime &current_time) const {
  return last_time.year != current_time.year;
}

CompactTimeSnapshot EnergyStatisticsManager::create_time_snapshot(const ESPTime &time) const {
  CompactTimeSnapshot snapshot;
  snapshot.timestamp = time.timestamp;
  snapshot.year = time.year;
  snapshot.month = time.month;
  snapshot.day_of_month = time.day_of_month;
  snapshot.day_of_week = time.day_of_week;
  return snapshot;
}
```

## 6. 持久化存储

### 6.1 统计数据持久化
```cpp
// 在BL0906Factory中添加
private:
  // 扩展的preferences对象
  ESPPreferenceObject energy_pref_with_stats_;
  
  // 数据管理
  void setup_energy_persistence_with_stats();
  void save_energy_data_with_stats();
  void load_energy_data_with_stats();
  uint32_t calculate_checksum_with_stats(const EnergyPersistenceDataWithStats& data) const;
```

### 6.2 数据加载和保存
```cpp
void BL0906Factory::save_energy_data_with_stats() {
  if (!energy_persistence_enabled_) {
    return;
  }

  OptimizedEnergyPersistenceData data;
  
  // 保存基础累计电量数据（用于实时显示）
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    data.total_energy[i] = total_energy_[i];
  }
  data.total_energy[6] = total_energy_sum_;              // 总和放在第7个元素
  
  // 保存持久化CF_count数据（统计功能的主要数据源）
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    data.persistent_cf_count[i] = persistent_cf_count_[i];
  }
  data.persistent_cf_count[6] = persistent_cf_count_sum_;
  
  // 保存BL0906硬件CF_count数据（用于增量计算）
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    data.last_cf_count[i] = last_cf_count_[i];
    data.cf_count_initialized[i] = cf_count_initialized_[i];
  }
  data.last_cf_count[6] = last_cf_count_sum_;
  data.cf_count_initialized[6] = cf_count_sum_initialized_;
  
  // 保存统计数据（线程安全）
  if (energy_statistics_enabled_ && energy_stats_manager_) {
    // 线程安全地获取统计数据
    energy_stats_manager_->get_statistics_data_safe(data.unified_statistics);
  }
  
  // 计算校验和
  data.checksum = calculate_checksum_with_stats(data);
  
  // 保存到flash
  bool save_success = energy_pref_with_stats_.save(&data);
  
  if (save_success) {
    ESP_LOGI(FACTORY_TAG, "✅ 电量数据（含统计）保存成功");
    last_save_time_ = millis();
  } else {
    ESP_LOGE(FACTORY_TAG, "❌ 保存电量数据失败！");
  }
}
```

## 7. 简化的实现要点

### 7.1 昨日电量处理
- **固定值策略**：昨日电量在日期变化时确定，之后不再更新
- **存储方式**：存储昨日开始和今日开始时的cf_count快照
- **计算方式**：昨日电量 = (今日开始cf_count - 昨日开始cf_count) / Ke

### 7.2 统计数据更新策略（内存优化+线程安全）
- **延迟更新**：只在持久化保存或日期变更时才更新统计数据的cf_count
- **临时存储**：平时只临时存储cf_count，不立即更新统计结构，使用原子操作确保线程安全
- **减少计算频率**：避免实时计算，降低CPU负担
- **批量更新**：统一更新所有统计传感器
- **内存优化**：所有通道共享时间戳信息，使用紧凑的数据结构
- **线程安全**：使用互斥锁保护关键数据访问，原子操作防止数据竞争

### 7.3 时间处理（基于ESPTime优化）
- **利用ESPTime结构**：直接使用ESPTime的day_of_month、month、year、day_of_week、day_of_year字段进行周期判断
- **精确的周期检测**：基于日历日期进行准确的日/周/月/年变化检测，解决跨年跨月等边界问题
- **不处理时区变化**：假设设备时区固定
- **不处理夏令时**：使用本地时间，不考虑夏令时切换

### 7.4 错误处理
- **时间无效处理**：时间无效时暂停统计功能
- **数据校验**：加载数据时验证完整性
- **溢出处理**：处理cf_count计数器溢出

## 8. YAML配置支持

### 8.1 配置示例
```yaml
time:
  - platform: homeassistant
    id: ha_time

bl0906_factory:
  # ... 现有配置 ...
  
  # 启用电量统计功能
  energy_statistics: true
  time_id: ha_time
  
  # 各通道统计传感器（可选）
  today_energy:
    - name: "通道1今日电量"
    - name: "通道2今日电量"
    # ... 其他通道
  
  yesterday_energy:
    - name: "通道1昨日电量"
    - name: "通道2昨日电量"
    # ... 其他通道
  
  # 总电量统计传感器（可选）
  today_total_energy:
    name: "今日总电量"
  
  yesterday_total_energy:
    name: "昨日总电量"
```

## 9. 核心优化实现细节

### 9.1 内存使用优化
通过以下策略显著减少内存占用：

#### 统一的时间管理
```cpp
// 原设计：每个通道都保存完整的时间信息（7个通道 × 5个周期 × 时间结构）
// 优化后：所有通道共享时间信息
struct OptimizedEnergyStatistics {
  // 所有通道共享的时间戳（只存一份）
  CompactTimeSnapshot period_times[5]; // 昨日、今日、本周、本月、本年开始时间
  
  // 各通道只存储持久化CF_count数值 [通道][周期]
  uint32_t period_persistent_cf_count[7][5];     // 7个通道（含总和） × 5个周期
  
  uint32_t current_persistent_cf_count[7];       // 当前持久化CF_count
  time_t last_update_timestamp;        // 上次更新时间
  std::atomic<bool> updating{false};   // 原子标志
};
```

#### 紧凑的时间快照结构
```cpp
// 精简的时间快照，只保存必要字段
struct CompactTimeSnapshot {
  time_t timestamp;       // 8字节 - Unix时间戳
  uint16_t year;         // 2字节 - 年份
  uint8_t month;         // 1字节 - 月份(1-12)
  uint8_t day_of_month;  // 1字节 - 日期(1-31)
  uint8_t day_of_week;   // 1字节 - 星期(0-6)
  // 总计：13字节，而不是完整的ESPTime结构
};
```

#### 内存占用对比
- **原设计**：7通道 × 5周期 × ESPTime结构 ≈ 1KB+
- **优化后**：5个时间快照(65字节) + 7×5个uint32(140字节) ≈ 205字节
- **节省约80%的内存**
- **新增优势**：使用持久化CF_count，断电后数据保持连续性

### 9.2 线程安全优化
通过以下机制确保多线程环境下的数据安全：

#### 分层的线程安全策略
```cpp
class EnergyStatisticsManager {
private:
  // 1. 互斥锁保护复杂操作
  mutable std::mutex statistics_mutex_;
  
  // 2. 原子操作保护简单数据
  std::atomic<bool> initialized_{false};
  std::atomic<time_t> last_check_timestamp_{0};
  std::atomic<uint32_t> temp_cf_count_[7];  // 临时cf_count存储
  
  // 3. 原子标志防止并发更新
  // （在OptimizedEnergyStatistics结构中的updating标志）
};
```

#### 线程安全的操作模式
```cpp
// 读取操作：使用共享锁或原子读取
float get_energy_value(int channel, EnergyPeriod period) const {
  std::lock_guard<std::mutex> lock(statistics_mutex_);
  return calculate_energy_for_period(channel, period);
}

// 写入操作：使用独占锁+原子标志
void update_statistics_on_save() {
  std::lock_guard<std::mutex> lock(statistics_mutex_);
  
  // 防止并发更新
  if (unified_statistics_.updating.exchange(true)) {
    return; // 已有更新在进行中
  }
  
  // 执行更新操作...
  
  unified_statistics_.updating.store(false);
}

  // 当前持久化CF_count存储：纯原子操作
void update_persistent_cf_count(int channel, uint32_t increment) {
  current_persistent_cf_count_[channel].fetch_add(increment);
}
```

#### 锁粒度优化
- **粗粒度锁**：保护整个统计数据结构的复杂操作
- **原子操作**：保护单个数据的简单读写
- **无锁设计**：当前持久化CF_count存储使用原子数组

### 9.3 基于ESPTime的周期计算优化
利用ESPTime结构的丰富字段实现精确的周期判断：

```cpp
bool EnergyStatisticsManager::is_new_week(const ESPTime &last_time, const ESPTime &current_time) const {
  // 跨年处理
  if (last_time.year != current_time.year) {
    return true;
  }
  
  // 使用day_of_year和day_of_week精确计算周边界
  int last_week_start = last_time.day_of_year - last_time.day_of_week;
  int current_week_start = current_time.day_of_year - current_time.day_of_week;
  
  return last_week_start != current_week_start;
}

bool EnergyStatisticsManager::is_new_month(const ESPTime &last_time, const ESPTime &current_time) const {
  return (last_time.month != current_time.month) || (last_time.year != current_time.year);
}

bool EnergyStatisticsManager::is_new_year(const ESPTime &last_time, const ESPTime &current_time) const {
  return last_time.year != current_time.year;
}
```

这种设计优势：
- **准确性**：正确处理跨年、跨月、闰年等边界情况
- **简洁性**：直接使用ESPTime字段，无需复杂的时间计算
- **效率**：避免了时间戳转换和日期计算的开销

## 10. 实施优先级（简化版）

### 第一阶段（核心功能）
1. 创建EnergyStatisticsManager类
2. 实现基于cf_count的电量计算
3. 实现日期变化检测和处理
4. 基本的今日/昨日统计功能

### 第二阶段（扩展功能）
1. 添加周/月/年统计支持
2. 完善数据持久化
3. 传感器管理完善
4. YAML配置支持

### 第三阶段（优化完善）
1. 性能优化
2. 错误处理完善
3. 测试和验证
4. 文档完善

## 10. 优势总结

### 10.1 架构优势
- **职责分离**：统计逻辑与主要功能解耦
- **易于维护**：独立的类便于测试和修改
- **可扩展性**：容易添加新的统计功能

### 10.2 算法优势
- **断电保护**：基于软件维护的持久化CF_count，BL0906断电后硬件CF_count清零不影响统计
- **数据连续性**：持久化CF_count数据在设备重启后仍然准确，统计结果不中断
- **内存效率**：所有通道共享时间戳信息，使用紧凑的数据结构，大幅减少内存占用
- **计算一致性**：保持传统CF_count转换电量的方式，但数据来源更可靠
- **增量更新**：基于CF_count增量累加，避免数据丢失
- **延迟更新**：减少不必要的计算和存储操作
- **结构优化**：统一管理所有通道的统计数据，避免重复存储
- **存储可靠**：持久化CF_count不受硬件状态影响，基于Flash存储
- **线程安全**：原子操作和互斥锁确保多线程环境下的数据一致性
- **周期计算精确**：基于ESPTime结构进行准确的日历周期判断

### 10.3 实现优势
- **断电恢复**：基于持久化CF_count，系统重启后统计功能完全恢复
- **配置灵活**：可选择启用统计功能
- **性能友好**：最小化对主要功能的影响，使用原子操作减少锁竞争
- **更新高效**：只在持久化时更新传感器，减少计算频率
- **线程安全**：支持多线程环境，防止数据竞争和不一致
- **内存优化**：统一的数据结构减少内存碎片和占用
- **数据可靠**：基于软件维护的持久化CF_count，不依赖易失性的硬件寄存器
- **计算兼容**：保持CF_count转换电量的传统方式，与现有系统兼容 