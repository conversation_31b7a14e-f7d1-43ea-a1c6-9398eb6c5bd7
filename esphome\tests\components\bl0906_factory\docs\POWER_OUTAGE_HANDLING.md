# BL0906断电重启处理机制

## 问题描述

当设备断电重启时，BL0906芯片内部的脉冲计数寄存器(CF_CNT)会被清零，这会影响到累计电量的准确性。如果不正确处理这种情况，可能导致：

1. **电量丢失**: 断电期间的电量消耗无法统计
2. **计算错误**: 重启后的脉冲计数从0开始，与保存的历史计数值不匹配
3. **数据异常**: 出现负电量或异常大的电量增量

## 解决方案

### 1. 芯片重启检测算法

我们实现了智能的芯片重启检测机制：

```cpp
// 检测是否发生了芯片重启（计数值明显减少）
if (current_count < last_cf_count_[i] && 
    (last_cf_count_[i] - current_count) > (last_cf_count_[i] / 2)) {
    // 可能是芯片重启，重新初始化基准值
    ESP_LOGW(FACTORY_TAG, "检测到通道%d芯片重启，重新初始化脉冲计数: %u -> %u", 
             i+1, last_cf_count_[i], current_count);
    last_cf_count_[i] = current_count;
}
```

**检测逻辑**:
- 如果当前计数值小于上次保存的计数值
- 且差值超过上次计数值的50%
- 则判定为芯片重启，重新初始化基准值

### 2. 初始化状态管理

增加了初始化状态标志来区分首次启动和正常运行：

```cpp
bool cf_count_initialized_[CHANNEL_COUNT]{false};   // 脉冲计数是否已初始化
bool cf_count_sum_initialized_{false};              // 总脉冲计数是否已初始化
```

**工作流程**:
1. **首次启动**: 将当前脉冲计数设为基准值，不计算电量增量
2. **正常运行**: 计算脉冲增量并累加电量
3. **重启检测**: 检测到重启后重新设置基准值

### 3. 数据持久化增强

扩展了持久化数据结构以保存初始化状态：

```cpp
struct EnergyPersistenceData {
  float total_energy[6];                    // 各通道累计电量 (kWh)
  float total_energy_sum;                   // 总累计电量 (kWh)
  uint32_t last_cf_count[6];                // 上次读取的脉冲计数
  uint32_t last_cf_count_sum;               // 上次读取的总脉冲计数
  bool cf_count_initialized[6];             // 脉冲计数是否已初始化
  bool cf_count_sum_initialized;            // 总脉冲计数是否已初始化
  uint32_t device_boot_time;                // 设备启动时间
  uint32_t save_timestamp;                  // 保存时间戳
  uint32_t checksum;                        // 数据校验和
};
```

## 处理流程详解

### 1. 设备启动时

```
设备启动 → 加载持久化数据 → 恢复累计电量和初始化状态
```

### 2. 首次读取脉冲计数

```
读取CF_CNT → 检查初始化状态 → 设置基准值 → 标记已初始化
```

### 3. 正常运行时

```
读取CF_CNT → 计算增量 → 累加电量 → 更新基准值
```

### 4. 检测到重启时

```
读取CF_CNT → 检测重启 → 重新设置基准值 → 继续正常运行
```

## 关键技术点

### 1. 重启检测阈值

使用50%作为重启检测阈值的原因：
- **避免误判**: 正常的脉冲计数溢出通常不会导致如此大的减少
- **快速检测**: 能够快速识别芯片重启情况
- **容错性**: 对于异常的计数跳变有一定的容错能力

### 2. 电量计算精度

```cpp
float energy_increment = pulse_increment * reference / 3600000.0f; // 转换为kWh
```

- `pulse_increment`: 脉冲增量
- `reference`: 校准系数(Ke)
- `3600000.0f`: 转换系数(秒到小时 × 1000)

### 3. 异常处理

对于无法明确判断的异常情况：
```cpp
// 计数值减少但不是重启，可能是溢出，暂时跳过
ESP_LOGD(FACTORY_TAG, "通道%d脉冲计数异常: %u -> %u", 
         i+1, last_cf_count_[i], current_count);
last_cf_count_[i] = current_count;
```

## 实际应用场景

### 1. 正常断电重启

**场景**: 设备正常断电，BL0906芯片重启
**处理**: 检测到计数值从大数变为小数，重新初始化基准值
**结果**: 累计电量保持不变，从新的基准值开始计算

### 2. 芯片复位

**场景**: 仅BL0906芯片复位，ESP32未重启
**处理**: 检测到脉冲计数异常减少，重新初始化
**结果**: 无电量丢失，继续正常累计

### 3. 计数器溢出

**场景**: 32位计数器溢出(极少见)
**处理**: 检测到异常但不满足重启条件，更新基准值
**结果**: 跳过当次计算，下次正常

## 日志监控

系统提供详细的日志信息用于监控和调试：

```
[I][bl0906_factory:507] 通道1脉冲计数初始化: 0
[V][bl0906_factory:522] 通道1电量增量: 0.001234 kWh, 累计: 1.234 kWh
[W][bl0906_factory:513] 检测到通道1芯片重启，重新初始化脉冲计数: 1000 -> 0
[D][bl0906_factory:528] 通道1脉冲计数异常: 1000 -> 800
```

## 注意事项

### 1. 断电期间电量丢失

**限制**: 无法统计断电期间的电量消耗
**原因**: BL0906芯片断电时停止工作
**建议**: 使用UPS或备用电源确保关键时段的连续监控

### 2. 重启检测精度

**阈值调整**: 可根据实际应用调整50%的检测阈值
**误判风险**: 过低的阈值可能导致误判，过高可能漏检
**建议**: 在实际环境中测试并调整阈值

### 3. 数据一致性

**保存频率**: 默认1分钟保存一次，平衡数据安全和flash寿命
**校验机制**: 使用校验和确保数据完整性
**恢复策略**: 数据损坏时自动重置并重新开始累计

## 总结

通过实现智能的芯片重启检测和初始化状态管理，我们的电量持久化系统能够：

1. **准确处理断电重启**: 自动检测并适应BL0906芯片的重启
2. **保持数据连续性**: 确保累计电量数据的准确性和连续性
3. **提供详细监控**: 通过日志系统提供完整的运行状态信息
4. **具备容错能力**: 对各种异常情况都有相应的处理机制

这使得系统在实际应用中具有很高的可靠性和稳定性。
