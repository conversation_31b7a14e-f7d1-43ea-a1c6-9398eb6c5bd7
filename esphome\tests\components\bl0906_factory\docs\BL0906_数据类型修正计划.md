# BL0906 数据类型修正计划

## ✅ 修改状态：已完成

根据BL0906数据手册，寄存器的数据类型应该按以下规则处理：
- **无符号数**：电压、电流、脉冲数、频率
- **有符号数**：其余寄存器（功率、校准寄存器等）

## 问题分析

~~但当前代码中存在以下问题：~~（已修复）

### 1. 统一符号扩展问题

在 `send_read_command_and_receive()` 函数中（第81行）：
```cpp
// 组合原始数据并返回
uint32_t temp = ((uint32_t)data.h << 16) | ((uint32_t)data.m << 8) | data.l;
int32_t raw = (temp << 8) >> 8;  // 自动符号扩展
```

**问题**：对所有寄存器都进行了符号扩展，但电压、电流、脉冲数、频率应该是无符号数。

### 2. 受影响的寄存器

#### 应该是无符号数的寄存器：
- `BL0906_V_RMS` (0x16) - 电压有效值
- `BL0906_I_RMS[0-5]` (0x0D-0x10, 0x13-0x14) - 电流有效值
- `BL0906_CF_CNT[0-5]` (0x30-0x33, 0x36-0x37) - 脉冲计数
- `BL0906_CF_SUM_CNT` (0x39) - 总脉冲计数
- `BL0906_FREQUENCY` (0x4E) - 频率
- `BL0906_TEMPERATURE` (0x5E) - 温度

#### 应该是有符号数的寄存器：
- `BL0906_WATT[0-5]` - 功率寄存器
- `BL0906_WATT_SUM` - 总功率寄存器
- 所有校准寄存器（CHGN、CHOS、RMSGN、RMSOS、WATTGN、WATTOS系列）

## ✅ 修改计划（已完成）

### ✅ 第一步：创建寄存器类型判断函数（已完成）

在 `bl0906_factory.h` 中添加新的辅助函数声明：
```cpp
// 判断寄存器是否为无符号类型
bool is_unsigned_register(uint8_t address);
```

在 `bl0906_factory.cpp` 中实现：
```cpp
bool BL0906Factory::is_unsigned_register(uint8_t address) {
  // 电压寄存器
  if (address == BL0906_V_RMS) return true;
  
  // 电流寄存器
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    if (address == BL0906_I_RMS[i]) return true;
  }
  
  // 脉冲计数寄存器
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    if (address == BL0906_CF_CNT[i]) return true;
  }
  if (address == BL0906_CF_SUM_CNT) return true;
  
  // 频率和温度寄存器
  if (address == BL0906_FREQUENCY) return true;
  if (address == BL0906_TEMPERATURE) return true;
  
  return false;  // 其他寄存器为有符号数
}
```

### ✅ 第二步：修改数据读取函数（已完成）

~~修改 `send_read_command_and_receive()` 函数：~~（已完成）
```cpp
int32_t BL0906Factory::send_read_command_and_receive(uint8_t address, bool* success) {
  // ... 现有的读取逻辑 ...
  
  // 组合原始数据
  uint32_t temp = ((uint32_t)data.h << 16) | ((uint32_t)data.m << 8) | data.l;
  
  int32_t raw;
  if (is_unsigned_register(address)) {
    // 无符号寄存器：直接使用24位值，不进行符号扩展
    raw = static_cast<int32_t>(temp & 0x00FFFFFF);
    ESP_LOGV(FACTORY_TAG, "读取无符号寄存器 0x%02X: 原始值=%u", address, raw);
  } else {
    // 有符号寄存器：进行符号扩展
    raw = (temp << 8) >> 8;  // 符号扩展
    ESP_LOGV(FACTORY_TAG, "读取有符号寄存器 0x%02X: 原始值=%d", address, raw);
  }
  
  if (success) *success = true;
  return raw;
}
```

### ✅ 第三步：修改数据结构（已完成）

~~考虑修改 `RawSensorData` 结构体，为无符号数据使用 `uint32_t`：~~（已完成）
```cpp
struct RawSensorData {
  // 基础传感器原始数据（无符号）
  uint32_t temperature_raw;
  uint32_t frequency_raw;
  uint32_t voltage_raw;
  
  // 通道数据
  struct ChannelData {
    uint32_t current_raw;    // 无符号
    int32_t power_raw;       // 有符号
    uint32_t energy_raw;     // 无符号
  } channels[6];
  
  // 总和数据
  int32_t power_sum_raw;     // 有符号
  uint32_t energy_sum_raw;   // 无符号
  
  // 其他字段保持不变
  uint32_t timestamp;
  bool read_complete;
};
```

### ✅ 第四步：修改数据转换函数（已完成）

~~更新 `convert_raw_to_value()` 函数以正确处理无符号数据：~~（已完成）
```cpp
float BL0906Factory::convert_raw_to_value(uint8_t address, int32_t raw_value) {
  switch (address) {
    case BL0906_FREQUENCY:
      // 频率是无符号数，确保使用无符号计算
      return (raw_value > 0) ? 10000000.0f / static_cast<uint32_t>(raw_value) : 0;
      
    case BL0906_TEMPERATURE:
      // 温度是无符号数
      return (static_cast<uint32_t>(raw_value) - 64) * 12.5f / 59.0f - 40.0f;
      
    case BL0906_V_RMS:
      // 电压是无符号数
      return static_cast<uint32_t>(raw_value) / Kv;
      
    // 电流寄存器（无符号）
    case BL0906_I_RMS[0]:
    case BL0906_I_RMS[1]:
    case BL0906_I_RMS[2]:
    case BL0906_I_RMS[3]:
    case BL0906_I_RMS[4]:
    case BL0906_I_RMS[5]:
      return static_cast<uint32_t>(raw_value) / Ki;
      
    // 功率寄存器（有符号）
    case BL0906_WATT[0]:
    case BL0906_WATT[1]:
    case BL0906_WATT[2]:
    case BL0906_WATT[3]:
    case BL0906_WATT[4]:
    case BL0906_WATT[5]:
      return raw_value / Kp;  // 保持有符号
      
    case BL0906_WATT_SUM:
      return raw_value / Kp_sum;  // 保持有符号
      
    // 电量寄存器（无符号）
    case BL0906_CF_CNT[0]:
    case BL0906_CF_CNT[1]:
    case BL0906_CF_CNT[2]:
    case BL0906_CF_CNT[3]:
    case BL0906_CF_CNT[4]:
    case BL0906_CF_CNT[5]:
      return static_cast<uint32_t>(raw_value) / Ke;
      
    case BL0906_CF_SUM_CNT:
      return static_cast<uint32_t>(raw_value) / Ke_sum;
      
    default:
      ESP_LOGW(FACTORY_TAG, "未知寄存器地址: 0x%02X", address);
      return raw_value;
  }
}
```

### ✅ 第五步：修改持久化存储（已完成）

~~更新电量持久化相关代码，确保脉冲计数使用无符号数：~~（已完成）
```cpp
void BL0906Factory::process_energy_persistence(const RawSensorData& data) {
  // ... 现有逻辑 ...
  
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    // 确保使用无符号数处理脉冲计数
    uint32_t current_count = data.channels[i].energy_raw;  // 已经是uint32_t
    
    // ... 其余逻辑保持不变 ...
  }
  
  // 处理总脉冲计数
  uint32_t current_sum_count = data.energy_sum_raw;  // 已经是uint32_t
  // ... 其余逻辑保持不变 ...
}
```

### ✅ 第六步：修改校准寄存器读取（已完成）

~~更新 `read_register_value()` 函数以正确处理不同类型的寄存器：~~（已完成）
```cpp
int32_t BL0906Factory::read_register_value(uint8_t address) {
  ESP_LOGI(FACTORY_TAG, "读取寄存器0x%02X的值", address);

  bool read_success = false;
  int32_t raw_value = send_read_command_and_receive(address, &read_success);
  
  if (!read_success) {
    ESP_LOGE(FACTORY_TAG, "读取寄存器0x%02X失败", address);
    return 0;
  }

  // 对于16位寄存器，需要特殊处理
  if (is_16bit_register(address)) {
    // 16位寄存器通常是校准寄存器，都是有符号数
    uint16_t raw_16bit = raw_value & 0xFFFF;
    int32_t result = ((int32_t)(raw_16bit << 16)) >> 16;  // 符号扩展
    ESP_LOGV(FACTORY_TAG, "16位寄存器读取: 0x%02X -> 0x%04X = %d", address, raw_16bit, result);
    return result;
  } else {
    // 24位寄存器根据类型处理
    if (is_unsigned_register(address)) {
      // 无符号寄存器：直接返回24位值
      uint32_t result = raw_value & 0x00FFFFFF;
      ESP_LOGV(FACTORY_TAG, "24位无符号寄存器读取: 0x%02X -> 0x%06X = %u", address, result, result);
      return static_cast<int32_t>(result);
    } else {
      // 有符号寄存器：符号扩展
      int32_t result = ((int32_t)(raw_value << 8)) >> 8;
      ESP_LOGV(FACTORY_TAG, "24位有符号寄存器读取: 0x%02X -> 0x%06X = %d", address, raw_value & 0xFFFFFF, result);
      return result;
    }
  }
}
```

## ✅ 修改完成总结

### 已完成的修改
1. ✅ **添加寄存器类型判断函数**：`is_unsigned_register()`
2. ✅ **修改数据读取逻辑**：根据寄存器类型选择是否进行符号扩展
3. ✅ **更新数据结构**：`RawSensorData`中无符号数据使用`uint32_t`
4. ✅ **修正数据转换**：确保无符号数据使用无符号计算
5. ✅ **更新持久化存储**：脉冲计数使用无符号处理
6. ✅ **修正校准寄存器读取**：正确处理不同类型的寄存器
7. ✅ **修改loop函数**：适配新的数据类型
8. ✅ **更新日志输出**：使用正确的格式化字符串
9. ✅ **修正RMSOS计算**：使用无符号电流数据

### 技术改进
- **类型安全**：严格区分有符号和无符号寄存器
- **数据准确性**：避免无符号数据被错误解释为负值
- **代码清晰度**：明确的类型判断和转换逻辑
- **符合规范**：严格按照BL0906数据手册要求

### 影响评估

#### 正面影响
1. **数据准确性**：正确处理无符号数据，避免负值错误
2. **符合规范**：严格按照数据手册要求处理数据类型
3. **扩展性好**：为未来添加新寄存器提供了清晰的分类机制
4. **类型安全**：减少类型转换错误的可能性

#### 潜在风险
1. **兼容性**：可能影响现有配置的数值显示（但这是正确的修复）
2. **测试需求**：需要全面测试各种数据范围
3. **文档更新**：需要更新相关文档说明

## 验证建议

1. **边界测试**：测试接近24位最大值的数据
2. **负载测试**：在不同负载下验证电流、功率数据
3. **长期测试**：验证脉冲计数的累积准确性
4. **对比测试**：与其他测量设备对比验证准确性
5. **校准测试**：验证校准寄存器的读写功能 