# BL0906 Factory 嵌套配置结构指南

## 概述

BL0906 Factory组件采用嵌套的通道配置结构，将每个通道的所有传感器组织在一个嵌套的配置块中。这种配置方式使得传感器配置更加清晰、直观和易于管理。

## 配置结构

### 嵌套通道配置格式

```yaml
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    
    # 全局传感器
    voltage:
      name: "电压"
    frequency:
      name: "频率"
    temperature:
      name: "温度"
    
    # 通道1传感器组（嵌套配置）
    ch1:
      current:
        name: "通道1电流"
      power:
        name: "通道1功率"
      energy:
        name: "通道1实时电量"
      total_energy:
        name: "通道1累计电量"
      yesterday_energy:
        name: "通道1昨日电量"
      today_energy:
        name: "通道1今日电量"
      week_energy:
        name: "通道1本周电量"
      month_energy:
        name: "通道1本月电量"
      year_energy:
        name: "通道1本年电量"
    
    # 通道2传感器组（嵌套配置）
    ch2:
      current:
        name: "通道2电流"
      power:
        name: "通道2功率"
      # ... 其他传感器
    
    # 总和传感器
    power_sum:
      name: "总功率"
    energy_sum:
      name: "总实时电量"
    
    # 总电量统计传感器
    yesterday_total_energy:
      name: "昨日总电量"
    today_total_energy:
      name: "今日总电量"
```

## 支持的传感器

### 通道配置（ch1-ch6）

每个通道支持以下传感器：

#### 基础测量传感器
- `current`: 电流传感器
- `power`: 功率传感器
- `energy`: 实时电量传感器
- `total_energy`: 累计电量传感器（持久化存储）

#### 电量统计传感器
- `yesterday_energy`: 昨日电量
- `today_energy`: 今日电量
- `week_energy`: 本周电量
- `month_energy`: 本月电量
- `year_energy`: 本年电量

### 全局传感器

#### 基础传感器
- `voltage`: 电压传感器
- `frequency`: 频率传感器
- `temperature`: 温度传感器

#### 总和传感器
- `power_sum`: 总功率
- `energy_sum`: 总实时电量
- `total_energy_sum`: 总累计电量

#### 总电量统计传感器
- `yesterday_total_energy`: 昨日总电量
- `today_total_energy`: 今日总电量
- `week_total_energy`: 本周总电量
- `month_total_energy`: 本月总电量
- `year_total_energy`: 本年总电量

## 配置优势

### 1. 清晰的层次结构
```yaml
# 嵌套结构 - 清晰直观
ch1:
  current: {...}
  power: {...}
  energy: {...}
  today_energy: {...}

ch2:
  current: {...}
  power: {...}
  energy: {...}
  today_energy: {...}
```

### 2. 简洁的配置
- 通道内的传感器配置简洁明了
- 配置结构紧凑
- 易于理解和维护

### 3. 更好的可读性
- 每个通道的传感器集中在一起
- 便于理解通道与传感器的关系
- 减少配置错误的可能性

### 4. 灵活的选择性配置
```yaml
# 通道1 - 完整配置
ch1:
  current: {...}
  power: {...}
  energy: {...}
  total_energy: {...}
  yesterday_energy: {...}
  today_energy: {...}
  week_energy: {...}
  month_energy: {...}
  year_energy: {...}

# 通道2 - 最小配置
ch2:
  current: {...}
  power: {...}
  today_energy: {...}

# 通道3 - 选择性配置
ch3:
  power: {...}
  energy: {...}
  today_energy: {...}
  month_energy: {...}
```

## 配置示例

### 基本配置示例
```yaml
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    
    # 全局传感器
    voltage:
      name: "电压"
    
    # 通道1传感器组
    ch1:
      current:
        name: "通道1电流"
      power:
        name: "通道1功率"
      today_energy:
        name: "通道1今日电量"
    
    # 通道2传感器组
    ch2:
      current:
        name: "通道2电流"
      power:
        name: "通道2功率"
      today_energy:
        name: "通道2今日电量"
    
    # 总电量统计
    today_total_energy:
      name: "今日总电量"
```

### 完整配置示例
```yaml
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    
    # 全局传感器
    voltage:
      name: "电压"
    frequency:
      name: "频率"
    temperature:
      name: "温度"
    
    # 通道1完整传感器组
    ch1:
      current:
        name: "通道1电流"
        unit_of_measurement: "A"
      power:
        name: "通道1功率"
        unit_of_measurement: "W"
      energy:
        name: "通道1实时电量"
        unit_of_measurement: "kWh"
      total_energy:
        name: "通道1累计电量"
        unit_of_measurement: "kWh"
      yesterday_energy:
        name: "通道1昨日电量"
        unit_of_measurement: "kWh"
      today_energy:
        name: "通道1今日电量"
        unit_of_measurement: "kWh"
      week_energy:
        name: "通道1本周电量"
        unit_of_measurement: "kWh"
      month_energy:
        name: "通道1本月电量"
        unit_of_measurement: "kWh"
      year_energy:
        name: "通道1本年电量"
        unit_of_measurement: "kWh"
    
    # 其他通道配置...
    
    # 总和传感器
    power_sum:
      name: "总功率"
    energy_sum:
      name: "总实时电量"
    total_energy_sum:
      name: "总累计电量"
    
    # 总电量统计传感器
    yesterday_total_energy:
      name: "昨日总电量"
    today_total_energy:
      name: "今日总电量"
    week_total_energy:
      name: "本周总电量"
    month_total_energy:
      name: "本月总电量"
    year_total_energy:
      name: "本年总电量"
```

## 配置最佳实践

### 1. 渐进式配置
从基础传感器开始，逐步添加统计传感器：

```yaml
# 第一步：基础配置
ch1:
  current: {...}
  power: {...}

# 第二步：添加电量监控
ch1:
  current: {...}
  power: {...}
  energy: {...}
  today_energy: {...}

# 第三步：完整统计
ch1:
  current: {...}
  power: {...}
  energy: {...}
  total_energy: {...}
  yesterday_energy: {...}
  today_energy: {...}
  week_energy: {...}
  month_energy: {...}
  year_energy: {...}
```

### 2. 按需配置
只配置实际需要的传感器：

```yaml
# 高功率设备监控
ch1:
  current: {...}
  power: {...}
  today_energy: {...}
  month_energy: {...}

# 低功率设备监控
ch2:
  power: {...}
  today_energy: {...}

# 关键设备监控
ch3:
  current: {...}
  power: {...}
  energy: {...}
  total_energy: {...}
  yesterday_energy: {...}
  today_energy: {...}
  week_energy: {...}
  month_energy: {...}
  year_energy: {...}
```

### 3. 命名规范
建议使用清晰的命名规范：

```yaml
ch1:
  current:
    name: "客厅空调电流"
  power:
    name: "客厅空调功率"
  today_energy:
    name: "客厅空调今日电量"

ch2:
  current:
    name: "厨房冰箱电流"
  power:
    name: "厨房冰箱功率"
  today_energy:
    name: "厨房冰箱今日电量"
```

## 传感器数量统计

### 通道传感器 (54个)
- 6个通道 × 9种传感器类型 = 54个传感器
- 每个通道支持：
  - 4个基础传感器：current, power, energy, total_energy
  - 5个统计传感器：yesterday_energy, today_energy, week_energy, month_energy, year_energy

### 全局传感器 (11个)
- 6个基础传感器：voltage, frequency, temperature, power_sum, energy_sum, total_energy_sum
- 5个总电量统计传感器：yesterday_total_energy, today_total_energy, week_total_energy, month_total_energy, year_total_energy

### 总计支持 65个传感器
- 54个通道传感器 + 11个全局传感器 = 65个传感器

## 总结

嵌套配置结构提供了：

1. **清晰的组织方式**：按通道分组，便于理解和管理
2. **简洁的配置**：减少重复，提高可读性
3. **灵活的选择**：支持选择性配置，按需启用传感器
4. **优秀的维护性**：配置结构清晰，便于后续维护和扩展

这种配置方式特别适合需要监控多个通道的复杂应用场景，能够显著提升配置的可读性和维护性。 