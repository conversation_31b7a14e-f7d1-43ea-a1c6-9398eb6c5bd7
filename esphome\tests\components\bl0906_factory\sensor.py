import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import sensor
from esphome.const import (
    CONF_ID, CONF_FREQUENCY, CONF_TEMPERATURE, CONF_VOLTAGE,
    CONF_CURRENT, CONF_POWER, CONF_ENERGY,
    DEVICE_CLASS_CURRENT, DEVICE_CLASS_ENERGY, DEVICE_CLASS_POWER,
    DEVICE_CLASS_VOLTAGE, DEVICE_CLASS_TEMPERATURE, DEVICE_CLASS_FREQUENCY,
    STATE_CLASS_MEASUREMENT, STATE_CLASS_TOTAL_INCREASING,
    UNIT_VOLT, UNIT_AMPERE, UNIT_WATT, UNIT_KILOWATT_HOURS,
    UNIT_HERTZ, UNIT_CELSIUS
)
from . import BL0906Factory, CONF_BL0906_FACTORY_ID, StatisticsSensorType

DEPENDENCIES = ["bl0906_factory"]

# 通道数量
CHANNEL_COUNT = 6

# 传感器类型枚举映射
SENSOR_TYPES = {
    "VOLTAGE": 0,
    "FREQUENCY": 1,
    "TEMPERATURE": 2,
    "CURRENT": 3,
    "POWER": 4,
    "ENERGY": 5,
    "POWER_SUM": 6,
    "ENERGY_SUM": 7,
    "TOTAL_ENERGY": 8,
    "TOTAL_ENERGY_SUM": 9
}

# 电量统计传感器类型枚举映射
STATISTICS_SENSOR_TYPES = {
    "YESTERDAY_ENERGY": 0,
    "TODAY_ENERGY": 1,
    "WEEK_ENERGY": 2,
    "MONTH_ENERGY": 3,
    "YEAR_ENERGY": 4,
    "YESTERDAY_TOTAL_ENERGY": 5,
    "TODAY_TOTAL_ENERGY": 6,
    "WEEK_TOTAL_ENERGY": 7,
    "MONTH_TOTAL_ENERGY": 8,
    "YEAR_TOTAL_ENERGY": 9
}

# 全局传感器配置
GLOBAL_SENSOR_CONFIGS = {
    # 基础传感器
    CONF_FREQUENCY: {
        "type": "FREQUENCY",
        "unit": UNIT_HERTZ,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_FREQUENCY,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    CONF_TEMPERATURE: {
        "type": "TEMPERATURE",
        "unit": UNIT_CELSIUS,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_TEMPERATURE,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    CONF_VOLTAGE: {
        "type": "VOLTAGE",
        "unit": UNIT_VOLT,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_VOLTAGE,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    # 总和传感器
    "power_sum": {
        "type": "POWER_SUM",
        "unit": UNIT_WATT,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_POWER,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    "energy_sum": {
        "type": "ENERGY_SUM",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
    "total_energy_sum": {
        "type": "TOTAL_ENERGY_SUM",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
    # 总电量统计传感器
    "yesterday_total_energy": {
        "type": "YESTERDAY_TOTAL_ENERGY",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
    "today_total_energy": {
        "type": "TODAY_TOTAL_ENERGY",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
    "week_total_energy": {
        "type": "WEEK_TOTAL_ENERGY",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
    "month_total_energy": {
        "type": "MONTH_TOTAL_ENERGY",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
    "year_total_energy": {
        "type": "YEAR_TOTAL_ENERGY",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
}

# 通道传感器配置模板
CHANNEL_SENSOR_TEMPLATES = {
    # 基础测量传感器
    CONF_CURRENT: {
        "type": "CURRENT",
        "unit": UNIT_AMPERE,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_CURRENT,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    CONF_POWER: {
        "type": "POWER",
        "unit": UNIT_WATT,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_POWER,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    CONF_ENERGY: {
        "type": "ENERGY",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
    "total_energy": {
        "type": "TOTAL_ENERGY",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
    # 电量统计传感器
    "yesterday_energy": {
        "type": "YESTERDAY_ENERGY",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
    "today_energy": {
        "type": "TODAY_ENERGY",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
    "week_energy": {
        "type": "WEEK_ENERGY",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
    "month_energy": {
        "type": "MONTH_ENERGY",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
    "year_energy": {
        "type": "YEAR_ENERGY",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
}

def build_channel_sensor_schema():
    """构建单个通道的传感器配置模式"""
    schema_dict = {}
    for sensor_key, config in CHANNEL_SENSOR_TEMPLATES.items():
        schema_dict[cv.Optional(sensor_key)] = sensor.sensor_schema(
            unit_of_measurement=config["unit"],
            accuracy_decimals=config["accuracy"],
            device_class=config["device_class"],
            state_class=config["state_class"],
        )
    return cv.Schema(schema_dict)

def build_config_schema():
    """构建嵌套配置模式"""
    schema_dict = {cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory)}

    # 添加全局传感器配置
    for key, config in GLOBAL_SENSOR_CONFIGS.items():
        schema_dict[cv.Optional(key)] = sensor.sensor_schema(
            unit_of_measurement=config["unit"],
            accuracy_decimals=config["accuracy"],
            device_class=config["device_class"],
            state_class=config["state_class"],
        )

    # 添加通道配置 (ch1-ch6)
    channel_schema = build_channel_sensor_schema()
    for i in range(1, CHANNEL_COUNT + 1):
        schema_dict[cv.Optional(f"ch{i}")] = channel_schema

    return cv.Schema(schema_dict)

CONFIG_SCHEMA = build_config_schema()

async def to_code(config):
    var = await cg.get_variable(config[CONF_BL0906_FACTORY_ID])

    # 注册全局传感器
    for sensor_key, sensor_config in GLOBAL_SENSOR_CONFIGS.items():
        if sensor_key in config:
            sens = await sensor.new_sensor(config[sensor_key])
            sensor_type = sensor_config["type"]
            
            # 判断是否为电量统计传感器
            if sensor_type in STATISTICS_SENSOR_TYPES:
                statistics_sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::StatisticsSensorType::{sensor_type}")
                cg.add(var.set_statistics_sensor(statistics_sensor_type_enum, sens, 0))
            else:
                sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::BL0906Factory::SensorType::{sensor_type}")
                cg.add(var.set_sensor(sensor_type_enum, sens, 0))

    # 注册通道传感器
    for i in range(1, CHANNEL_COUNT + 1):
        channel_key = f"ch{i}"
        if channel_key in config:
            channel_config = config[channel_key]
            channel_index = i - 1  # 内部使用0-5索引
            
            for sensor_key, sensor_config in CHANNEL_SENSOR_TEMPLATES.items():
                if sensor_key in channel_config:
                    sens = await sensor.new_sensor(channel_config[sensor_key])
                    sensor_type = sensor_config["type"]
                    
                    # 判断是否为电量统计传感器
                    if sensor_type in STATISTICS_SENSOR_TYPES:
                        statistics_sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::StatisticsSensorType::{sensor_type}")
                        cg.add(var.set_statistics_sensor(statistics_sensor_type_enum, sens, channel_index))
                    else:
                        sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::BL0906Factory::SensorType::{sensor_type}")
                        cg.add(var.set_sensor(sensor_type_enum, sens, channel_index))
