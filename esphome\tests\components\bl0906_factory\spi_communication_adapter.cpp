#include "spi_communication_adapter.h"
#include "bl0906_registers.h"
#include "esphome/core/log.h"
#include "esphome/core/helpers.h"
#include <functional>

namespace esphome {
namespace bl0906_factory {

static const char *const TAG = "spi_comm_adapter";

// ========== 设置方法 ==========

void SpiCommunicationAdapter::set_spi_parent(spi::SPIComponent *parent) {
  this->parent_ = parent;
}

void SpiCommunicationAdapter::set_cs_pin(GPIOPin *cs_pin) {
  this->cs_ = cs_pin;
}

// ========== CommunicationAdapterInterface 实现 ==========

bool SpiCommunicationAdapter::initialize() {
  if (initialized_) {
    return true;
  }
  
  if (!this->parent_) {
    set_error(CommunicationError::HARDWARE_ERROR, "SPI父组件未设置");
    return false;
  }
  
  if (!this->cs_) {
    set_error(CommunicationError::HARDWARE_ERROR, "CS引脚未设置");
    return false;
  }
  
  // 关键修复：调用spi_setup()初始化SPI设备
  ESP_LOGI(TAG, "正在初始化SPI设备...");
  this->spi_setup();
  ESP_LOGI(TAG, "SPI设备初始化完成");
  
  // 重置统计信息
  reset_statistics();
  reset_error_state();
  
  initialized_ = true;
  ESP_LOGI(TAG, "SPI通信适配器初始化成功");
  return true;
}

int32_t SpiCommunicationAdapter::read_register(uint8_t address, bool* success) {
  if (!initialized_) {
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "适配器未初始化");
    if (success) *success = false;
    return 0;
  }
  
  // 使用重试机制执行读取操作
  auto read_operation = [this, address, success]() -> int32_t {
    return this->send_spi_read_command(address, success);
  };
  
  return execute_with_retry<int32_t>(read_operation);
}

bool SpiCommunicationAdapter::write_register(uint8_t address, int16_t value) {
  if (!initialized_) {
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "适配器未初始化");
    return false;
  }
  
  // 使用重试机制执行写入操作
  auto write_operation = [this, address, value]() -> bool {
    return this->send_spi_write_command(address, value);
  };
  
  return execute_with_retry<bool>(write_operation);
}

bool SpiCommunicationAdapter::send_raw_command(const uint8_t* command, size_t length) {
  ESP_LOGI(TAG, "=== 开始执行SPI原始命令发送 ===");
  
  if (!command || length == 0) {
    ESP_LOGE(TAG, "无效的原始命令参数: command=%p, length=%zu", command, length);
    set_error(CommunicationError::INVALID_RESPONSE, "无效的原始命令参数");
    update_statistics(false, CommunicationError::INVALID_RESPONSE);
    return false;
  }

  ESP_LOGI(TAG, "SPI设备可用性检查...");
  if (!is_available()) {
    ESP_LOGE(TAG, "SPI设备不可用: initialized_=%s, parent_=%p, cs_=%p", 
             initialized_ ? "true" : "false", this->parent_, this->cs_);
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "SPI设备不可用");
    update_statistics(false, CommunicationError::DEVICE_NOT_AVAILABLE);
    return false;
  }

  ESP_LOGI(TAG, "发送SPI原始命令，长度: %zu", length);
  
  // 记录要发送的命令（用于调试）
  std::string cmd_str = "准备发送SPI原始命令: ";
  for (size_t i = 0; i < length; i++) {
    cmd_str += format_hex(command[i]);
    if (i < length - 1) cmd_str += " ";
  }
  ESP_LOGI(TAG, "%s", cmd_str.c_str());
  
  // 执行SPI原始命令发送
  ESP_LOGI(TAG, "开始执行SPI操作...");
  bool spi_success = safe_spi_operation([this, command, length]() {
    ESP_LOGV(TAG, "SPI使能CS引脚...");
    this->enable();
    // 短暂延时确保CS稳定
    delayMicroseconds(CS_SETUP_DELAY_US);
    
    ESP_LOGV(TAG, "开始SPI数据传输...");
    this->write_array(command, length);
    
    // 添加延时确保数据稳定
    delayMicroseconds(CS_SETUP_DELAY_US);
    ESP_LOGV(TAG, "SPI禁用CS引脚...");
    this->disable();
  });
  
  ESP_LOGI(TAG, "SPI操作完成，结果: %s", spi_success ? "成功" : "失败");
  
  if (!spi_success) {
    ESP_LOGE(TAG, "SPI原始命令发送失败");
    set_error(CommunicationError::HARDWARE_ERROR, "SPI原始命令发送失败");
    update_statistics(false, CommunicationError::HARDWARE_ERROR);
    return false;
  }
  
  // 再次记录发送的命令（确认）
  cmd_str = "SPI原始命令已发送: ";
  for (size_t i = 0; i < length; i++) {
    cmd_str += format_hex(command[i]);
    if (i < length - 1) cmd_str += " ";
  }
  ESP_LOGI(TAG, "%s", cmd_str.c_str());
  
  update_statistics(true);
  ESP_LOGI(TAG, "=== SPI原始命令发送完成 ===");
  return true;
}

bool SpiCommunicationAdapter::is_available() {
  return initialized_ && this->parent_ && this->cs_;
}

bool SpiCommunicationAdapter::is_connected() {
  return is_available();
}

void SpiCommunicationAdapter::flush_buffer() {
  // SPI模式下无需清空缓冲区，这是一个空操作
  ESP_LOGV(TAG, "SPI模式无需清空缓冲区");
}

std::string SpiCommunicationAdapter::get_last_error() const {
  return last_error_message_;
}

void SpiCommunicationAdapter::reset_error_state() {
  last_error_ = CommunicationError::SUCCESS;
  last_error_message_.clear();
}

CommunicationError SpiCommunicationAdapter::get_last_error_code() const {
  return last_error_;
}

size_t SpiCommunicationAdapter::get_success_count() const {
  return stats_.success_count;
}

size_t SpiCommunicationAdapter::get_error_count() const {
  return stats_.error_count;
}

CommunicationStats SpiCommunicationAdapter::get_statistics() const {
  return stats_;
}

void SpiCommunicationAdapter::reset_statistics() {
  stats_ = CommunicationStats{};
}

std::string SpiCommunicationAdapter::get_adapter_type() const {
  return "SPI";
}

std::string SpiCommunicationAdapter::get_status_info() const {
  char buffer[256];
  snprintf(buffer, sizeof(buffer),
           "SPI适配器状态: 初始化=%s, 成功=%zu, 错误=%zu, 超时=%zu, 校验和错误=%zu",
           initialized_ ? "是" : "否",
           stats_.success_count,
           stats_.error_count,
           stats_.timeout_count,
           stats_.checksum_error_count);
  return std::string(buffer);
}

bool SpiCommunicationAdapter::self_test() {
  if (!is_available()) {
    set_error(CommunicationError::DEVICE_NOT_AVAILABLE, "设备不可用");
    return false;
  }
  
  // 尝试读取温度寄存器作为自检
  bool success = false;
  int32_t temp_value = read_register(BL0906_TEMPERATURE, &success);
  
  if (success && temp_value > 0) {
    ESP_LOGI(TAG, "SPI适配器自检通过，温度值: %d", temp_value);
    return true;
  } else {
    set_error(CommunicationError::HARDWARE_ERROR, "自检失败：无法读取温度寄存器");
    return false;
  }
}

// ========== 内部方法实现 ==========

int32_t SpiCommunicationAdapter::send_spi_read_command(uint8_t address, bool* success) {
  // 初始化返回值
  if (success) *success = false;
  
  ESP_LOGV(TAG, "开始SPI读取操作: 地址=0x%02X", address);
  
  // 按照BL0906规格书要求：连续的48位SPI传输
  // ESPHome SPI的transfer_array是in-place操作，同一数组既用于发送也用于接收
  uint8_t spi_data[6] = {0x82, address, 0x00, 0x00, 0x00, 0x00};
  
  ESP_LOGV(TAG, "发送SPI命令: 0x%02X 0x%02X (连续48位传输)", spi_data[0], spi_data[1]);
  ESP_LOGD(TAG, "SPI发送数据: %02X %02X %02X %02X %02X %02X", 
           spi_data[0], spi_data[1], spi_data[2], spi_data[3], spi_data[4], spi_data[5]);
  
  // 执行连续的48位SPI传输
  bool spi_success = safe_spi_operation([this, &spi_data]() {
    this->enable();
    // 短暂延时确保CS稳定
    delayMicroseconds(CS_SETUP_DELAY_US);
    
    // 使用transfer_array实现真正的双向同步传输（连续48位）
    // ESPHome的transfer_array是in-place操作：发送spi_data，结果覆盖到spi_data
    this->transfer_array(spi_data, 6);
    
    // 添加延时确保数据稳定
    delayMicroseconds(CS_SETUP_DELAY_US);
    this->disable();
  });
  
  ESP_LOGV(TAG, "SPI操作结果: %s", spi_success ? "成功" : "失败");
  ESP_LOGD(TAG, "SPI完整接收数据: %02X %02X %02X %02X %02X %02X", 
           spi_data[0], spi_data[1], spi_data[2], spi_data[3], spi_data[4], spi_data[5]);
  
  if (!spi_success) {
    set_error(CommunicationError::HARDWARE_ERROR, "SPI操作异常，地址: 0x" + format_hex(address));
    update_statistics(false, CommunicationError::HARDWARE_ERROR);
    return 0;
  }
  
  // 提取有效数据：跳过前2字节（命令和地址的回显），从第3字节开始是有效数据
  uint8_t data_h = spi_data[2];    // DATA_H
  uint8_t data_m = spi_data[3];    // DATA_M  
  uint8_t data_l = spi_data[4];    // DATA_L
  uint8_t received_checksum = spi_data[5];  // CHECKSUM
  
  ESP_LOGD(TAG, "SPI有效数据: DATA_H=0x%02X, DATA_M=0x%02X, DATA_L=0x%02X, CHECKSUM=0x%02X", 
           data_h, data_m, data_l, received_checksum);
  
  // 校验和验证
  uint8_t calculated_checksum = calculate_spi_checksum(0x82, address, 
    (static_cast<uint32_t>(data_h) << 16) | 
    (static_cast<uint32_t>(data_m) << 8) | 
    static_cast<uint32_t>(data_l));
    
  if (calculated_checksum != received_checksum) {
    set_error(CommunicationError::CHECKSUM_ERROR,
              "SPI读取校验和错误: 地址=0x" + format_hex(address) + 
              ", 期望=0x" + format_hex(calculated_checksum) + 
              ", 实际=0x" + format_hex(received_checksum));
    ESP_LOGD(TAG, "校验和计算: cmd=0x82, addr=0x%02X, data=0x%02X%02X%02X", 
             address, data_h, data_m, data_l);
    update_statistics(false, CommunicationError::CHECKSUM_ERROR);
    return 0;
  }
  
  if (success) *success = true;
  
  // 根据寄存器类型返回正确的数据
  if (is_16bit_register(address)) {
    // 16位寄存器：只使用低16位，处理符号扩展
    int16_t value = (data_m << 8) | data_l;
    ESP_LOGD(TAG, "SPI读取16位寄存器 0x%02X: 原始数据[%02X %02X %02X], 16位值: %d", 
             address, data_h, data_m, data_l, value);
    update_statistics(true);
    return static_cast<int32_t>(value);
  } else {
    // 24位寄存器
    if (is_unsigned_register(address)) {
      // 无符号24位
      uint32_t value = (static_cast<uint32_t>(data_h) << 16) | 
                       (static_cast<uint32_t>(data_m) << 8) | 
                       static_cast<uint32_t>(data_l);
      ESP_LOGV(TAG, "SPI读取无符号24位寄存器 0x%02X: %u", address, value);
      update_statistics(true);
      return static_cast<int32_t>(value);
    } else {
      // 带符号24位，需要符号扩展
      int32_t value = (static_cast<int8_t>(data_h) << 16) | 
                      (static_cast<uint32_t>(data_m) << 8) | 
                      static_cast<uint32_t>(data_l);
      ESP_LOGV(TAG, "SPI读取有符号24位寄存器 0x%02X: %d", address, value);
      update_statistics(true);
      return value;
    }
  }
}

bool SpiCommunicationAdapter::send_spi_write_command(uint8_t address, int16_t value) {
  ESP_LOGI(TAG, "正在写入寄存器 0x%02X 值: %d (SPI)", address, value);
  
  // 按照BL0906规格书要求：连续的48位SPI传输
  // 发送：0x81 + ADDR + DATA_H + DATA_M + DATA_L + CHECKSUM (6字节 = 48位)
  uint8_t tx_data[6] = {0};  // 初始化发送缓冲区
  tx_data[0] = 0x81;
  tx_data[1] = address;
  
  if (is_16bit_register(address)) {
    // 16位寄存器：高字节为0，中字节和低字节存储16位值
    tx_data[2] = 0x00;
    tx_data[3] = (value >> 8) & 0xFF;
    tx_data[4] = value & 0xFF;
  } else {
    // 24位寄存器：使用位移技巧进行符号扩展（感谢用户建议！）
    // 将16位值左移8位再右移8位，利用算术右移自动实现符号扩展
    int32_t extended_value = (static_cast<int32_t>(value) << 8) >> 8;
    tx_data[2] = (extended_value >> 16) & 0xFF;  // DATA_H
    tx_data[3] = (extended_value >> 8) & 0xFF;   // DATA_M  
    tx_data[4] = extended_value & 0xFF;          // DATA_L
  }
  
  // 计算校验和
  uint32_t data_24bit = (static_cast<uint32_t>(tx_data[2]) << 16) |
                        (static_cast<uint32_t>(tx_data[3]) << 8) |
                        static_cast<uint32_t>(tx_data[4]);
  tx_data[5] = calculate_spi_checksum(0x81, address, data_24bit);
  
  // 执行连续的48位SPI传输
  bool spi_success = safe_spi_operation([this, &tx_data]() {
    this->enable();
    // 短暂延时确保CS稳定
    delayMicroseconds(CS_SETUP_DELAY_US);
    
    // write_array已经实现连续传输（48位）
    this->write_array(tx_data, 6);
    
    // 添加延时确保数据发送完成
    delayMicroseconds(SPI_DELAY_US);
    this->disable();
  });
  
  if (!spi_success) {
    set_error(CommunicationError::HARDWARE_ERROR, "SPI写入操作异常，寄存器: 0x" + format_hex(address));
    update_statistics(false, CommunicationError::HARDWARE_ERROR);
    return false;
  }
  
  ESP_LOGV(TAG, "SPI写入(连续48位): 0x%02X%02X%02X%02X%02X%02X", 
           tx_data[0], tx_data[1], tx_data[2], tx_data[3], tx_data[4], tx_data[5]);
  
  // 延时等待写入完成
  delay(10);  // 增加延时时间
  
  // 验证写入
  bool read_success = false;
  int32_t read_value = read_register(address, &read_success);
  
  if (!read_success) {
    set_error(CommunicationError::INVALID_RESPONSE, "SPI寄存器写入验证读取失败");
    update_statistics(false, CommunicationError::INVALID_RESPONSE);
    return false;
  }
  
  if (read_value == value) {
    ESP_LOGI(TAG, "SPI寄存器 0x%02X 写入成功，值: %d", address, value);
    update_statistics(true);
    return true;
  } else {
    set_error(CommunicationError::HARDWARE_ERROR,
              "SPI寄存器写入验证失败，写入值=" + std::to_string(value) + 
              "，读回值=" + std::to_string(read_value));
    update_statistics(false, CommunicationError::HARDWARE_ERROR);
    return false;
  }
}

uint8_t SpiCommunicationAdapter::calculate_spi_checksum(uint8_t cmd, uint8_t addr, uint32_t data) {
  uint8_t sum = cmd + addr + 
                ((data >> 16) & 0xFF) + 
                ((data >> 8) & 0xFF) + 
                (data & 0xFF);
  return sum ^ 0xFF;
}

bool SpiCommunicationAdapter::is_16bit_register(uint8_t address) {
  // 16位寄存器：CHGN、CHOS、RMSGN、WATTGN、WATTOS系列
  // 与BL0906Factory::is_16bit_register保持一致
  return (address >= 0xA1 && address <= 0xA8) ||  // CHGN 1-6
         (address == 0xAA) ||                     // CHGN_V
         (address >= 0xAC && address <= 0xAF) ||  // CHOS 1-4
         (address >= 0xB2 && address <= 0xB3) ||  // CHOS 5-6
         (address == 0xB5) ||                     // CHOS_V
         (address >= 0x6D && address <= 0x74) ||  // RMSGN 1-6
         (address >= 0xB7 && address <= 0xBE) ||  // WATTGN 1-6
         (address >= 0xC1 && address <= 0xC8);    // WATTOS 1-6
}

bool SpiCommunicationAdapter::is_unsigned_register(uint8_t address) {
  // 无符号寄存器列表
  switch (address) {
    case BL0906_TEMPERATURE:
    case BL0906_FREQUENCY:
    case BL0906_V_RMS:
    case BL0906_I_1_RMS:
    case BL0906_I_2_RMS:
    case BL0906_I_3_RMS:
    case BL0906_I_4_RMS:
    case BL0906_I_5_RMS:
    case BL0906_I_6_RMS:
    case BL0906_CF_1_CNT:
    case BL0906_CF_2_CNT:
    case BL0906_CF_3_CNT:
    case BL0906_CF_4_CNT:
    case BL0906_CF_5_CNT:
    case BL0906_CF_6_CNT:
    case BL0906_CF_SUM_CNT:
      return true;
    default:
      return false;
  }
}

void SpiCommunicationAdapter::set_error(CommunicationError error, const std::string& message) {
  last_error_ = error;
  last_error_message_ = message;
  stats_.last_error = error;
  stats_.last_error_timestamp = esphome::millis();
  
  ESP_LOGW(TAG, "通信错误: %s", message.c_str());
}

void SpiCommunicationAdapter::update_statistics(bool success, CommunicationError error) {
  if (success) {
    stats_.success_count++;
  } else {
    stats_.error_count++;
    
    switch (error) {
      case CommunicationError::TIMEOUT:
        stats_.timeout_count++;
        break;
      case CommunicationError::CHECKSUM_ERROR:
        stats_.checksum_error_count++;
        break;
      default:
        break;
    }
  }
}

template<typename T>
T SpiCommunicationAdapter::execute_with_retry(std::function<T()> operation, int max_retries) {
  T result = T();
  
  for (int attempt = 0; attempt <= max_retries; attempt++) {
    // 重置错误状态
    reset_error_state();
    
    result = operation();
    
    // 检查操作是否成功：如果没有错误，则认为成功
    bool operation_success = (last_error_ == CommunicationError::SUCCESS);
    
    // 如果操作成功，立即返回结果
    if (operation_success) {
      ESP_LOGV(TAG, "操作成功，第%d次尝试", attempt + 1);
      return result;
    }
    
    // 如果还有重试机会，输出重试信息
    if (attempt < max_retries) {
      ESP_LOGW(TAG, "操作失败，第%d次重试...", attempt + 1);
      delayMicroseconds(SPI_DELAY_US * 2);  // 重试前延时
    } else {
      ESP_LOGW(TAG, "操作失败，已达到最大重试次数(%d)", max_retries + 1);
    }
  }
  
  return result;
}

bool SpiCommunicationAdapter::safe_spi_operation(std::function<void()> operation) {
  // ESP平台不支持异常处理，直接执行操作
  operation();
  return true;
}

} // namespace bl0906_factory
} // namespace esphome 