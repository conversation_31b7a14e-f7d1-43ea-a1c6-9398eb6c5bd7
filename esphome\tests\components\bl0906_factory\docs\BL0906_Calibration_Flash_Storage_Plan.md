# BL0906 校准数据Flash存储改造计划

## 1. 项目概述

### 1.1 目标
- 将BL0906芯片的校准寄存器数值保存到ESP32的自定义Flash分区中
- 保持电量持久化数据仍使用NVS分区存储
- 实现芯片初始化时自动从Flash分区读取并应用校准值
- 支持校准版本（带Number组件）和量产版本（无Number组件）的模块化设计
- 量产版本只读取校准数据，不支持运行时修改

### 1.2 优势
- 校准数据与固件分离，更新固件不会丢失校准值
- 模块化设计便于生成不同版本（校准版/量产版）
- 减少NVS分区的使用压力
- 量产版本代码精简，无需Number组件依赖

## 2. 技术方案

### 2.1 分区表设计

```csv
# ESP32 分区表示例
# Name,   Type, SubType, Offset,  Size,   Flags
nvs,      data, nvs,     0x9000,  0x5000,
otadata,  data, ota,     0xe000,  0x2000,
app0,     app,  ota_0,   0x10000, 0x1C0000,
app1,     app,  ota_1,   0x1D0000,0x1C0000,
bl0906_cal,data,0x40,    0x390000,0x4000,  # 16KB校准数据分区
spiffs,   data, spiffs,  0x394000,0x6C000,
```

### 2.2 数据结构设计

```cpp
// 校准数据分区头部结构
struct CalibrationHeader {
    uint32_t magic;          // 魔数: 0xBL0906CA
    uint16_t version;        // 数据格式版本
    uint16_t entry_count;    // 校准项数量
    uint32_t crc32;          // 数据区CRC校验
    uint32_t timestamp;      // 最后更新时间戳
    uint8_t reserved[16];    // 预留字段
};

// 单个校准项结构（简化版）
struct CalibrationEntry {
    uint8_t register_addr;   // 寄存器地址
    uint8_t reserved;        // 保留字节，用于对齐
    int16_t value;           // 校准值
};

// 完整的校准数据结构
struct CalibrationData {
    CalibrationHeader header;
    CalibrationEntry entries[MAX_CALIB_ENTRIES];  // 最多128个校准项
    uint8_t padding[SECTOR_SIZE - sizeof(header) - sizeof(entries)];
};
```

### 2.3 手动保存设计优势
- 避免频繁写入Flash，延长Flash寿命
- 用户可以多次调整后一次性保存
- 减少校准过程中的系统开销
- 便于实现批量校准和验证流程

### 2.4 模块化设计

```cpp
// 基础校准存储类（量产版和校准版共用）
class CalibrationStorageBase {
public:
    // 初始化分区访问
    bool init();
    
    // 读取所有校准数据（只读操作）
    bool read_all(std::vector<CalibrationEntry>& entries);
    
    // 验证数据完整性
    bool verify();
    
protected:
    const esp_partition_t* partition_;
    std::vector<CalibrationEntry> cache_;
    bool cache_valid_;
};

// 扩展校准存储类（仅校准版使用）
#ifdef BL0906_CALIBRATION_MODE
class CalibrationStorage : public CalibrationStorageBase {
public:
    // 写入所有校准数据
    bool write_all(const std::vector<CalibrationEntry>& entries);
    
    // 更新单个校准值
    bool update_single(uint8_t reg_addr, int16_t value);
    
    // 擦除分区
    bool erase();
};
#endif
```

## 3. 实现步骤

### 3.1 第一阶段：基础框架搭建
1. **创建分区表配置文件**
   - 修改platformio.ini添加自定义分区表
   - 创建partitions.csv文件定义bl0906_cal分区

2. **实现CalibrationStorageBase类**
   - 文件位置：`calibration_storage_base.h/cpp`
   - 实现分区查找和初始化
   - 实现只读功能（read_all, verify）
   - 添加CRC校验功能

3. **实现CalibrationStorage扩展类**
   - 文件位置：`calibration_storage.h/cpp`
   - 继承CalibrationStorageBase
   - 实现写入功能（write_all, update_single, erase）
   - 使用条件编译控制

4. **模块化集成到BL0906Factory类**
   - 使用条件编译分离校准版和量产版代码
   - 在setup()中初始化存储
   - 量产版只读取，校准版支持读写

### 3.2 第二阶段：功能完善
1. **校准版功能**
   - 首次启动时从YAML配置迁移到Flash
   - 添加Button组件用于手动保存校准数据
   - 实现批量读取和保存所有校准值
   - Number组件只负责修改芯片寄存器，不自动保存

2. **量产版优化**
   - 移除所有Number组件相关代码
   - 移除Button组件
   - 精简初始化流程
   - 优化启动速度

3. **错误处理和恢复**
   - Flash损坏时的回退机制
   - 校验失败时使用默认值
   - 日志级别控制

### 3.3 第三阶段：高级功能
1. **校准数据管理**
   - 导出/导入功能
   - 版本管理
   - 差异化更新

2. **性能优化**
   - 缓存机制
   - 批量操作优化
   - 减少Flash写入次数

## 4. 代码修改位置

### 4.1 新增文件
- `components/bl0906_factory/calibration_storage_base.h/cpp` (基础类)
- `components/bl0906_factory/calibration_storage.h/cpp` (扩展类，仅校准版)
- `partitions.csv` (项目根目录)

### 4.2 修改文件
1. **bl0906_factory.h**
   - 添加条件编译宏定义
   - 根据版本添加不同的成员变量
   - 分离校准版和量产版的方法声明

2. **bl0906_factory.cpp**
   - 使用条件编译分离代码
   - 量产版：简化setup()，只读取校准数据
   - 校准版：完整功能，支持Number组件

3. **platformio.ini**
   - 添加分区表配置
   - 添加编译标志控制版本（BL0906_CALIBRATION_MODE）

4. **__init__.py**
   - 根据配置生成不同版本的代码
   - 量产版不注册Number组件

## 5. 数据流程

### 5.1 初始化流程
```
1. BL0906Factory::setup()
   ├── CalibrationStorage::init()
   ├── CalibrationStorage::read_all()
   ├── 验证数据有效性
   │   ├── 有效 → 应用到芯片
   │   └── 无效 → 使用YAML默认值
   └── 继续其他初始化
```

### 5.2 更新流程
```
1. 用户修改校准值（通过Number组件）
   ├── Number组件更新
   ├── write_register_value()
   └── 写入芯片寄存器

2. 用户点击保存按钮
   ├── Button的on_press触发
   ├── 调用save_all_calibration_to_flash()
   ├── 收集所有Number组件的当前值
   ├── CalibrationStorage::write_all()
   └── 批量保存到Flash
```

## 6. 测试计划

### 6.1 单元测试
- CalibrationStorage类的各个方法
- CRC校验功能
- 错误处理路径

### 6.2 集成测试
- 初始化流程测试
- 数据迁移测试
- 更新和持久化测试
- 异常恢复测试

### 6.3 性能测试
- Flash读写速度
- 启动时间影响
- 内存使用情况

## 7. 兼容性考虑

### 7.1 版本兼容
- 校准版和量产版使用相同的数据格式
- 校准版生成的数据可直接用于量产版
- 保留YAML配置作为初始值来源

### 7.2 平台支持
- ESP32/ESP32-S2/ESP32-S3/ESP32-C3支持
- 不支持ESP8266（无需兼容）

## 8. 风险评估

### 8.1 技术风险
- Flash寿命问题：通过减少写入频率缓解
- 分区损坏：双备份机制
- 启动时间增加：优化读取算法

### 8.2 兼容性风险
- 分区表冲突：预留足够空间
- OTA更新影响：保持分区独立

## 9. 实施时间表

- 第1周：基础框架实现
- 第2周：功能完善和测试
- 第3周：性能优化和文档
- 第4周：集成测试和发布

## 10. 未来扩展

- 支持多设备校准数据管理
- 云端备份和恢复
- 自动校准算法集成
- 图形化校准工具 

## 11. 实施示例代码

### 11.1 分区表配置 (partitions.csv)

```csv
# Name,   Type, SubType, Offset,  Size,   Flags
# 注意：确保bl0906_cal分区的偏移地址不与其他分区冲突
nvs,      data, nvs,     0x9000,  0x5000,
otadata,  data, ota,     0xe000,  0x2000,
app0,     app,  ota_0,   0x10000, 0x140000,
app1,     app,  ota_1,   0x150000,0x140000,
bl0906_cal,data,0x40,    0x290000,0x4000,  # 16KB for calibration data
spiffs,   data, spiffs,  0x294000,0x6C000,
```

### 11.2 模块化实现示例

```cpp
// calibration_storage_base.h - 基础类（量产版和校准版共用）
#pragma once
#include <vector>
#include <esp_partition.h>
#include "esphome/core/helpers.h"

namespace esphome {
namespace bl0906_factory {

static const uint32_t CALIB_MAGIC = 0xB0906CAL;
static const uint16_t CALIB_VERSION = 1;
static const size_t MAX_CALIB_ENTRIES = 128;
static const char* PARTITION_LABEL = "bl0906_cal";

struct CalibrationHeader {
    uint32_t magic;
    uint16_t version;
    uint16_t entry_count;
    uint32_t crc32;
    uint32_t timestamp;
    uint8_t reserved[16];
} __attribute__((packed));

struct CalibrationEntry {
    uint8_t register_addr;
    uint8_t reserved;
    int16_t value;
} __attribute__((packed));

// 基础类 - 只提供读取功能
class CalibrationStorageBase {
public:
    CalibrationStorageBase() : partition_(nullptr), cache_valid_(false) {}
    
    bool init();
    bool read_all(std::vector<CalibrationEntry>& entries);
    bool verify();
    
protected:
    const esp_partition_t* partition_;
    std::vector<CalibrationEntry> cache_;
    bool cache_valid_;
    
    uint32_t calculate_crc32(const uint8_t* data, size_t len);
};

}  // namespace bl0906_factory
}  // namespace esphome

// calibration_storage.h - 扩展类（仅校准版使用）
#ifdef BL0906_CALIBRATION_MODE
#pragma once
#include "calibration_storage_base.h"

namespace esphome {
namespace bl0906_factory {

// 扩展类 - 添加写入功能
class CalibrationStorage : public CalibrationStorageBase {
public:
    bool write_all(const std::vector<CalibrationEntry>& entries);
    bool update_single(uint8_t reg_addr, int16_t value);
    bool erase();
};

}  // namespace bl0906_factory
}  // namespace esphome
#endif // BL0906_CALIBRATION_MODE
```

### 11.3 修改BL0906Factory类集成示例

```cpp
// 在bl0906_factory.h中添加
#ifdef BL0906_CALIBRATION_MODE
  #include "calibration_storage.h"
#else
  #include "calibration_storage_base.h"
#endif

class BL0906Factory : public Component, public uart::UARTDevice {
    // ... existing code ...
    
private:
    // 根据版本使用不同的存储类
    #ifdef BL0906_CALIBRATION_MODE
        std::unique_ptr<CalibrationStorage> calib_storage_;
    #else
        std::unique_ptr<CalibrationStorageBase> calib_storage_;
    #endif
    
    // 通用方法
    void load_calibration_from_flash();
    
    // 仅校准版需要的方法
    #ifdef BL0906_CALIBRATION_MODE
        // 批量保存所有校准值到Flash（供Button使用）
        void save_all_calibration_to_flash();
        
        // Number组件相关成员和方法
        std::vector<BL0906Number*> calib_numbers_;
        void register_calib_number(BL0906Number *number);
        void refresh_all_calib_numbers();
    #endif
};

// 在bl0906_factory.cpp中修改setup()方法
void BL0906Factory::setup() {
    ESP_LOGD(FACTORY_TAG, "开始初始化BL0906...");
    
    // 初始化互斥锁
    this->init_mutex();
    
    // 清空缓冲区
    flush_rx_buffer();
    
    // 初始化校准存储（校准版和量产版共用）
    #ifdef BL0906_CALIBRATION_MODE
        ESP_LOGI(FACTORY_TAG, "BL0906校准版本初始化");
        calib_storage_ = std::make_unique<CalibrationStorage>();
    #else
        ESP_LOGI(FACTORY_TAG, "BL0906量产版本初始化");
        calib_storage_ = std::make_unique<CalibrationStorageBase>();
    #endif
    
    if (calib_storage_->init()) {
        ESP_LOGI(FACTORY_TAG, "校准存储初始化成功");
        // 从Flash加载校准值
        load_calibration_from_flash();
    } else {
        ESP_LOGW(FACTORY_TAG, "校准存储初始化失败，使用默认值");
        #ifdef BL0906_CALIBRATION_MODE
            // 校准版：尝试使用YAML配置
            apply_calibration_values();
        #else
            // 量产版：使用硬编码默认值或跳过
            ESP_LOGE(FACTORY_TAG, "量产版本无法加载校准数据！");
        #endif
    }
    
    // 校准版特有的初始化
    #ifdef BL0906_CALIBRATION_MODE
        // 读取校准寄存器
        refresh_all_calib_numbers();
        ESP_LOGI(FACTORY_TAG, "已注册 %d 个校准数字组件", calib_numbers_.size());
    #endif
    
    // ... rest of setup code ...
}

// 实现从Flash加载校准值
void BL0906Factory::load_calibration_from_flash() {
    std::vector<CalibrationEntry> entries;
    
    if (calib_storage_->read_all(entries)) {
        ESP_LOGI(FACTORY_TAG, "从Flash加载%d个校准值", entries.size());
        
        // 解除写保护
        if (!this->turn_off_write_protect()) {
            ESP_LOGE(FACTORY_TAG, "无法解除写保护");
            return;
        }
        
        // 应用校准值到芯片
        for (const auto& entry : entries) {
            if (this->write_register_value(entry.register_addr, entry.value)) {
                ESP_LOGD(FACTORY_TAG, "应用校准值: 寄存器0x%02X = %d", 
                         entry.register_addr, entry.value);
            }
        }
    } else {
        ESP_LOGW(FACTORY_TAG, "Flash中无有效校准数据，检查是否需要迁移");
        
        // 如果有YAML配置的初始值，迁移到Flash
        if (!initial_calibration_values_.empty()) {
            ESP_LOGI(FACTORY_TAG, "迁移YAML校准值到Flash");
            
            std::vector<CalibrationEntry> new_entries;
            for (const auto& pair : initial_calibration_values_) {
                CalibrationEntry entry;
                entry.register_addr = pair.first;
                entry.reserved = 0;  // 保留字节
                entry.value = pair.second;
                new_entries.push_back(entry);
            }
            
            if (calib_storage_->write_all(new_entries)) {
                ESP_LOGI(FACTORY_TAG, "校准值迁移成功");
            }
            
            // 继续应用校准值
            apply_calibration_values();
        }
    }
}

// write_register_value()不再自动同步到Flash
bool BL0906Factory::write_register_value(uint8_t reg, int16_t value) {
    // ... existing write code ...
    
    // 注意：不再自动同步到Flash，需要通过Button手动保存
    
    return success;
}

#ifdef BL0906_CALIBRATION_MODE
// 批量保存所有校准值到Flash（由Button组件调用）
void BL0906Factory::save_all_calibration_to_flash() {
    ESP_LOGI(FACTORY_TAG, "开始批量保存校准数据到Flash...");
    
    std::vector<CalibrationEntry> entries;
    
    // 从所有已注册的Number组件收集当前值
    for (auto* number : calib_numbers_) {
        if (number != nullptr) {
            CalibrationEntry entry;
            entry.register_addr = number->get_register_address();
            entry.reserved = 0;
            entry.value = static_cast<int16_t>(number->state);
            entries.push_back(entry);
            
            ESP_LOGD(FACTORY_TAG, "收集校准值: 寄存器0x%02X = %d", 
                     entry.register_addr, entry.value);
        }
    }
    
    // 批量写入Flash
    if (calib_storage_ && calib_storage_->write_all(entries)) {
        ESP_LOGI(FACTORY_TAG, "成功保存%d个校准值到Flash", entries.size());
    } else {
        ESP_LOGE(FACTORY_TAG, "保存校准数据到Flash失败");
    }
}
#endif
```

### 11.4 platformio.ini配置示例

```ini
# 校准版配置
[env:esp32dev_calibration]
platform = espressif32
board = esp32dev
framework = arduino

# 添加自定义分区表
board_build.partitions = partitions.csv

# 校准版构建标志
build_flags = 
    -DCORE_DEBUG_LEVEL=5
    -DCONFIG_PARTITION_TABLE_CUSTOM=y
    -DCONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"
    -DBL0906_CALIBRATION_MODE=1  # 启用校准模式

# 量产版配置
[env:esp32dev_production]
platform = espressif32
board = esp32dev
framework = arduino

# 添加自定义分区表
board_build.partitions = partitions.csv

# 量产版构建标志
build_flags = 
    -DCORE_DEBUG_LEVEL=1  # 减少日志输出
    -DCONFIG_PARTITION_TABLE_CUSTOM=y
    -DCONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"
    # 不定义BL0906_CALIBRATION_MODE，默认为量产版
```

### 11.5 YAML配置示例

#### 校准版配置
```yaml
# 用于校准的配置
bl0906_factory:
  id: bl0906_1
  calibration_mode: true  # 启用校准模式
  
  # 初始校准值（首次使用或Flash为空时）
  initial_calibration:
    - register: 0xA1
      value: 1000
    - register: 0xA2
      value: 1000
  
  # Number组件配置（仅校准版）
  calibration_numbers:
    - name: "Channel 1 Gain"
      register: 0xA1
      min_value: -32768
      max_value: 32767
    - name: "Channel 2 Gain"
      register: 0xA2
      min_value: -32768
      max_value: 32767

# Button组件用于手动保存校准数据
button:
  - platform: template
    name: "保存校准数据"
    icon: "mdi:content-save"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(bl0906_1));
        if (bl0906 != nullptr) {
          bl0906->save_all_calibration_to_flash();
          ESP_LOGI("button", "校准数据已保存到Flash");
        }
```

#### 量产版配置
```yaml
# 用于量产的配置
bl0906_factory:
  id: bl0906_1
  calibration_mode: false  # 禁用校准模式（量产版）
  
  # 量产版不需要initial_calibration
  # 量产版不需要calibration_numbers
```

## 12. 校准流程示例

### 12.1 完整的校准步骤
1. **烧录校准版固件**
   - 使用包含Number和Button组件的配置
   - 确保Flash分区表正确配置

2. **进行校准操作**
   - 通过Web界面或API调整Number组件值
   - 实时查看测量结果
   - 反复调整直到满意

3. **保存校准数据**
   - 点击"保存校准数据"按钮
   - 确认日志显示保存成功
   - 可以断电重启验证数据持久性

4. **切换到量产固件**
   - 烧录量产版固件
   - 量产版自动读取已保存的校准数据
   - 验证测量准确性

### 12.2 批量校准建议
```yaml
# 可以添加多个Button实现不同功能
button:
  - platform: template
    name: "保存校准数据"
    on_press:
      lambda: |-
        id(bl0906_1)->save_all_calibration_to_flash();
        
  - platform: template  
    name: "验证校准数据"
    on_press:
      lambda: |-
        // 读回并验证保存的数据
        id(bl0906_1)->verify_calibration_data();
        
  - platform: template
    name: "恢复默认值"  
    on_press:
      lambda: |-
        // 恢复到YAML中的初始值
        id(bl0906_1)->restore_default_calibration();
```

## 13. 注意事项

1. **分区大小考虑**
   - 16KB足够存储128个校准项
   - 每个校准项4字节（地址1+保留1+值2）
   - 预留空间用于未来扩展

2. **版本管理**
   - 校准版和量产版使用相同的数据格式
   - 通过编译标志控制功能差异
   - 量产版代码更精简，启动更快

3. **性能优化建议**
   - 量产版只在启动时读取一次
   - 校准版使用缓存减少Flash读取
   - 批量更新时使用事务机制

4. **错误处理**
   - Flash读取失败时的处理策略
   - 量产版必须有可靠的默认值
   - 校准版可回退到YAML配置

5. **开发流程**
   - 使用校准版进行设备校准
   - 校准完成后数据存储在Flash
   - 量产时切换到量产版固件
   - 量产版自动读取已保存的校准数据 