# 电量持久化存储配置 - 替换原有的globals系统
# 新的持久化系统直接在BL0906Factory组件中实现，无需globals

# 启用preferences组件用于数据持久化
preferences:
  flash_write_interval: 1min  # 每分钟写入一次flash，平衡数据安全和flash寿命

# Time component for energy statistics
external_components:
  - source:
      type: local
      path: "components"
    refresh: 0s
i2c:
  sda: 10
  scl: 18
  scan: true
  frequency: 400kHz
  id: i2c_bus

spi:
  - id: spi_bus
    mosi_pin: 6
    miso_pin: 7
    clk_pin: 19
bl0906_factory:
    communication: spi
    spi_id: spi_bus
    cs_pin: 3
    id: sensor_bl0906
    update_interval: 60s
    instance_id: 0x906B0001  # 手动指定实例ID
    calibration_mode: true
    calibration:
      enabled: true
      storage_type: eeprom
      eeprom_type: 24c02
    # EEPROM的I2C配置（独立于BL0906的SPI通信）
    i2c_id: i2c_bus
    address: 0x50
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    
    # 全局传感器
    frequency:
      name: 'BL0906 Frequency'
      icon: "mdi:sine-wave"
    temperature:
      name: 'BL0906 Temperature'
      icon: "mdi:thermometer"
    voltage:
      name: 'BL0906 Voltage'
      icon: "mdi:lightning-bolt-outline"
    
    # 总和传感器
    power_sum:
      name: "6-ch sum power"
      icon: "mdi:sigma"
      web_server:
          sorting_group_id: power
    energy_sum:
      name: "6-chs sum energy"
      icon: "mdi:sigma"
      web_server:
          sorting_group_id: energy
      accuracy_decimals: 6
      unit_of_measurement: kWh
    total_energy_sum:
      name: "6-ch Sum Total Energy"
      icon: "mdi:sigma"
      unit_of_measurement: kWh
      device_class: energy
      state_class: total_increasing
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy

    # 总电量统计传感器
    today_total_energy:
      name: "Today Total Energy"
      icon: "mdi:calendar-today"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    yesterday_total_energy:
      name: "Yesterday Total Energy"
      icon: "mdi:calendar-minus"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    week_total_energy:
      name: "Week Total Energy"
      icon: "mdi:calendar-week"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    month_total_energy:
      name: "Month Total Energy"
      icon: "mdi:calendar-month"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats
    year_total_energy:
      name: "Year Total Energy"
      icon: "mdi:calendar"
      unit_of_measurement: kWh
      device_class: energy
      accuracy_decimals: 3
      web_server:
          sorting_group_id: energy_stats

    # 通道1配置
    ch1:
      current:
        name: "${ch1} current"
        id: ch1_current
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch1} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch1_energy
        name: "${ch1} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch1} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      today_energy:
        name: "CH1 Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "CH1 Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "CH1 Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "CH1 Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "CH1 Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道2配置
    ch2:
      current:
        name: "${ch2} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch2} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch2_energy
        name: "${ch2} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch2} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      today_energy:
        name: "CH2 Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "CH2 Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "CH2 Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "CH2 Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "CH2 Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道3配置
    ch3:
      current:
        name: "${ch3} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch3} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch3_energy
        name: "${ch3} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch3} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      today_energy:
        name: "CH3 Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "CH3 Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "CH3 Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "CH3 Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "CH3 Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道4配置
    ch4:
      current:
        name: "${ch4} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch4} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch4_energy
        name: "${ch4} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch4} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      today_energy:
        name: "CH4 Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "CH4 Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "CH4 Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "CH4 Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "CH4 Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道5配置
    ch5:
      current:
        name: "${ch5} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch5} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch5_energy
        name: "${ch5} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch5} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      today_energy:
        name: "CH5 Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "CH5 Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "CH5 Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "CH5 Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "CH5 Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

    # 通道6配置
    ch6:
      current:
        name: "${ch6} current"
        icon: "mdi:current-ac"
        web_server:
            sorting_group_id: current
      power:
        name: "${ch6} power"
        icon: "mdi:flash"
        web_server:
            sorting_group_id: power
      energy:
        id: ch6_energy
        name: "${ch6} energy"
        icon: "mdi:lightning-bolt"
        web_server:
            sorting_group_id: energy
        accuracy_decimals: 6
        unit_of_measurement: kWh
      total_energy:
        name: "${ch6} Total Energy"
        icon: "mdi:counter"
        unit_of_measurement: kWh
        device_class: energy
        state_class: total_increasing
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy
      today_energy:
        name: "CH6 Today Energy"
        icon: "mdi:calendar-today"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      yesterday_energy:
        name: "CH6 Yesterday Energy"
        icon: "mdi:calendar-minus"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      week_energy:
        name: "CH6 Week Energy"
        icon: "mdi:calendar-week"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      month_energy:
        name: "CH6 Month Energy"
        icon: "mdi:calendar-month"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats
      year_energy:
        name: "CH6 Year Energy"
        icon: "mdi:calendar"
        unit_of_measurement: kWh
        device_class: energy
        accuracy_decimals: 3
        web_server:
            sorting_group_id: energy_stats

  # 系统状态监控传感器
  - platform: template
    name: "Device Uptime"
    unit_of_measurement: "s"
    accuracy_decimals: 0
    lambda: |-
      return millis() / 1000.0;
    update_interval: 60s

  - platform: template
    name: "Free Heap"
    unit_of_measurement: "bytes"
    accuracy_decimals: 0
    lambda: |-
      return esp_get_free_heap_size();
    update_interval: 60s

button:
  - platform: template
    name: "读取校正值"
    icon: "mdi:chip"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->refresh_all_calib_numbers();
  - platform: template
    name: "Update bl0906 Sensors"
    on_press:
      then:
        - logger.log: "正在读取bl0906传感器"
        - component.update: sensor_bl0906

  # 电量持久化控制按钮
  - platform: template
    name: "Reset Total Energy"
    icon: "mdi:restart"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->reset_energy_data();
            ESP_LOGI("main", "累计电量数据已重置");

  - platform: template
    name: "Force Save Energy Data"
    icon: "mdi:content-save"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->force_save_energy_data();
            ESP_LOGI("main", "强制保存电量数据完成");

  - platform: template
    name: "Reload Energy Data"
    icon: "mdi:reload"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->reload_energy_data();
            ESP_LOGI("main", "重新加载电量数据完成");

  - platform: template
    name: "Diagnose Energy Persistence"
    icon: "mdi:diagnose"
    on_press:
      then:
        - lambda: |-
            auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            bl0906->diagnose_energy_persistence();
            ESP_LOGI("main", "电量持久化诊断完成");
      
  - platform: template
    name: "CALCULATE RMSOS"
    on_press:
      - lambda: |-
          auto bl0906 = id(sensor_bl0906);
          bl0906->enqueue_action_(&bl0906_factory::BL0906Factory::calculate_and_write_rmsos_all_channels);

  - platform: template
    name: "Save Calibration Data to Flash"
    icon: "mdi:content-save"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
        if (bl0906 != nullptr) {
          bl0906->save_all_calibration_to_flash();
          ESP_LOGI("button", "校准数据已保存到Flash");
        }
        
  - platform: template  
    name: "Verify Calibration Data"
    icon: "mdi:check-circle"
    on_press:
      lambda: |-
        auto* bl0906 = static_cast<bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
        if (bl0906 != nullptr) {
          ESP_LOGI("button", "开始验证校准数据...");
          // 触发重新读取校准寄存器
          bl0906->refresh_all_calib_numbers();
        }
  - platform: template
    name: "Read Calibration Data from Flash"
    icon: "mdi:database-search"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              ESP_LOGI("button", "开始读取持久化存储的校准数据...");
              
              // 获取实例ID
              uint32_t instance_id = bl0906->get_instance_id();
              ESP_LOGI("button", "当前实例ID: 0x%08X", instance_id);
              
              // 创建校准存储对象（根据配置的存储类型）
              std::unique_ptr<esphome::bl0906_factory::CalibrationStorageInterface> storage;
              storage.reset(new esphome::bl0906_factory::PreferenceCalibrationStorage());
              
              if (!storage->init()) {
                ESP_LOGE("button", "校准存储初始化失败");
                return;
              }
              
              // 读取校准数据
              std::vector<esphome::bl0906_factory::CalibrationEntry> entries;
              if (storage->read_instance(instance_id, entries)) {
                ESP_LOGI("button", "成功读取 %d 个校准条目:", entries.size());
                
                // 显示所有校准数据
                for (size_t i = 0; i < entries.size(); i++) {
                  const auto& entry = entries[i];
                  ESP_LOGI("button", "  [%d] 寄存器: 0x%02X, 值: %d", 
                           i, entry.register_addr, entry.value);
                }
                
                // 可选：将读取的数据应用到当前的Number组件显示
                ESP_LOGI("button", "正在更新Number组件显示...");
                bl0906->refresh_all_calib_numbers();
                
              } else {
                ESP_LOGW("button", "实例 0x%08X 的校准数据不存在或读取失败", instance_id);
              }
            } else {
              ESP_LOGE("button", "BL0906Factory组件未找到");
            }

  - platform: template
    name: "Show All Instance Calibration Data"
    icon: "mdi:database-outline"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              ESP_LOGI("button", "开始读取所有实例的校准数据...");
              
              // 创建校准存储对象
              std::unique_ptr<esphome::bl0906_factory::CalibrationStorageInterface> storage;
              storage.reset(new esphome::bl0906_factory::PreferenceCalibrationStorage());
              
              if (!storage->init()) {
                ESP_LOGE("button", "校准存储初始化失败");
                return;
              }
              
              // 获取所有实例列表
              std::vector<uint32_t> instance_list = storage->get_instance_list();
              ESP_LOGI("button", "找到 %d 个实例:", instance_list.size());
              
              // 遍历所有实例
              for (uint32_t instance_id : instance_list) {
                ESP_LOGI("button", "=== 实例 0x%08X ===", instance_id);
                
                std::vector<esphome::bl0906_factory::CalibrationEntry> entries;
                if (storage->read_instance(instance_id, entries)) {
                  ESP_LOGI("button", "  校准条目数量: %d", entries.size());
                  
                  for (size_t i = 0; i < entries.size(); i++) {
                    const auto& entry = entries[i];
                    ESP_LOGI("button", "    [%d] 寄存器: 0x%02X, 值: %d", 
                             i, entry.register_addr, entry.value);
                  }
                } else {
                  ESP_LOGW("button", "  读取实例数据失败");
                }
              }
              
              if (instance_list.empty()) {
                ESP_LOGI("button", "没有找到任何校准数据实例");
              }
            } else {
              ESP_LOGE("button", "BL0906Factory组件未找到");
            }

  - platform: template
    name: "Verify Calibration Data Integrity"
    icon: "mdi:shield-check"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              ESP_LOGI("button", "开始验证校准数据完整性...");
              
              // 创建校准存储对象
              std::unique_ptr<esphome::bl0906_factory::CalibrationStorageInterface> storage;
              storage.reset(new esphome::bl0906_factory::PreferenceCalibrationStorage());
              
              if (!storage->init()) {
                ESP_LOGE("button", "校准存储初始化失败");
                return;
              }
              
              // 验证存储完整性
              if (storage->verify()) {
                ESP_LOGI("button", "校准数据完整性验证通过");
                
                // 获取当前实例的校准数据
                uint32_t instance_id = bl0906->get_instance_id();
                std::vector<esphome::bl0906_factory::CalibrationEntry> entries;
                
                if (storage->read_instance(instance_id, entries)) {
                  ESP_LOGI("button", "当前实例 0x%08X 有 %d 个校准条目", 
                           instance_id, entries.size());
                  
                  // 检查关键校准寄存器是否存在
                  bool has_voltage_gain = false;
                  bool has_current_gain = false;
                  
                  for (const auto& entry : entries) {
                    if (entry.register_addr == 0x6D) { // CHGN_V
                      has_voltage_gain = true;
                      ESP_LOGI("button", "  电压增益校准值: %d", entry.value);
                    }
                    if (entry.register_addr >= 0x6E && entry.register_addr <= 0x73) { // CHGN_1-6
                      has_current_gain = true;
                      ESP_LOGI("button", "  通道%d电流增益校准值: %d", 
                               entry.register_addr - 0x6D, entry.value);
                    }
                  }
                  
                  if (has_voltage_gain && has_current_gain) {
                    ESP_LOGI("button", "校准数据完整，包含必要的增益校准值");
                  } else {
                    ESP_LOGW("button", "校准数据不完整，缺少关键校准值");
                  }
                } else {
                  ESP_LOGW("button", "当前实例没有校准数据");
                }
              } else {
                ESP_LOGE("button", "校准数据完整性验证失败");
              }
            } else {
              ESP_LOGE("button", "BL0906Factory组件未找到");
            }

  - platform: template
    name: "Clear EEPROM (Fix Full EEPROM)"
    icon: "mdi:delete-sweep"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              ESP_LOGI("button", "开始清除EEPROM数据...");
              
              // 创建EEPROM存储对象
              std::unique_ptr<esphome::bl0906_factory::I2CEEPROMCalibrationStorage> eeprom_storage;
              eeprom_storage.reset(new esphome::bl0906_factory::I2CEEPROMCalibrationStorage(
                  id(i2c_bus), esphome::bl0906_factory::EEPROMType::TYPE_24C02, 0x50));
              
              if (!eeprom_storage->init()) {
                ESP_LOGE("button", "EEPROM存储初始化失败");
                return;
              }
              
              // 清除EEPROM
              if (eeprom_storage->erase()) {
                ESP_LOGI("button", "EEPROM清除成功！现在可以保存校准数据了");
              } else {
                ESP_LOGE("button", "EEPROM清除失败");
              }
            } else {
              ESP_LOGE("button", "BL0906Factory组件未找到");
            }

  - platform: template
    name: "Show EEPROM Status"
    icon: "mdi:information"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              ESP_LOGI("button", "查询EEPROM状态...");
              
              // 创建EEPROM存储对象
              std::unique_ptr<esphome::bl0906_factory::I2CEEPROMCalibrationStorage> eeprom_storage;
              eeprom_storage.reset(new esphome::bl0906_factory::I2CEEPROMCalibrationStorage(
                  id(i2c_bus), esphome::bl0906_factory::EEPROMType::TYPE_24C02, 0x50));
              
              if (!eeprom_storage->init()) {
                ESP_LOGE("button", "EEPROM存储初始化失败");
                return;
              }
              
              // 获取实例列表
              auto instance_list = eeprom_storage->get_instance_list();
              size_t max_instances = eeprom_storage->get_max_instances();
              
              ESP_LOGI("button", "=== EEPROM状态信息 ===");
              ESP_LOGI("button", "EEPROM型号: 24C02 (256字节)");
              ESP_LOGI("button", "最大实例数: %d", max_instances);
              ESP_LOGI("button", "当前实例数: %d", instance_list.size());
              ESP_LOGI("button", "剩余可用槽位: %d", max_instances - instance_list.size());
              
              if (!instance_list.empty()) {
                ESP_LOGI("button", "已存储的实例:");
                for (size_t i = 0; i < instance_list.size(); i++) {
                  ESP_LOGI("button", "  [%d] 实例ID: 0x%08X", i, instance_list[i]);
                }
              } else {
                ESP_LOGI("button", "EEPROM为空，可以保存新实例");
              }
              
              uint32_t current_instance = bl0906->get_instance_id();
              ESP_LOGI("button", "当前配置的实例ID: 0x%08X", current_instance);
            } else {
              ESP_LOGE("button", "BL0906Factory组件未找到");
            }

  - platform: template
    name: "Debug EEPROM Header"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              // 创建EEPROM存储对象
              std::unique_ptr<esphome::bl0906_factory::I2CEEPROMCalibrationStorage> eeprom_storage;
              eeprom_storage.reset(new esphome::bl0906_factory::I2CEEPROMCalibrationStorage(
                  id(i2c_bus), esphome::bl0906_factory::EEPROMType::TYPE_24C02, 0x50));
              
              if (!eeprom_storage->init()) {
                ESP_LOGE("button", "EEPROM存储初始化失败");
                return;
              }
              
              // 读取原始头部数据
              uint8_t header_data[16];
              uint8_t addr = 0x00;
              auto err = id(i2c_bus)->write(0x50, &addr, 1, false);
              if (err == esphome::i2c::ERROR_OK) {
                err = id(i2c_bus)->read(0x50, header_data, 16);
                if (err == esphome::i2c::ERROR_OK) {
                  ESP_LOGI("button", "EEPROM头部原始数据:");
                  for (int i = 0; i < 16; i++) {
                    ESP_LOGI("button", "  字节[%d] = 0x%02X (%d)", i, header_data[i], header_data[i]);
                  }
                  
                  // 打印关键字段
                  uint8_t instance_count = header_data[4]; // 假设instance_count在偏移量4
                  uint8_t max_instances = header_data[3];  // 假设max_instances在偏移量3
                  ESP_LOGI("button", "instance_count = %d, max_instances = %d", instance_count, max_instances);
                  ESP_LOGI("button", "判断条件: %d >= %d = %s", 
                           instance_count, max_instances, 
                           (instance_count >= max_instances) ? "true" : "false");
                }
              }
            }
  - platform: template
    name: "Reset EEPROM Header"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              // 创建EEPROM存储对象
              std::unique_ptr<esphome::bl0906_factory::I2CEEPROMCalibrationStorage> eeprom_storage;
              eeprom_storage.reset(new esphome::bl0906_factory::I2CEEPROMCalibrationStorage(
                  id(i2c_bus), esphome::bl0906_factory::EEPROMType::TYPE_24C02, 0x50));
              
              // 直接写入新头部
              uint8_t header_data[20] = {0};
              // 设置魔数 (0x24C0CA10 | 0x02)
              header_data[0] = 0x12;
              header_data[1] = 0xCA;
              header_data[2] = 0xC0;
              header_data[3] = 0x24;
              // 版本3
              header_data[4] = 3;
              header_data[5] = 0;
              // EEPROM类型
              header_data[6] = 0x02;
              // 最大实例数
              header_data[7] = 1;
              // 当前实例数
              header_data[8] = 0;
              
              // 写入头部
              uint8_t addr = 0x00;
              auto err = id(i2c_bus)->write(0x50, &addr, 1, false);
              if (err == esphome::i2c::ERROR_OK) {
                err = id(i2c_bus)->write(0x50, header_data, 20, true);
                if (err == esphome::i2c::ERROR_OK) {
                  ESP_LOGI("button", "成功重置EEPROM头部");
                } else {
                  ESP_LOGE("button", "写入EEPROM头部失败");
                }
              } else {
                ESP_LOGE("button", "设置EEPROM地址失败");
              }
              
              // 等待写入完成
              delay(10);
              
              // 重新初始化
              if (eeprom_storage->init()) {
                ESP_LOGI("button", "EEPROM重新初始化成功");
              } else {
                ESP_LOGE("button", "EEPROM重新初始化失败");
              }
            }
  - platform: template
    name: "Debug EEPROM Header Correctly"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              // 创建EEPROM存储对象
              std::unique_ptr<esphome::bl0906_factory::I2CEEPROMCalibrationStorage> eeprom_storage;
              eeprom_storage.reset(new esphome::bl0906_factory::I2CEEPROMCalibrationStorage(
                  id(i2c_bus), esphome::bl0906_factory::EEPROMType::TYPE_24C02, 0x50));
              
              if (!eeprom_storage->init()) {
                ESP_LOGE("button", "EEPROM存储初始化失败");
                return;
              }
              
              // 读取原始头部数据
              uint8_t header_data[20];
              uint8_t addr = 0x00;
              auto err = id(i2c_bus)->write(0x50, &addr, 1, false);
              if (err == esphome::i2c::ERROR_OK) {
                err = id(i2c_bus)->read(0x50, header_data, 20);
                if (err == esphome::i2c::ERROR_OK) {
                  ESP_LOGI("button", "EEPROM头部原始数据:");
                  for (int i = 0; i < 20; i++) {
                    ESP_LOGI("button", "  字节[%d] = 0x%02X (%d)", i, header_data[i], header_data[i]);
                  }
                  
                  // 正确解析头部字段
                  uint32_t magic = header_data[0] | (header_data[1] << 8) | 
                                  (header_data[2] << 16) | (header_data[3] << 24);
                  uint16_t version = header_data[4] | (header_data[5] << 8);
                  uint8_t eeprom_type = header_data[6];
                  uint8_t max_instances = header_data[7];
                  uint8_t instance_count = header_data[8];
                  
                  ESP_LOGI("button", "正确解析: magic=0x%08X, version=%d", magic, version);
                  ESP_LOGI("button", "正确解析: eeprom_type=0x%02X, max_instances=%d, instance_count=%d", 
                           eeprom_type, max_instances, instance_count);
                  ESP_LOGI("button", "判断条件: %d >= %d = %s", 
                           instance_count, max_instances, 
                           (instance_count >= max_instances) ? "true" : "false");
                }
              }
            }
  - platform: template
    name: "Fix EEPROM Header"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              // 创建EEPROM存储对象
              std::unique_ptr<esphome::bl0906_factory::I2CEEPROMCalibrationStorage> eeprom_storage;
              eeprom_storage.reset(new esphome::bl0906_factory::I2CEEPROMCalibrationStorage(
                  id(i2c_bus), esphome::bl0906_factory::EEPROMType::TYPE_24C02, 0x50));
              
              // 直接写入正确的头部
              uint8_t header_data[20] = {0};
              // 设置魔数 (0x24C0CA10 | 0x02)
              header_data[0] = 0x12;
              header_data[1] = 0xCA;
              header_data[2] = 0xC0;
              header_data[3] = 0x24;
              // 版本3
              header_data[4] = 3;
              header_data[5] = 0;
              // EEPROM类型
              header_data[6] = 0x02;
              // 最大实例数
              header_data[7] = 1;
              // 当前实例数 - 关键修复点
              header_data[8] = 0;  // 设置为0，表示没有实例
              
              // 写入头部
              uint8_t addr = 0x00;
              auto err = id(i2c_bus)->write(0x50, &addr, 1, false);
              if (err == esphome::i2c::ERROR_OK) {
                err = id(i2c_bus)->write(0x50, header_data, 20, true);
                if (err == esphome::i2c::ERROR_OK) {
                  ESP_LOGI("button", "成功修复EEPROM头部");
                  ESP_LOGI("button", "instance_count已设置为0");
                } else {
                  ESP_LOGE("button", "写入EEPROM头部失败");
                }
              } else {
                ESP_LOGE("button", "设置EEPROM地址失败");
              }
              
              // 等待写入完成
              delay(10);
              
              // 重新初始化
              if (eeprom_storage->init()) {
                ESP_LOGI("button", "EEPROM重新初始化成功");
              } else {
                ESP_LOGE("button", "EEPROM重新初始化失败");
              }
            }

  - platform: template
    name: "Check EEPROM After Init"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              // 创建EEPROM存储对象
              std::unique_ptr<esphome::bl0906_factory::I2CEEPROMCalibrationStorage> eeprom_storage;
              eeprom_storage.reset(new esphome::bl0906_factory::I2CEEPROMCalibrationStorage(
                  id(i2c_bus), esphome::bl0906_factory::EEPROMType::TYPE_24C02, 0x50));
              
              // 初始化并检查结果
              bool init_result = eeprom_storage->init();
              ESP_LOGI("button", "EEPROM初始化结果: %s", init_result ? "成功" : "失败");
              
              // 读取初始化后的头部
              uint8_t header_data[20];
              uint8_t addr = 0x00;
              auto err = id(i2c_bus)->write(0x50, &addr, 1, false);
              if (err == esphome::i2c::ERROR_OK) {
                err = id(i2c_bus)->read(0x50, header_data, 20);
                if (err == esphome::i2c::ERROR_OK) {
                  ESP_LOGI("button", "初始化后的EEPROM头部:");
                  for (int i = 0; i < 20; i++) {
                    ESP_LOGI("button", "  字节[%d] = 0x%02X (%d)", i, header_data[i], header_data[i]);
                  }
                  
                  // 解析关键字段
                  uint32_t magic = header_data[0] | (header_data[1] << 8) | 
                                  (header_data[2] << 16) | (header_data[3] << 24);
                  uint16_t version = header_data[4] | (header_data[5] << 8);
                  uint8_t eeprom_type = header_data[6];
                  uint8_t max_instances = header_data[7];
                  uint8_t instance_count = header_data[8];
                  
                  ESP_LOGI("button", "初始化后: magic=0x%08X, version=%d", magic, version);
                  ESP_LOGI("button", "初始化后: eeprom_type=0x%02X, max_instances=%d, instance_count=%d", 
                           eeprom_type, max_instances, instance_count);
                }
              }
            }
  - platform: template
    name: "Fix EEPROM Header Byte Order"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              // 创建EEPROM存储对象
              std::unique_ptr<esphome::bl0906_factory::I2CEEPROMCalibrationStorage> eeprom_storage;
              eeprom_storage.reset(new esphome::bl0906_factory::I2CEEPROMCalibrationStorage(
                  id(i2c_bus), esphome::bl0906_factory::EEPROMType::TYPE_24C02, 0x50));
              
              // 直接写入正确的头部（注意字节顺序）
              uint8_t header_data[20] = {0};
              
              // 设置魔数 (0x24C0CA02) - 注意字节顺序
              header_data[0] = 0x02;  // 最低字节
              header_data[1] = 0xCA;
              header_data[2] = 0xC0;
              header_data[3] = 0x24;  // 最高字节
              
              // 版本3 (小端序)
              header_data[4] = 3;
              header_data[5] = 0;
              
              // EEPROM类型
              header_data[6] = 0x02;  // TYPE_24C02
              
              // 最大实例数
              header_data[7] = 1;     // 24C02支持1个实例
              
              // 当前实例数
              header_data[8] = 0;     // 设置为0，表示没有实例
              
              // 其余字段清零
              for (int i = 9; i < 20; i++) {
                header_data[i] = 0;
              }
              
              // 写入头部
              uint8_t addr = 0x00;
              auto err = id(i2c_bus)->write(0x50, &addr, 1, false);
              if (err == esphome::i2c::ERROR_OK) {
                err = id(i2c_bus)->write(0x50, header_data, 20, true);
                if (err == esphome::i2c::ERROR_OK) {
                  ESP_LOGI("button", "成功修复EEPROM头部（字节顺序修正）");
                  ESP_LOGI("button", "magic=0x24C0CA02, max_instances=1, instance_count=0");
                } else {
                  ESP_LOGE("button", "写入EEPROM头部失败");
                }
              } else {
                ESP_LOGE("button", "设置EEPROM地址失败");
              }
              
              // 等待写入完成
              delay(10);
              
              // 重新初始化
              if (eeprom_storage->init()) {
                ESP_LOGI("button", "EEPROM重新初始化成功");
              } else {
                ESP_LOGE("button", "EEPROM重新初始化失败");
              }
            }

  - platform: template
    name: "Dump EEPROM Content"
    on_press:
      then:
        - lambda: |-
            auto* bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
            if (bl0906 != nullptr) {
              ESP_LOGI("button", "开始读取EEPROM内容...");
              
              // 读取前64字节
              for (uint16_t base_addr = 0; base_addr < 64; base_addr += 16) {
                uint8_t data[16];
                uint8_t addr = base_addr;
                
                auto err = id(i2c_bus)->write(0x50, &addr, 1, false);
                if (err == esphome::i2c::ERROR_OK) {
                  err = id(i2c_bus)->read(0x50, data, 16);
                  if (err == esphome::i2c::ERROR_OK) {
                    char hex_line[50];
                    sprintf(hex_line, "%02X: ", base_addr);
                    for (int i = 0; i < 16; i++) {
                      sprintf(hex_line + strlen(hex_line), "%02X ", data[i]);
                    }
                    ESP_LOGI("button", "%s", hex_line);
                  } else {
                    ESP_LOGE("button", "读取EEPROM数据失败");
                  }
                } else {
                  ESP_LOGE("button", "设置EEPROM地址失败");
                }
              }
            }
number:
  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    chgn_decimal_1:
      name: "CH_1 Current Gain"

  - platform: bl0906_factory
    bl0906_factory_id: sensor_bl0906
    chgn_decimal_1:
      name: "CH_1 Current Gain"
      id: chgn_1_adjust
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_2:
      name: "CH_2 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_3:
      name: "CH_3 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_4:
      name: "CH_4 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_5:
      name: "CH_5 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_decimal_6:
      name: "CH_6 Current Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: current

    chgn_v_decimal:
      name: "Voltage Gain"
      icon: "mdi:tune-vertical"
      mode: box
      web_server:
          sorting_group_id: power

    chos_decimal_1:
      name: "CH_1 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_2:
      name: "CH_2 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_3:
      name: "CH_3 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_4:
      name: "CH_4 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_5:
      name: "CH_5 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_decimal_6:
      name: "CH_6 Current Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: current

    chos_v_decimal:
      name: "Voltage Offset"
      icon: "mdi:tune"
      mode: box
      web_server:
          sorting_group_id: power
    # 添加十进制形式的RMS校准值
    rmsgn_decimal_1:
      name: "CH_1 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_2:
      name: "CH_2 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_3:
      name: "CH_3 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_4:
      name: "CH_4 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_5:
      name: "CH_5 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsgn_decimal_6:
      name: "CH_6 RMS Gain"
      icon: "mdi:chart-bell-curve-cumulative"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_1:
      name: "CH_1 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_2:
      name: "CH_2 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_3:
      name: "CH_3 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_4:
      name: "CH_4 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_5:
      name: "CH_5 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

    rmsos_decimal_6:
      name: "CH_6 RMS Offset"
      icon: "mdi:chart-bell-curve"
      mode: box
      web_server:
          sorting_group_id: current

switch:
  - platform: gpio
    id: bl0906_nrst
    pin: 1
    name: "BL0906_NRST"
    restore_mode: always_on
  - platform: template
    name: "bl0906 update switch"
    optimistic: true
    assumed_state: true
    turn_off_action:
      - logger.log: "turn off bl0906 update"
      - component.suspend: sensor_bl0906
    turn_on_action:
      - logger.log: "turn on bl0906 update"
      - component.resume: sensor_bl0906

  # 电量持久化开关
  - platform: template
    name: "Energy Persistence"
    icon: "mdi:database"
    optimistic: true
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
          bl0906->set_energy_persistence_enabled(true);
          ESP_LOGI("main", "电量持久化存储已启用");
    turn_off_action:
      - lambda: |-
          auto bl0906 = static_cast<esphome::bl0906_factory::BL0906Factory*>(id(sensor_bl0906));
          bl0906->set_energy_persistence_enabled(false);
          ESP_LOGI("main", "电量持久化存储已禁用");

text_sensor:
  - platform: template
    name: "System Info"
    icon: "mdi:chip"
    lambda: |-
      return {"Uptime: " + to_string(millis() / 1000) + "s, Free Heap: " + to_string(esp_get_free_heap_size()) + " bytes"};
    update_interval: 60s
