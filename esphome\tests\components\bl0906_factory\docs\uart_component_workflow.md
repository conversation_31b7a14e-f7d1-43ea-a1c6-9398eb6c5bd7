# ESPHome UART组件工作流程

本文档描述了ESPHome UART组件的完整工作流程，包括初始化、配置、数据传输和调试功能。

## 组件架构流程图

```mermaid
graph TB
    subgraph "配置阶段"
        A[YAML配置文件] --> B[Python配置验证]
        B --> C[平台检测]
        C --> D{选择平台实现}
        D -->|ESP32 Arduino| E[ESP32ArduinoUARTComponent]
        D -->|ESP32 IDF| F[IDFUARTComponent]
        D -->|ESP8266| G[ESP8266UartComponent]
        D -->|RP2040| H[RP2040UartComponent]
        D -->|LibreTiny| I[LibreTinyUARTComponent]
        D -->|Host| J[HostUartComponent]
    end

    subgraph "初始化阶段"
        E --> K[setup方法]
        F --> K
        G --> K
        H --> K
        I --> K
        J --> K
        K --> L[配置GPIO引脚]
        L --> M[设置波特率]
        M --> N[配置数据位/停止位/校验位]
        N --> O[初始化缓冲区]
        O --> P[检查日志冲突]
    end

    subgraph "运行时操作"
        P --> Q[UARTComponent就绪]
        Q --> R[UARTDevice创建]
        R --> S{数据操作类型}
        
        S -->|写入| T[write_byte/write_array/write_str]
        T --> U[平台特定写入实现]
        U --> V[数据发送到TX引脚]
        
        S -->|读取| W[read_byte/read_array/peek_byte]
        W --> X[平台特定读取实现]
        X --> Y[从RX引脚接收数据]
        Y --> Z[存储到接收缓冲区]
        
        S -->|状态查询| AA[available/flush]
        AA --> BB[返回缓冲区状态]
    end

    subgraph "调试功能"
        V --> CC{启用调试?}
        Z --> CC
        CC -->|是| DD[UARTDebugger]
        DD --> EE[数据方向检测]
        EE --> FF{触发条件}
        FF -->|字节数达到| GG[触发日志输出]
        FF -->|超时| GG
        FF -->|分隔符匹配| GG
        FF -->|方向改变| GG
        GG --> HH[格式化输出]
        HH --> II{输出格式}
        II -->|十六进制| JJ[log_hex]
        II -->|字符串| KK[log_string]
        II -->|整数| LL[log_int]
        II -->|二进制| MM[log_binary]
    end

    subgraph "子组件"
        R --> NN{子组件类型}
        NN -->|按钮| OO[UARTButton]
        NN -->|开关| PP[UARTSwitch]
        NN -->|虚拟接收器| QQ[UARTDummyReceiver]
        
        OO --> RR[按钮按下时发送数据]
        PP --> SS[开关状态改变时发送数据]
        QQ --> TT[读取并丢弃数据用于调试]
    end

    subgraph "自动化操作"
        UU[UART写入动作] --> VV[UARTWriteAction]
        VV --> WW[模板化数据处理]
        WW --> T
    end
```

## 数据流程图

```mermaid
sequenceDiagram
    participant User as 用户代码
    participant Device as UARTDevice
    participant Component as UARTComponent
    participant Platform as 平台实现
    participant Hardware as 硬件UART
    participant Debugger as UARTDebugger

    Note over User,Debugger: 初始化阶段
    User->>Component: 配置UART参数
    Component->>Platform: setup()
    Platform->>Hardware: 初始化硬件UART
    
    Note over User,Debugger: 数据写入流程
    User->>Device: write_str("Hello")
    Device->>Component: write_array(data, len)
    Component->>Platform: 平台特定写入
    Platform->>Hardware: 发送到TX引脚
    
    opt 启用调试
        Platform->>Debugger: 通知数据发送
        Debugger->>Debugger: 检查触发条件
        alt 满足条件
            Debugger->>User: 触发调试输出
        end
    end
    
    Note over User,Debugger: 数据读取流程
    Hardware->>Platform: RX引脚接收数据
    Platform->>Component: 存储到缓冲区
    
    opt 启用调试
        Platform->>Debugger: 通知数据接收
        Debugger->>Debugger: 检查触发条件
        alt 满足条件
            Debugger->>User: 触发调试输出
        end
    end
    
    User->>Device: read_byte()
    Device->>Component: read_array(data, 1)
    Component->>Platform: 平台特定读取
    Platform-->>Component: 返回数据
    Component-->>Device: 返回结果
    Device-->>User: 返回字节
```

## 状态机图

```mermaid
stateDiagram-v2
    [*] --> 未初始化
    未初始化 --> 配置中: 开始配置
    配置中 --> 初始化中: 配置验证通过
    配置中 --> 错误: 配置验证失败
    初始化中 --> 就绪: setup()成功
    初始化中 --> 错误: setup()失败
    
    就绪 --> 发送中: write操作
    发送中 --> 就绪: 发送完成
    发送中 --> 错误: 发送失败
    
    就绪 --> 接收中: 有数据可读
    接收中 --> 就绪: 读取完成
    接收中 --> 错误: 读取失败
    
    就绪 --> 调试中: 启用调试
    调试中 --> 就绪: 调试输出完成
    
    错误 --> [*]: 重置
```

## 组件关系图

```mermaid
classDiagram
    class UARTComponent {
        <<abstract>>
        +write_array(data, len)
        +read_array(data, len)
        +peek_byte(data)
        +available()
        +flush()
        +set_baud_rate(rate)
        +set_tx_pin(pin)
        +set_rx_pin(pin)
    }
    
    class UARTDevice {
        -UARTComponent* parent_
        +write_byte(data)
        +write_str(str)
        +read_byte(data)
        +check_uart_settings()
    }
    
    class ESP32ArduinoUARTComponent {
        -HardwareSerial* hw_serial_
        +setup()
        +dump_config()
        +load_settings()
    }
    
    class UARTDebugger {
        -UARTDirection for_direction_
        -vector~uint8_t~ bytes_
        +set_direction(direction)
        +set_after_bytes(size)
        +set_after_timeout(timeout)
        +add_delimiter_byte(byte)
    }
    
    class UARTButton {
        +press()
    }
    
    class UARTSwitch {
        +write_state(state)
    }
    
    UARTComponent <|-- ESP32ArduinoUARTComponent
    UARTComponent <|-- IDFUARTComponent
    UARTComponent <|-- ESP8266UartComponent
    UARTDevice --> UARTComponent : uses
    UARTDebugger --> UARTComponent : monitors
    UARTButton --|> UARTDevice
    UARTSwitch --|> UARTDevice
```

## 关键特性

### 1. 多平台支持
- ESP32 (Arduino/IDF框架)
- ESP8266
- RP2040
- LibreTiny
- Host平台

### 2. 灵活的配置选项
- 波特率设置
- 数据位/停止位/校验位配置
- TX/RX引脚配置
- 缓冲区大小设置

### 3. 强大的调试功能
- 多种触发条件（字节数、超时、分隔符、方向改变）
- 多种输出格式（十六进制、字符串、整数、二进制）
- 双向数据监控

### 4. 扩展组件
- UART按钮：发送预定义数据
- UART开关：根据状态发送不同数据
- 虚拟接收器：用于协议逆向工程

### 5. 自动化集成
- 支持ESPHome自动化系统
- 模板化数据处理
- 事件触发机制 