# BL0906传感器注册修复说明

## 问题描述

在实现电量持久化存储功能时，我们在C++代码中添加了新的传感器类型：
- `TOTAL_ENERGY` - 各通道累计电量传感器
- `TOTAL_ENERGY_SUM` - 总累计电量传感器

但是忘记在Python组件注册部分添加相应的支持，导致配置文件中使用这些传感器时出现错误。

## 修复内容

### 1. 更新传感器类型枚举

**文件**: `sensor.py`

**修改前**:
```python
SENSOR_TYPES = {
    "VOLTAGE": 0,
    "FREQUENCY": 1,
    "TEMPERATURE": 2,
    "CURRENT": 3,
    "POWER": 4,
    "ENERGY": 5,
    "POWER_SUM": 6,
    "ENERGY_SUM": 7
}
```

**修改后**:
```python
SENSOR_TYPES = {
    "VOLTAGE": 0,
    "FREQUENCY": 1,
    "TEMPERATURE": 2,
    "CURRENT": 3,
    "POWER": 4,
    "ENERGY": 5,
    "POWER_SUM": 6,
    "ENERGY_SUM": 7,
    "TOTAL_ENERGY": 8,      # 各通道累计电量
    "TOTAL_ENERGY_SUM": 9   # 总累计电量
}
```

### 2. 添加总累计电量传感器配置

**新增配置**:
```python
# 总累计电量传感器（持久化存储）
"total_energy_sum": {
    "type": "TOTAL_ENERGY_SUM",
    "unit": UNIT_KILOWATT_HOURS,
    "accuracy": 3,
    "device_class": DEVICE_CLASS_ENERGY,
    "state_class": STATE_CLASS_TOTAL_INCREASING,
},
```

### 3. 添加各通道累计电量传感器模板

**新增模板**:
```python
# 各通道累计电量传感器（持久化存储）
"total_energy": {
    "type": "TOTAL_ENERGY",
    "unit": UNIT_KILOWATT_HOURS,
    "accuracy": 3,
    "device_class": DEVICE_CLASS_ENERGY,
    "state_class": STATE_CLASS_TOTAL_INCREASING,
},
```

## 修复后的功能

### 1. 支持的传感器类型

现在组件完整支持以下传感器类型：

**基础传感器**:
- `voltage` - 电压
- `frequency` - 频率  
- `temperature` - 温度

**通道传感器** (1-6):
- `current_1` ~ `current_6` - 各通道电流
- `power_1` ~ `power_6` - 各通道功率
- `energy_1` ~ `energy_6` - 各通道瞬时电量
- `total_energy_1` ~ `total_energy_6` - 各通道累计电量 ✨**新增**

**总和传感器**:
- `power_sum` - 总功率
- `energy_sum` - 总瞬时电量
- `total_energy_sum` - 总累计电量 ✨**新增**

### 2. 配置示例

**正确的配置方式**:
```yaml
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_component
    
    # 各通道累计电量传感器
    total_energy_1:
      name: "Channel 1 Total Energy"
      unit_of_measurement: kWh
      device_class: energy
      state_class: total_increasing
      accuracy_decimals: 3
    
    total_energy_2:
      name: "Channel 2 Total Energy"
      unit_of_measurement: kWh
      device_class: energy
      state_class: total_increasing
      accuracy_decimals: 3
    
    # ... 其他通道
    
    # 总累计电量传感器
    total_energy_sum:
      name: "Total Accumulated Energy"
      unit_of_measurement: kWh
      device_class: energy
      state_class: total_increasing
      accuracy_decimals: 3
```

### 3. 自动配置特性

由于使用了数据驱动的配置系统，新的传感器会自动获得：

- ✅ 正确的单位 (`kWh`)
- ✅ 正确的设备类 (`energy`)
- ✅ 正确的状态类 (`total_increasing`)
- ✅ 合适的精度 (3位小数)
- ✅ Home Assistant完美集成

## 测试验证

### 1. 创建测试配置

已创建 `test_sensor_registration.yaml` 文件来验证所有传感器类型的注册。

### 2. 验证步骤

1. **编译测试**:
   ```bash
   esphome compile test_sensor_registration.yaml
   ```

2. **检查生成的C++代码**:
   确认所有传感器都正确注册到BL0906Factory组件。

3. **运行时测试**:
   部署到设备后检查所有传感器是否正常工作。

## 错误排除

### 1. 常见错误

**错误信息**:
```
Unknown sensor type: total_energy_1
```

**原因**: sensor.py中缺少对应的传感器类型定义

**解决**: 确保sensor.py中包含了所有新增的传感器类型

### 2. 配置验证

**检查点**:
- ✅ `SENSOR_TYPES` 包含所有传感器类型
- ✅ `SENSOR_CONFIGS` 包含基础传感器配置
- ✅ `CHANNEL_SENSOR_TEMPLATES` 包含通道传感器模板
- ✅ 动态生成逻辑正确处理所有模板

### 3. C++与Python同步

**确保一致性**:
- C++头文件中的枚举值与Python中的映射一致
- 传感器类型名称完全匹配
- 通道索引处理方式一致

## 总结

通过这次修复，我们完善了BL0906组件的传感器注册系统，确保：

1. **完整性**: 所有C++中定义的传感器类型都有对应的Python注册支持
2. **一致性**: C++和Python代码保持同步
3. **可用性**: 用户可以在配置文件中正常使用所有传感器类型
4. **可维护性**: 数据驱动的配置系统便于后续扩展

这个修复解决了配置文件出错的根本原因，确保了电量持久化存储功能的完整可用性。
