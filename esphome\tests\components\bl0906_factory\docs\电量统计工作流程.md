# BL0906 电量统计管理器工作流程

## 概述

BL0906电量统计管理器是一个复杂的系统，负责管理6个通道的电量统计数据，包括昨日、今日、本周、本月、本年的电量统计。系统采用线程安全设计，支持实时更新和持久化存储。

## 核心数据结构

### 1. 电量转换系数
- **Ke**: 3600000.0f * 16 * Kp / (4194304.0f * 0.032768f * 16) kWh/pulse
- **Ke_sum**: Ke/16 (总电量转换系数)

### 2. 统计周期
- 昨日 (YESTERDAY)
- 今日 (TODAY) 
- 本周 (THIS_WEEK)
- 本月 (THIS_MONTH)
- 本年 (THIS_YEAR)

### 3. 数据存储结构
```cpp
struct OptimizedEnergyStatistics {
  CompactTimeSnapshot period_times[5];           // 各周期起始时间
  uint32_t period_persistent_cf_count[7][5];     // 各通道各周期起始CF_count
  uint32_t current_persistent_cf_count[7];       // 当前持久化CF_count
  time_t last_update_timestamp;                  // 上次更新时间戳
  std::atomic<bool> updating;                    // 更新标志
}
```

## 系统架构图

```mermaid
graph TB
    subgraph "BL0906Factory"
        BL0906[BL0906芯片]
        Factory[BL0906Factory主控制器]
    end
    
    subgraph "EnergyStatisticsManager"
        ESM[电量统计管理器]
        TimeComp[时间组件]
        Mutex[互斥锁]
        AtomicData[原子数据]
    end
    
    subgraph "数据存储"
        UnifiedStats[统一统计数据]
        Sensors[传感器数组]
        Persistence[持久化存储]
    end
    
    BL0906 --> Factory
    Factory --> ESM
    TimeComp --> ESM
    ESM --> UnifiedStats
    ESM --> Sensors
    ESM --> Persistence
    
    Factory -.->|save_energy_data| Persistence
    Factory -.->|load_energy_data| Persistence
```

## 初始化流程

```mermaid
sequenceDiagram
    participant Main as 主程序
    participant ESM as EnergyStatisticsManager
    participant Factory as BL0906Factory
    participant Time as TimeComponent
    participant Storage as 持久化存储
    
    Main->>ESM: 创建实例(parent)
    Main->>ESM: set_time_component()
    Main->>ESM: setup()
    
    ESM->>Factory: load_energy_data()
    Factory->>Storage: 读取统计数据
    Storage-->>Factory: 返回数据
    Factory-->>ESM: 加载完成
    
    ESM->>Time: 检查时间有效性
    Time-->>ESM: 时间状态
    
    alt 时间有效
        ESM->>ESM: create_current_snapshot_safe()
        Note over ESM: 创建初始时间快照
    end
    
    ESM->>ESM: initialized_.store(true)
    ESM-->>Main: 初始化完成
```

## CF_count更新流程

```mermaid
flowchart TD
    Start([开始]) --> GetCF[获取CF_count增量]
    GetCF --> ValidateChannel{验证通道有效性}
    
    ValidateChannel -->|无效| LogError[记录错误日志]
    ValidateChannel -->|有效| UpdateAtomic[原子更新CF_count]
    
    UpdateAtomic --> IsSum{是否为总和通道?}
    IsSum -->|是| UpdateSum[更新总和CF_count<br/>索引6]
    IsSum -->|否| UpdateChannel[更新通道CF_count<br/>索引0-5]
    
    UpdateSum --> LogDebug[记录调试日志]
    UpdateChannel --> LogDebug
    LogDebug --> End([结束])
    LogError --> End
    
    style UpdateAtomic fill:#e1f5fe
    style UpdateSum fill:#f3e5f5
    style UpdateChannel fill:#f3e5f5
```

## 周期变化检测流程

```mermaid
flowchart TD
    Start([定时检查]) --> CheckInit{系统已初始化?}
    CheckInit -->|否| End([结束])
    CheckInit -->|是| CheckTime{时间组件有效?}
    
    CheckTime -->|否| End
    CheckTime -->|是| GetCurrentTime[获取当前时间]
    
    GetCurrentTime --> CheckInterval{距离上次检查<br/>超过1分钟?}
    CheckInterval -->|否| End
    CheckInterval -->|是| UpdateTimestamp[更新检查时间戳]
    
    UpdateTimestamp --> FirstCheck{是否首次检查?}
    FirstCheck -->|是| End
    FirstCheck -->|否| CheckYear{检查年份变化}
    
    CheckYear -->|新年| HandleNewYear[处理新年]
    CheckYear -->|否| CheckMonth{检查月份变化}
    
    CheckMonth -->|新月| HandleNewMonth[处理新月]
    CheckMonth -->|否| CheckWeek{检查周变化}
    
    CheckWeek -->|新周| HandleNewWeek[处理新周]
    CheckWeek -->|否| CheckDay{检查日期变化}
    
    CheckDay -->|新日| HandleNewDay[处理新日]
    CheckDay -->|否| End
    
    HandleNewYear --> End
    HandleNewMonth --> End
    HandleNewWeek --> End
    HandleNewDay --> End
    
    style HandleNewYear fill:#ffebee
    style HandleNewMonth fill:#fff3e0
    style HandleNewWeek fill:#e8f5e8
    style HandleNewDay fill:#e3f2fd
```

## 新日处理详细流程

```mermaid
sequenceDiagram
    participant Timer as 定时器
    participant ESM as EnergyStatisticsManager
    participant Mutex as 互斥锁
    participant Stats as 统计数据
    participant Atomic as 原子变量
    
    Timer->>ESM: handle_new_day()
    ESM->>Mutex: lock()
    
    ESM->>Stats: 检查updating标志
    alt 正在更新中
        ESM->>ESM: 记录警告并返回
    else 可以更新
        ESM->>Stats: updating.exchange(true)
        ESM->>ESM: 获取当前时间
        ESM->>ESM: 创建时间快照
        
        Note over ESM: 更新时间点快照
        ESM->>Stats: period_times[0] = period_times[1]
        ESM->>Stats: period_times[1] = 当前时间快照
        
        Note over ESM: 更新CF_count快照
        loop 所有通道(0-6)
            ESM->>Atomic: 读取current_persistent_cf_count_[i]
            ESM->>Stats: period_persistent_cf_count[i][0] = period_persistent_cf_count[i][1]
            ESM->>Stats: period_persistent_cf_count[i][1] = 当前值
            ESM->>Stats: current_persistent_cf_count[i] = 当前值
        end
        
        ESM->>Stats: last_update_timestamp = 当前时间戳
        ESM->>Stats: updating.store(false)
    end
    
    ESM->>Mutex: unlock()
    ESM-->>Timer: 处理完成
```

## 电量计算流程

```mermaid
flowchart TD
    Start([计算电量]) --> ValidateChannel{验证通道}
    ValidateChannel -->|无效| Return0[返回0.0]
    ValidateChannel -->|有效| LockMutex[获取互斥锁]
    
    LockMutex --> GetPeriod{确定统计周期}
    
    GetPeriod -->|昨日| GetYesterday[start_cf = period[0]<br/>current_cf = period[1]]
    GetPeriod -->|今日| GetToday[start_cf = period[1]<br/>current_cf = 当前值]
    GetPeriod -->|本周| GetWeek[start_cf = period[2]<br/>current_cf = 当前值]
    GetPeriod -->|本月| GetMonth[start_cf = period[3]<br/>current_cf = 当前值]
    GetPeriod -->|本年| GetYear[start_cf = period[4]<br/>current_cf = 当前值]
    
    GetYesterday --> CalcDiff[计算CF_count差值]
    GetToday --> CalcDiff
    GetWeek --> CalcDiff
    GetMonth --> CalcDiff
    GetYear --> CalcDiff
    
    CalcDiff --> CheckOverflow{检查溢出}
    CheckOverflow -->|正常| NormalCalc[count_diff = current - start]
    CheckOverflow -->|溢出| OverflowCalc[count_diff = (0xFFFFFFFF - start) + current + 1]
    
    NormalCalc --> ConvertEnergy[电量 = count_diff / Ke]
    OverflowCalc --> ConvertEnergy
    
    ConvertEnergy --> ValidateResult{电量值有效?}
    ValidateResult -->|负值| Reset0[重置为0并记录警告]
    ValidateResult -->|有效| ReturnEnergy[返回电量值]
    
    Reset0 --> ReturnEnergy
    ReturnEnergy --> End([结束])
    Return0 --> End
    
    style ConvertEnergy fill:#e1f5fe
    style CheckOverflow fill:#fff3e0
    style ValidateResult fill:#f3e5f5
```

## 传感器更新流程

```mermaid
graph TD
    Start([update_sensors_on_save]) --> CheckInit{系统已初始化?}
    CheckInit -->|否| End([结束])
    CheckInit -->|是| LoopChannels[遍历所有通道0-6]
    
    LoopChannels --> CheckYesterday{昨日传感器存在?}
    CheckYesterday -->|是| CalcYesterday[计算昨日电量]
    CheckYesterday -->|否| CheckToday{今日传感器存在?}
    
    CalcYesterday --> PublishYesterday[发布昨日电量]
    PublishYesterday --> CheckToday
    
    CheckToday -->|是| CalcToday[计算今日电量]
    CheckToday -->|否| CheckWeek{本周传感器存在?}
    
    CalcToday --> PublishToday[发布今日电量]
    PublishToday --> CheckWeek
    
    CheckWeek -->|是| CalcWeek[计算本周电量]
    CheckWeek -->|否| CheckMonth{本月传感器存在?}
    
    CalcWeek --> PublishWeek[发布本周电量]
    PublishWeek --> CheckMonth
    
    CheckMonth -->|是| CalcMonth[计算本月电量]
    CheckMonth -->|否| CheckYear{本年传感器存在?}
    
    CalcMonth --> PublishMonth[发布本月电量]
    PublishMonth --> CheckYear
    
    CheckYear -->|是| CalcYear[计算本年电量]
    CheckYear -->|否| NextChannel{下一个通道?}
    
    CalcYear --> PublishYear[发布本年电量]
    PublishYear --> NextChannel
    
    NextChannel -->|有| LoopChannels
    NextChannel -->|无| LogComplete[记录完成日志]
    LogComplete --> End
    
    style CalcYesterday fill:#ffebee
    style CalcToday fill:#e3f2fd
    style CalcWeek fill:#e8f5e8
    style CalcMonth fill:#fff3e0
    style CalcYear fill:#f3e5f5
```

## 数据持久化流程

```mermaid
sequenceDiagram
    participant ESM as EnergyStatisticsManager
    participant Factory as BL0906Factory
    participant Storage as 存储系统
    participant Mutex as 互斥锁
    
    Note over ESM: 保存流程
    ESM->>ESM: update_statistics_on_save()
    ESM->>Mutex: lock(statistics_mutex_)
    
    ESM->>ESM: 检查updating标志
    alt 正在更新
        ESM->>ESM: 记录警告并跳过
    else 可以更新
        ESM->>ESM: updating.exchange(true)
        
        loop 所有通道
            ESM->>ESM: 更新current_persistent_cf_count
        end
        
        ESM->>ESM: 更新last_update_timestamp
        ESM->>ESM: updating.store(false)
    end
    
    ESM->>Mutex: unlock()
    ESM->>Factory: save_energy_data()
    Factory->>Storage: 写入统计数据
    Storage-->>Factory: 保存完成
    Factory-->>ESM: 保存完成
    
    Note over ESM: 加载流程
    ESM->>Factory: load_energy_data()
    Factory->>Storage: 读取统计数据
    Storage-->>Factory: 返回数据
    Factory->>ESM: 设置统计数据
    ESM-->>Factory: 加载完成
```

## 线程安全机制

```mermaid
graph TB
    subgraph "线程安全保护"
        Mutex[statistics_mutex_<br/>互斥锁]
        AtomicBool[updating标志<br/>原子布尔]
        AtomicCF[current_persistent_cf_count_<br/>原子数组]
        AtomicTime[last_check_timestamp_<br/>原子时间戳]
    end
    
    subgraph "并发操作"
        Update[数据更新操作]
        Read[数据读取操作]
        Period[周期变化处理]
        Save[保存操作]
    end
    
    Update --> Mutex
    Read --> Mutex
    Period --> Mutex
    Save --> Mutex
    
    Update --> AtomicBool
    Period --> AtomicBool
    Save --> AtomicBool
    
    Update --> AtomicCF
    Read --> AtomicCF
    
    Period --> AtomicTime
    
    style Mutex fill:#ffebee
    style AtomicBool fill:#e3f2fd
    style AtomicCF fill:#e8f5e8
    style AtomicTime fill:#fff3e0
```

## 关键特性

### 1. 线程安全
- 使用`std::mutex`保护统计数据
- 使用`std::atomic`保护关键变量
- 防止并发更新冲突

### 2. 溢出处理
- CF_count为32位无符号整数
- 自动检测和处理溢出情况
- 确保电量计算的连续性

### 3. 时间管理
- 基于ESPTime的精确时间处理
- 支持跨年、跨月、跨周、跨日检测
- 紧凑的时间快照存储

### 4. 数据优化
- 统一的数据结构减少内存占用
- 原子操作提高性能
- 延迟保存机制减少I/O操作

### 5. 错误处理
- 完善的参数验证
- 详细的日志记录
- 优雅的错误恢复机制

## 使用示例

```cpp
// 初始化
EnergyStatisticsManager esm(bl0906_factory);
esm.set_time_component(time_component);
esm.setup();

// 更新CF_count
esm.update_persistent_cf_count(0, 100);  // 通道1增加100个脉冲
esm.update_persistent_cf_count_sum(600); // 总和增加600个脉冲

// 设置传感器
esm.set_sensor(StatisticsSensorType::TODAY_ENERGY, today_sensor, 0);
esm.set_sensor(StatisticsSensorType::YESTERDAY_ENERGY, yesterday_sensor, 0);

// 定期检查
esm.check_period_changes();

// 保存时更新
esm.update_statistics_on_save();
esm.update_sensors_on_save();
``` 