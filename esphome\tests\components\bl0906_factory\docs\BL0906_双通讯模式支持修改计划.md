# BL0906_Factory 双通讯模式支持修改计划

## 1. 整体架构设计

### 1.1 条件编译架构
使用条件编译宏来选择通讯方式，在编译时确定使用UART还是SPI：

```cpp
// bl0906_factory.h 中的条件编译
#ifdef USE_BL0906_FACTORY_SPI
  #include "esphome/components/spi/spi.h"
  #define BL0906_FACTORY_COMM_BASE spi::SPIDevice
#else
  #include "esphome/components/uart/uart.h"
  #define BL0906_FACTORY_COMM_BASE uart::UARTDevice
#endif
```

### 1.2 统一类定义
```cpp
class BL0906Factory : public PollingComponent, public BL0906_FACTORY_COMM_BASE {
#ifdef USE_BL0906_FACTORY_SPI
  // SPI 相关成员变量和方法
private:
  static const uint8_t BL0906_SPI_READ_CMD = 0x82;
  static const uint8_t BL0906_SPI_WRITE_CMD = 0x81;
  int32_t spi_send_read_command_and_receive(uint8_t address, bool* success);
  bool spi_write_register_value(uint8_t address, int16_t value);
#else
  // UART 相关成员变量和方法 (保留现有实现)
private:
  static const uint8_t BL0906_UART_READ_CMD = 0x35;
  static const uint8_t BL0906_UART_WRITE_CMD = 0xCA;
  // 保持现有的UART方法不变
#endif
  
  // 其他成员保持不变...
  State current_state_{State::IDLE};
  RawSensorData raw_data_;
  // ... 所有现有成员变量和方法
};
```

### 1.3 条件编译优势
- **代码体积小**：只编译需要的通讯代码
- **运行时效率高**：无虚函数调用开销
- **维护简单**：避免复杂的抽象层
- **配置清晰**：编译时就确定通讯方式

## 2. 主要修改内容

### 2.1 修改BL0906Factory主类 (bl0906_factory.h)
```cpp
// 条件编译头文件包含
#ifdef USE_BL0906_FACTORY_SPI
  #include "esphome/components/spi/spi.h"
  #define BL0906_FACTORY_COMM_BASE spi::SPIDevice
#else
  #include "esphome/components/uart/uart.h"
  #define BL0906_FACTORY_COMM_BASE uart::UARTDevice  
#endif

class BL0906Factory : public PollingComponent, public BL0906_FACTORY_COMM_BASE {
public:
  // 现有的传感器设置方法保持不变
  void set_sensor(SensorType type, sensor::Sensor *sensor, int channel = 0);
  // ... 其他现有方法保持不变
  
  void loop() override;
  void update() override;
  void setup() override;

protected:
  
#ifdef USE_BL0906_FACTORY_SPI
  // SPI 特有方法 - 替换现有的UART通讯方法
  int32_t spi_send_read_command_and_receive(uint8_t address, bool* success);
  bool spi_write_register_value(uint8_t address, int16_t value);
  uint8_t calculate_spi_checksum(uint8_t cmd, uint8_t addr, uint32_t data);
  void spi_flush_rx_buffer() {} // SPI模式下无需清空缓冲区
#else
  // UART 特有方法 (保留现有实现)
  // 保持现有的 send_read_command_and_receive() 和 write_register_value() 方法不变
  void flush_rx_buffer(); // 保留UART缓冲区清空方法
#endif

  // 其他成员变量保持不变
  State current_state_{State::IDLE};
  RawSensorData raw_data_;
  std::unique_ptr<EnergyStatisticsManager> energy_stats_manager_;
  // ... 所有现有成员变量和方法保持不变
};
```

### 2.2 SPI协议具体实现要点

**写寄存器操作：**
- 帧格式：`0x81 + ADDR + DATA_H + DATA_M + DATA_L + CHECKSUM`
- 校验和：`((0x81 + ADDR + DATA_H + DATA_M + DATA_L) & 0xFF) ^ 0xFF`
- 48位时钟脉冲完成一次操作

**读寄存器操作：**
- 发送：`0x82 + ADDR` (16位)
- 接收：`DATA_H + DATA_M + DATA_L + CHECKSUM` (32位)
- 校验和：`((0x82 + ADDR + DATA_H + DATA_M + DATA_L) & 0xFF) ^ 0xFF`

### 2.3 SPI配置参数
```cpp
// SPI模式配置
spi_mode: MODE_1  // CPOL=0, CPHA=1
bit_order: MSB_FIRST
data_rate: 1.5MHz  // 最大1.5M
```

## 3. Python配置文件修改

### 3.1 修改__init__.py
```python
# 添加SPI支持的导入
from esphome.components import uart, number, i2c, spi
from esphome import pins

# 通讯方式配置 - 条件编译方式
CONF_COMMUNICATION = "communication"
COMMUNICATION_MODES = {
    "uart": "UART",
    "spi": "SPI", 
}

# 更新依赖关系 - 根据通讯方式决定
def get_dependencies(config):
    deps = []
    if config.get(CONF_COMMUNICATION) == "spi":
        deps.append("spi")
    else:
        deps.append("uart")
    return deps

# 扩展现有的配置模式
BASE_CONFIG_SCHEMA = cv.Schema({
    cv.GenerateID(): cv.declare_id(BL0906Factory),
    cv.Required(CONF_COMMUNICATION): cv.enum(COMMUNICATION_MODES, upper=True),
    cv.Optional(CONF_UPDATE_INTERVAL, default="60s"): cv.update_interval,
    cv.Optional(CONF_CALIBRATION): CALIBRATION_SCHEMA,
    cv.Optional(CONF_CALIBRATION_MODE, default=False): cv.boolean,
    cv.Optional(CONF_INITIAL_CALIBRATION): cv.ensure_list(INITIAL_CALIBRATION_SCHEMA),
    cv.Required(CONF_INSTANCE_ID): cv.hex_uint32_t,
    # UART模式配置  
    cv.Optional(CONF_UART_ID): cv.use_id(uart.UARTComponent),
    # SPI模式配置
    cv.Optional(CONF_SPI_ID): cv.use_id(spi.SPIComponent),
    cv.Optional(CONF_CS_PIN): pins.gpio_output_pin_schema,
}).extend(cv.polling_component_schema("60s"))

# 条件验证函数
def validate_communication_config(config):
    comm_mode = config[CONF_COMMUNICATION]
    if comm_mode == "uart":
        if CONF_UART_ID not in config:
            raise cv.Invalid("uart_id is required when using UART communication")
        # 移除SPI相关配置
        config.pop(CONF_SPI_ID, None)
        config.pop(CONF_CS_PIN, None)
    elif comm_mode == "spi":
        if CONF_SPI_ID not in config:
            raise cv.Invalid("spi_id is required when using SPI communication")
        # 移除UART相关配置
        config.pop(CONF_UART_ID, None)
    return config

CONFIG_SCHEMA = cv.All(
    BASE_CONFIG_SCHEMA,
    validate_communication_config,
    validate_i2c_dependency
)
```

### 3.2 代码生成逻辑
```python
async def to_code(config):
    var = cg.new_Pvariable(config[CONF_ID])
    await cg.register_component(var, config)
    
    # 添加现代化组件标识
    cg.add_define("USE_BL0906_FACTORY")
    
    # 根据通讯方式设置编译宏和注册设备
    comm_mode = config[CONF_COMMUNICATION]
    if comm_mode == "SPI":
        cg.add_define("USE_BL0906_FACTORY_SPI")
        # SPI配置
        spi_component = await cg.get_variable(config[CONF_SPI_ID])
        cg.add(var.set_spi_parent(spi_component))
        if CONF_CS_PIN in config:
            cs_pin = await cg.gpio_pin_expression(config[CONF_CS_PIN])
            cg.add(var.set_cs_pin(cs_pin))
        # 注册为SPI设备
        await spi.register_spi_device(var, config)
    else:
        # UART配置 (默认) - 保持现有的注册方式
        await uart.register_uart_device(var, config)
    
    # 保持现有的所有其他配置逻辑不变
    # 处理校准模式
    if config.get(CONF_CALIBRATION_MODE, False):
        cg.add_define("BL0906_CALIBRATION_MODE")
    
    # 处理存储类型（保持现有逻辑）
    if CONF_CALIBRATION in config:
        # ... 现有的校准配置处理逻辑保持不变
    
    # 处理实例ID（保持现有逻辑）
    instance_id = config[CONF_INSTANCE_ID]
    cg.add(var.set_instance_id(instance_id))
    
    # 处理初始校准值（保持现有逻辑）
    if CONF_INITIAL_CALIBRATION in config:
        # ... 现有的初始校准值处理逻辑保持不变
```

## 4. 文件结构规划

```
components/bl0906_factory/
├── bl0906_factory.h                    # 主类定义（修改 - 条件编译）
├── bl0906_factory.cpp                  # 主类实现（修改 - 条件编译）
├── bl0906_calibration.h               # 校准参数（保持）
├── bl0906_registers.h                 # 寄存器定义（保持）
├── energy_statistics_manager.h       # 电量统计管理（保持）
├── energy_statistics_manager.cpp     # 电量统计管理（保持）
├── calibration_storage_interface.h   # 校准存储接口（保持）
├── preference_calibration_storage.h  # Preference存储（保持）
├── preference_calibration_storage.cpp # Preference存储（保持）
├── i2c_eeprom_calibration_storage.h  # I2C EEPROM存储（保持）
├── i2c_eeprom_calibration_storage.cpp # I2C EEPROM存储（保持）
├── bl0906_number.h                   # 数字组件（保持）
├── bl0906_number.cpp                 # 数字组件（保持）
├── sensor.py                         # 传感器定义（保持）
├── number.py                         # 数字组件定义（保持）
└── __init__.py                       # 配置文件（修改 - 支持通讯模式选择）
```

## 5. 配置示例

### 5.1 UART模式配置
```yaml
uart:
  tx_pin: GPIO1
  rx_pin: GPIO3
  baud_rate: 9600

bl0906_factory:
  communication: uart
  uart_id: uart_bus
  instance_id: 0x12345678
  
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: my_bl0906
    voltage:
      name: "Voltage"
```

### 5.2 SPI模式配置  
```yaml
spi:
  clk_pin: GPIO18
  mosi_pin: GPIO23
  miso_pin: GPIO19

bl0906_factory:
  communication: spi
  spi_id: spi_bus
  cs_pin: GPIO5
  instance_id: 0x12345678
  
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: my_bl0906
    voltage:
      name: "Voltage"
```

## 6. 实现优先级

1. **第一阶段**：在现有bl0906_factory.h中添加条件编译宏和SPI相关头文件
2. **第二阶段**：在bl0906_factory.cpp中实现SPI通讯方法（条件编译）
3. **第三阶段**：修改Python配置文件支持communication参数和编译宏设置
4. **第四阶段**：测试两种通讯模式并优化

## 7. 关键技术点

### 7.1 SPI时序控制
- 确保CPOL=0, CPHA=1的严格时序
- 正确处理48位传输周期
- CS信号的正确控制

### 7.2 数据校验
- 实现校验和计算和验证
- 错误帧的处理机制
- SPI复位机制的实现

### 7.3 性能优化
- 避免阻塞操作
- 状态机的高效切换
- 内存使用的优化

## 8. SPI通讯协议详细说明

### 8.1 通讯模式特点
- 从模式，半双工通讯，最大通讯速率1.5M
- 8-bit数据传输，MSB在前，LSB在后
- 固定时钟极性/相位（CPOL=0，CPHA=1）

### 8.2 帧结构详解

#### 写寄存器操作帧结构：
| 字节1 | 字节2 | 字节3 | 字节4 | 字节5 | 字节6 |
|-------|-------|-------|-------|-------|-------|
| 0x81 | ADDR[7:0] | DATA_H[7:0] | DATA_M[7:0] | DATA_L[7:0] | CHECKSUM[7:0] |

#### 读寄存器操作发送帧：
| 字节1 | 字节2 |
|-------|-------|
| 0x82 | ADDR[7:0] |

#### 读寄存器操作返回帧：
| 字节1 | 字节2 | 字节3 | 字节4 |
|-------|-------|-------|-------|
| DATA_H[7:0] | DATA_M[7:0] | DATA_L[7:0] | CHECKSUM[7:0] |

### 8.3 容错机制
1. **数据校验**：如果帧识别字节错误或SUM字节错误，则该帧数据放弃
2. **SPI模块复位**：通过SPI接口下发6个字节的0xFF，可单独对SPI接口进行复位
3. **CS复位**：CS拉高复位

## 9. 条件编译实现细节

### 9.1 bl0906_factory.cpp 中的条件编译实现
```cpp
// 修改现有的 send_read_command_and_receive 方法以支持SPI
int32_t BL0906Factory::send_read_command_and_receive(uint8_t address, bool* success) {
#ifdef USE_BL0906_FACTORY_SPI
  return spi_send_read_command_and_receive(address, success);
#else
  // 保留现有的UART实现不变
  // ... 现有的UART读取逻辑
#endif
}

// 修改现有的 write_register_value 方法以支持SPI
bool BL0906Factory::write_register_value(uint8_t address, int16_t value) {
#ifdef USE_BL0906_FACTORY_SPI
  return spi_write_register_value(address, value);
#else
  // 保留现有的UART实现不变
  // ... 现有的UART写入逻辑
#endif
}

#ifdef USE_BL0906_FACTORY_SPI
int32_t BL0906Factory::spi_send_read_command_and_receive(uint8_t address, bool* success) {
  // SPI协议：发送 0x82 + ADDR，接收 DATA_H + DATA_M + DATA_L + CHECKSUM
  uint8_t tx_data[2] = {0x82, address};
  uint8_t rx_data[4];
  
  this->enable();
  this->write_array(tx_data, 2);
  this->read_array(rx_data, 4);
  this->disable();
  
  // 校验和验证
  uint8_t calculated_checksum = ((0x82 + address + rx_data[0] + rx_data[1] + rx_data[2]) & 0xFF) ^ 0xFF;
  if (calculated_checksum != rx_data[3]) {
    if (success) *success = false;
    ESP_LOGW(TAG, "SPI读取校验和错误: 地址=0x%02X, 期望=0x%02X, 实际=0x%02X", 
             address, calculated_checksum, rx_data[3]);
    return 0;
  }
  
  if (success) *success = true;
  
  // 根据寄存器类型返回正确的数据
  if (is_16bit_register(address)) {
    // 16位寄存器：只使用低16位，处理符号扩展
    int16_t value = (rx_data[1] << 8) | rx_data[2];
    return static_cast<int32_t>(value);
  } else {
    // 24位寄存器
    if (is_unsigned_register(address)) {
      // 无符号24位
      return (static_cast<uint32_t>(rx_data[0]) << 16) | 
             (static_cast<uint32_t>(rx_data[1]) << 8) | 
             static_cast<uint32_t>(rx_data[2]);
    } else {
      // 带符号24位，需要符号扩展
      int32_t value = (static_cast<int8_t>(rx_data[0]) << 16) | 
                      (static_cast<uint32_t>(rx_data[1]) << 8) | 
                      static_cast<uint32_t>(rx_data[2]);
      return value;
    }
  }
}

bool BL0906Factory::spi_write_register_value(uint8_t address, int16_t value) {
  // SPI协议：发送 0x81 + ADDR + DATA_H + DATA_M + DATA_L + CHECKSUM
  uint8_t tx_data[6];
  tx_data[0] = 0x81;
  tx_data[1] = address;
  
  if (is_16bit_register(address)) {
    // 16位寄存器：高字节为0，中字节和低字节存储16位值
    tx_data[2] = 0x00;
    tx_data[3] = (value >> 8) & 0xFF;
    tx_data[4] = value & 0xFF;
  } else {
    // 24位寄存器：将16位值扩展到24位
    tx_data[2] = (value >> 8) & 0xFF;
    tx_data[3] = value & 0xFF;
    tx_data[4] = 0x00;
  }
  
  // 计算校验和
  uint8_t checksum = 0;
  for (int i = 0; i < 5; i++) {
    checksum += tx_data[i];
  }
  tx_data[5] = checksum ^ 0xFF;
  
  // 发送数据
  this->enable();
  this->write_array(tx_data, 6);
  this->disable();
  
  return true;
}
#endif
```

### 9.2 编译时优化
- **代码大小**：只编译使用的通讯协议代码，减少约30-40%的代码量
- **运行时性能**：无虚函数调用，减少函数调用开销
- **内存使用**：不需要动态分配通讯对象，节省内存

## 10. 总结

使用条件编译方式实现双通讯模式支持的主要优势：

1. **代码简洁**：避免复杂的抽象层和虚函数
2. **性能高效**：编译时确定通讯方式，无运行时开销
3. **体积小**：只编译需要的代码，减少固件大小
4. **维护简单**：所有代码集中在两个文件中
5. **配置清晰**：通过YAML明确指定通讯模式

修改完成后，用户只需在配置文件中指定`communication: uart`或`communication: spi`即可选择通讯方式，编译器会自动优化代码。 