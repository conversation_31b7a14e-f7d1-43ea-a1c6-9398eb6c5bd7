#include "adapter_registry.h"
#include "esphome/core/log.h"
#include <memory>

// 根据实际使用的通信方式来决定包含哪些头文件
// 通过检查 Python 配置生成的宏来精确控制

#ifdef USE_SPI_COMMUNICATION_ADAPTER
#include "spi_communication_adapter.h"
#endif

#ifdef USE_UART_COMMUNICATION_ADAPTER
#include "uart_communication_adapter.h"
#endif

// 确保至少定义了一个适配器
#if !defined(USE_SPI_COMMUNICATION_ADAPTER) && !defined(USE_UART_COMMUNICATION_ADAPTER)
#warning "没有定义任何通信适配器，BL0906Factory 将无法工作"
#endif

namespace esphome {
namespace bl0906_factory {

static const char *const TAG = "bl0906_adapter_factory";

std::unique_ptr<CommunicationAdapterInterface> CommunicationAdapterFactory::create_adapter() {
  // 这个方法现在不应该被直接调用
  // 应该使用 create_uart_adapter() 或 create_spi_adapter()
  ESP_LOGE(TAG, "create_adapter() 已弃用，请使用 create_uart_adapter() 或 create_spi_adapter()");
  return nullptr;
}

std::unique_ptr<CommunicationAdapterInterface> CommunicationAdapterFactory::create_uart_adapter() {
#ifdef USE_UART_COMMUNICATION_ADAPTER
  ESP_LOGD(TAG, "创建UART适配器实例");
  return std::unique_ptr<CommunicationAdapterInterface>(new UartCommunicationAdapter());
#else
  ESP_LOGW(TAG, "UART通信适配器未启用，返回空指针");
  return nullptr;
#endif
}

std::unique_ptr<CommunicationAdapterInterface> CommunicationAdapterFactory::create_spi_adapter() {
#ifdef USE_SPI_COMMUNICATION_ADAPTER
  ESP_LOGD(TAG, "创建SPI适配器实例");
  return std::unique_ptr<CommunicationAdapterInterface>(new SpiCommunicationAdapter());
#else
  ESP_LOGW(TAG, "SPI通信适配器未启用，返回空指针");
  return nullptr;
#endif
}

} // namespace bl0906_factory
} // namespace esphome 