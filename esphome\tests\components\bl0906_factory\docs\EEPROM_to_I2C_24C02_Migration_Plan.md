# BL0906 Factory 从模拟EEPROM迁移到多型号I2C EEPROM的详细修改计划

## 项目概述
本文档详细描述了将BL0906 Factory组件从使用ESP32模拟EEPROM改为使用I2C接口的多型号EEPROM芯片（24C02/24C04/24C08/24C16）的完整迁移方案。通过支持不同容量的EEPROM型号，可以根据实际的实例数量和存储需求灵活选择最适合的存储解决方案。

## 当前架构分析

### 1. 现有EEPROM使用分析
- **存储容量**: 当前使用4KB模拟EEPROM (EEPROM_SIZE = 4096)
- **数据结构**: 支持多实例存储，最多3个实例
- **存储布局**:
  - 头部: 32字节 (EEPROMCalibHeader)
  - 每个实例: 156字节 (4字节实例ID + 152字节校准数据)
  - 每个实例支持38个校准寄存器
- **主要文件**:
  - `eeprom_calibration_storage.h/cpp` - 基础读取功能
  - `eeprom_calibration_storage_full.h/cpp` - 扩展写入功能(仅校准模式)
  - `eeprom_calibration_adapter.h/cpp` - 适配器类

### 2. 支持的存储方式对比

| 存储方式 | 容量 | 硬件需求 | 优势 | 劣势 | 适用场景 |
|----------|------|----------|------|------|----------|
| **preference** | ~95KB | 无需外部硬件 | 简单、内置 | 与Flash共享空间 | 原型开发、小数据量 |
| **24C02** | 256字节 | I2C EEPROM芯片 | 独立存储 | 容量较小 | 单实例应用 |
| **24C04** | 512字节 | I2C EEPROM芯片 | 中等容量 | 需要外部硬件 | 2-3实例应用 |
| **24C08** | 1024字节 | I2C EEPROM芯片 | 较大容量 | 需要外部硬件 | 4-6实例应用 |
| **24C16** | 2048字节 | I2C EEPROM芯片 | 大容量 | 需要外部硬件 | 8-12实例应用 |

### 3. I2C EEPROM规格详情
**共同特性**:
- **接口**: I2C (地址0x50-0x57可配置)
- **页大小**: 24C02为8字节，其他为16字节
- **写入时间**: 5ms典型值
- **寿命**: 1,000,000次写入周期
- **工作电压**: 2.5V-5.5V

## 设计原则

### 1. 简洁性优先
**原则**: 代码结构简单清晰，易于维护
**实现**:
- 提供preference和EEPROM两种存储方式选择
- 统一的数据结构设计
- 最小化配置选项

### 2. 存储方式灵活性
**原则**: 根据应用需求选择合适的存储方式
**选择**:
- **preference**: 使用ESP32内置NVS，无需外部硬件
- **eeprom**: 使用I2C外部EEPROM，独立存储

### 2. 数据结构重设计

#### 动态存储布局设计
```
通用头部 (20字节):
- magic: 4字节 (0x24CXCAL, X为型号标识)
- version: 2字节 (版本3)
- eeprom_type: 1字节 (型号标识: 02/04/08/16)
- max_instances: 1字节 (最大实例数)
- instance_count: 1字节 (当前实例数)
- header_crc: 2字节 (头部CRC)
- data_crc: 4字节 (数据区CRC)
- timestamp: 4字节 (最后更新时间戳)
- reserved: 1字节 (保留)

实例索引区 (每个实例4字节):
- instance_id: 4字节 (实例标识符)

校准数据区 (每个条目3字节):
- register_addr: 1字节
- value: 2字节 (int16_t)
```

#### 不同型号的布局分配
| 型号 | 总容量 | 头部 | 索引区 | 数据区 | 每实例条目数 |
|------|--------|------|--------|--------|-------------|
| 24C02 | 256字节 | 20字节 | 4字节 | 232字节 | 77个 |
| 24C04 | 512字节 | 20字节 | 12字节 | 480字节 | 每实例80个 |
| 24C08 | 1024字节 | 20字节 | 24字节 | 980字节 | 每实例82个 |
| 24C16 | 2048字节 | 20字节 | 48字节 | 1980字节 | 每实例82个 |

### 3. 接口统一
**原则**: 提供统一的存储接口
**实现**:
- 统一的校准数据存储接口
- preference方式使用ESPHome内置API
- EEPROM方式使用I2C组件
- 移除复杂的适配器层

## 详细修改计划

### 阶段1: 新建存储接口类

#### 1.1 创建统一存储接口 `calibration_storage_interface.h`
```cpp
#pragma once
#include <vector>
#include <map>

namespace esphome {
namespace bl0906_factory {

// 校准条目结构
struct CalibrationEntry {
    uint8_t register_addr;
    int16_t value;
} __attribute__((packed));

// 统一的存储接口
class CalibrationStorageInterface {
public:
    virtual ~CalibrationStorageInterface() = default;
    
    virtual bool init() = 0;
    virtual bool read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) = 0;
    virtual bool write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) = 0;
    virtual bool delete_instance(uint32_t instance_id) = 0;
    virtual bool verify() = 0;
    virtual bool erase() = 0;
    
    virtual std::vector<uint32_t> get_instance_list() = 0;
    virtual size_t get_max_instances() = 0;
};

}  // namespace bl0906_factory
}  // namespace esphome
```

#### 1.2 创建preference存储实现 `preference_calibration_storage.h`
```cpp
#pragma once
#include "calibration_storage_interface.h"
#include "esphome/components/preferences/preferences.h"

namespace esphome {
namespace bl0906_factory {

class PreferenceCalibrationStorage : public CalibrationStorageInterface {
public:
    PreferenceCalibrationStorage();
    
    bool init() override;
    bool read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) override;
    bool write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) override;
    bool delete_instance(uint32_t instance_id) override;
    bool verify() override;
    bool erase() override;
    
    std::vector<uint32_t> get_instance_list() override;
    size_t get_max_instances() override { return 16; } // NVS支持更多实例

private:
    std::string get_preference_key(uint32_t instance_id);
    bool save_instance_list();
    bool load_instance_list();
    
    std::vector<uint32_t> instance_list_;
};

}  // namespace bl0906_factory
}  // namespace esphome
```

#### 1.3 创建I2C EEPROM存储实现 `i2c_eeprom_calibration_storage.h`
```cpp
#pragma once
#include "calibration_storage_interface.h"
#include "esphome/components/i2c/i2c.h"

namespace esphome {
namespace bl0906_factory {

// 支持的EEPROM型号枚举
enum class EEPROMType : uint8_t {
    TYPE_24C02 = 0x02,  // 256字节
    TYPE_24C04 = 0x04,  // 512字节
    TYPE_24C08 = 0x08,  // 1024字节
    TYPE_24C16 = 0x16   // 2048字节
};

class I2CEEPROMCalibrationStorage : public CalibrationStorageInterface {
public:
    I2CEEPROMCalibrationStorage(i2c::I2CComponent *i2c, EEPROMType type, uint8_t address = 0x50);
    
    bool init() override;
    bool read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) override;
    bool write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) override;
    bool delete_instance(uint32_t instance_id) override;
    bool verify() override;
    bool erase() override;
    
    std::vector<uint32_t> get_instance_list() override;
    size_t get_max_instances() override;

private:
    i2c::I2CComponent *i2c_;
    uint8_t address_;
    EEPROMType eeprom_type_;
    
    // I2C操作
    bool read_bytes(uint16_t addr, uint8_t *data, size_t len);
    bool write_bytes(uint16_t addr, const uint8_t *data, size_t len);
    
    // 数据验证
    uint16_t calculate_crc(const uint8_t* data, size_t len);
};

}  // namespace bl0906_factory
}  // namespace esphome
```

#### 1.4 实现存储类文件
- `preference_calibration_storage.cpp`: 使用ESPHome preferences API
- `i2c_eeprom_calibration_storage.cpp`: 实现I2C EEPROM读写操作

### 阶段2: 主组件集成

#### 2.1 修改 `bl0906_factory.h`
```cpp
class BL0906Factory : public PollingComponent, public uart::UARTDevice, public i2c::I2CDevice {
public:
    // 存储配置接口
    void set_storage_type(const std::string& type) { storage_type_ = type; }
    void set_eeprom_type(EEPROMType type) { eeprom_type_ = type; }
    
protected:
    std::string storage_type_ = "preference";  // "preference" 或 "eeprom"
    EEPROMType eeprom_type_ = EEPROMType::TYPE_24C02;
    std::unique_ptr<CalibrationStorageInterface> calibration_storage_;
    
    // 核心功能
    bool init_calibration_storage();
    bool load_calibration_data();
    bool save_calibration_data();
};
```

#### 2.2 修改 `bl0906_factory.cpp` 的setup()方法
```cpp
void BL0906Factory::setup() {
    // ... 现有代码 ...
    
    if (!init_calibration_storage()) {
        ESP_LOGE(TAG, "校准存储初始化失败");
        this->mark_failed();
        return;
    }
    
    // 加载校准数据
    if (!load_calibration_data()) {
        ESP_LOGW(TAG, "校准数据加载失败，使用默认值");
    }
    
    // ... 继续现有代码 ...
}

bool BL0906Factory::init_calibration_storage() {
    if (storage_type_ == "preference") {
        calibration_storage_ = std::make_unique<PreferenceCalibrationStorage>();
    } else if (storage_type_ == "eeprom") {
        calibration_storage_ = std::make_unique<I2CEEPROMCalibrationStorage>(
            this->parent_, eeprom_type_);
    } else {
        ESP_LOGE(TAG, "未知的存储类型: %s", storage_type_.c_str());
        return false;
    }
    
    return calibration_storage_->init();
}
```

### 阶段3: ESPHome配置集成

#### 3.1 修改 `__init__.py`
```python
# 配置选项
CONF_STORAGE_TYPE = "storage_type"
CONF_EEPROM_TYPE = "eeprom_type"

# 存储类型和EEPROM型号
STORAGE_TYPES = ["preference", "eeprom"]
EEPROM_TYPES = ["24c02", "24c04", "24c08", "24c16"]

# 配置模式
CALIBRATION_SCHEMA = cv.Schema({
    cv.Optional("enabled", default=True): cv.boolean,
    cv.Optional(CONF_STORAGE_TYPE, default="preference"): cv.one_of(*STORAGE_TYPES, lower=True),
    cv.Optional(CONF_EEPROM_TYPE, default="24c02"): cv.one_of(*EEPROM_TYPES, lower=True),
})

# 条件性I2C依赖
def validate_i2c_dependency(config):
    if CONF_CALIBRATION in config:
        calib_config = config[CONF_CALIBRATION]
        if calib_config.get(CONF_STORAGE_TYPE) == "eeprom":
            # 需要I2C设备
            return cv.Schema(CONFIG_SCHEMA.schema).extend(i2c.i2c_device_schema(0x50))(config)
    return config

CONFIG_SCHEMA = cv.Schema({
    cv.GenerateID(): cv.declare_id(BL0906Factory),
    cv.Optional(CONF_UPDATE_INTERVAL, default="60s"): cv.update_interval,
    cv.Optional(CONF_CALIBRATION): CALIBRATION_SCHEMA,
    cv.Optional(CONF_CALIBRATION_MODE, default=False): cv.boolean,
    cv.Optional(CONF_INITIAL_CALIBRATION): cv.ensure_list(INITIAL_CALIBRATION_SCHEMA),
    cv.Required(CONF_INSTANCE_ID): cv.hex_uint32_t,
}).extend(cv.polling_component_schema("60s")).extend(uart.UART_DEVICE_SCHEMA)

FINAL_VALIDATE_SCHEMA = cv.All(
    validate_i2c_dependency,
    uart.final_validate_device_schema("bl0906_factory")
)

async def to_code(config):
    # ... 现有代码 ...
    
    # 处理校准配置
    if CONF_CALIBRATION in config:
        calib_config = config[CONF_CALIBRATION]
        storage_type = calib_config.get(CONF_STORAGE_TYPE, "preference")
        
        cg.add(var.set_storage_type(storage_type))
        
        if storage_type == "eeprom":
            # 注册为I2C设备
            await i2c.register_i2c_device(var, config)
            
            eeprom_type = calib_config.get(CONF_EEPROM_TYPE, "24c02")
            eeprom_enum_map = {
                "24c02": "EEPROMType::TYPE_24C02",
                "24c04": "EEPROMType::TYPE_24C04", 
                "24c08": "EEPROMType::TYPE_24C08",
                "24c16": "EEPROMType::TYPE_24C16"
            }
            cg.add(var.set_eeprom_type(cg.RawExpression(eeprom_enum_map[eeprom_type])))
            
            # 添加I2C EEPROM定义
            cg.add_define("USE_I2C_EEPROM_CALIBRATION")
        else:
            # 添加preference定义
            cg.add_define("USE_PREFERENCE_CALIBRATION")
```

#### 3.2 更新配置文件示例

**使用preference存储 `6-ch-monitor-preference.yaml` (推荐)**:
```yaml
bl0906_factory:
  id: sensor_bl0906
  uart_id: uart_bus
  update_interval: 1s
  instance_id: 0x906B0001
  calibration_mode: true
  calibration:
    enabled: true
    storage_type: preference    # 使用内置preference (默认)
```

**使用I2C EEPROM存储 `6-ch-monitor-eeprom.yaml`**:
```yaml
# I2C总线配置 (仅EEPROM模式需要)
i2c:
  sda: 8
  scl: 9
  scan: true

bl0906_factory:
  id: sensor_bl0906
  uart_id: uart_bus
  i2c_id: bus_a              # 指定I2C总线
  update_interval: 1s
  instance_id: 0x906B0001
  calibration_mode: true
  calibration:
    enabled: true
    storage_type: eeprom       # 使用外部EEPROM
    eeprom_type: 24c02         # 选择EEPROM型号
```

**不同存储方式的选择建议**:
```yaml
# 原型开发/测试 - 使用preference
calibration:
  storage_type: preference

# 生产环境/数据安全 - 使用EEPROM
calibration:
  storage_type: eeprom
  eeprom_type: 24c04         # 根据实例数选择合适型号
```

### 阶段4: 测试和文档

#### 4.1 功能测试
- 测试多型号EEPROM的读写功能
- 验证数据完整性和CRC校验
- 测试页写入逻辑的正确性

#### 4.2 集成测试
- 测试与BL0906 Factory的集成
- 验证不同型号的容量限制
- 测试I2C总线兼容性

#### 4.3 文档更新
- 更新配置文档
- 添加硬件连接说明
- 提供型号选择指南

## 实施时间表

### 第1周: 基础架构
- [ ] 创建I2C EEPROM存储类(支持4种型号)
- [ ] 实现基础读写操作和页写入逻辑
- [ ] 实现CRC校验和数据验证
- [ ] 基础功能测试

### 第2周: 组件集成
- [ ] 修改BL0906Factory类集成I2C EEPROM
- [ ] 实现校准数据加载和保存
- [ ] 简化配置接口
- [ ] 集成测试

### 第3周: 配置和文档
- [ ] 更新ESPHome配置文件
- [ ] 简化配置选项和验证
- [ ] 创建配置示例
- [ ] 功能验证测试

### 第4周: 完善和部署
- [ ] 多型号兼容性测试
- [ ] 性能测试和优化
- [ ] 文档完善
- [ ] 代码审查和部署

## 潜在风险和缓解措施

### 1. 存储容量选择不当
**风险**: 选择的EEPROM型号容量不足或过大造成浪费
**缓解**: 
- 自动容量检测和型号推荐
- 数据压缩和优化算法
- 智能实例数量管理
- 提供容量规划工具

### 2. I2C总线冲突
**风险**: 与其他I2C设备地址冲突
**缓解**:
- 使用标准I2C地址(0x50)
- 提供I2C扫描功能
- 清晰的硬件文档

### 3. 硬件兼容性
**风险**: 现有硬件需要添加I2C连接
**缓解**:
- 提供详细的硬件连接指南
- 支持不同的I2C引脚配置
- 明确的硬件要求说明

## 总结

本迁移计划提供了从ESP32模拟EEPROM到多型号I2C EEPROM的完整解决方案。通过支持24C02、24C04、24C08、24C16四种型号，可以根据不同的应用场景和实例需求选择最合适的存储容量。

## 关键优势：

### 🎯 **灵活的存储选择**
- **preference**: ESP32内置NVS存储，无需外部硬件，适合原型开发
- **24C02 (256字节)**: 适合单实例小型应用，独立存储  
- **24C04 (512字节)**: 适合2-3个实例的基础应用
- **24C08 (1024字节)**: 适合4-6个实例的扩展应用
- **24C16 (2048字节)**: 适合8-12个实例的大容量应用

### 🚀 **简洁性特性**
- 统一的存储接口设计
- 简单的存储方式选择
- 最小化的配置选项
- 清晰的代码结构

### 🛡️ **可靠性保障**
- preference: 使用ESP32成熟的NVS机制
- EEPROM: 独立的外部存储，不受Flash限制
- 更高的写入寿命(EEPROM: 1,000,000次)
- CRC校验确保数据完整性

### 📈 **扩展性设计**
- 支持2种存储方式: preference和EEPROM
- 支持4种常用EEPROM型号
- 统一的存储接口
- 易于添加新存储方式

## 实施建议：

1. **存储方式选择**: 
   - 原型开发: 优先使用preference存储，简单无需额外硬件
   - 生产环境: 考虑使用EEPROM获得独立存储
2. **EEPROM型号选择**: 根据实例数量选择合适型号(24C02/04/08/16)
3. **硬件准备**: EEPROM模式需要正确连接I2C线路(SDA/SCL)
4. **配置验证**: 在测试环境中验证所选存储方式工作正常

建议开发阶段使用preference存储进行快速验证，生产环境根据数据安全性要求选择合适的存储方式。 