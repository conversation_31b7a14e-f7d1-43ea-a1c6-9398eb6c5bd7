# BL0906Factory UART模式测试配置
esphome:
  name: bl0906-factory-uart-test
  platform: ESP32
  board: esp32dev

wifi:
  ssid: "test"
  password: "test"

logger:
  level: DEBUG

api:
ota:

uart:
  id: uart_bus
  tx_pin: GPIO17
  rx_pin: GPIO16
  baud_rate: 9600

bl0906_factory:
  id: bl0906_device
  communication: uart
  uart_id: uart_bus
  instance_id: 0x906B0001
  update_interval: 10s
  calibration:
    enabled: true
    storage_type: preference
  initial_calibration:
    - register: 0xA1
      value: 1000
    - register: 0xA2
      value: 1000

sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    voltage:
      name: "Voltage"
    frequency:
      name: "Frequency"
    temperature:
      name: "Temperature"
    current_1:
      name: "Current 1"
    power_1:
      name: "Power 1"
    energy_1:
      name: "Energy 1"
    total_energy_1:
      name: "Total Energy 1"

number:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    current_1_gain:
      name: "Current 1 Gain"
    current_2_gain:
      name: "Current 2 Gain" 