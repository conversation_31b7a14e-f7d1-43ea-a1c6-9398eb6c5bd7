"""BL0906 Factory - 现代化电能计量组件"""
import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import uart, number, i2c, spi
from esphome import pins
from esphome.const import (
    CONF_ID,
    CONF_UPDATE_INTERVAL
)

# 根据配置动态添加依赖
def _get_dependencies(config):
    deps = []
    if config and CONF_COMMUNICATION in config:
        comm_mode = config[CONF_COMMUNICATION]
        if comm_mode == "uart":
            deps.append("uart")
        elif comm_mode == "spi":
            deps.append("spi")
    return deps

DEPENDENCIES = []  # 基础依赖为空，会在配置验证时动态添加
CODEOWNERS = ["@carrot8848"]
AUTO_LOAD = ["sensor", "number"]
MULTI_CONF = True

# 现代化命名空间定义
bl0906_factory_ns = cg.esphome_ns.namespace("bl0906_factory")
BL0906Factory = bl0906_factory_ns.class_("BL0906Factory", cg.PollingComponent)
BL0906Number = bl0906_factory_ns.class_("BL0906Number", number.Number, cg.Component)

# 通信适配器类声明
CommunicationAdapterInterface = bl0906_factory_ns.class_("CommunicationAdapterInterface")
UartCommunicationAdapter = bl0906_factory_ns.class_("UartCommunicationAdapter", CommunicationAdapterInterface, uart.UARTDevice)
SpiCommunicationAdapter = bl0906_factory_ns.class_("SpiCommunicationAdapter", CommunicationAdapterInterface, spi.SPIDevice)

# 现代化枚举定义
CalibRegType = bl0906_factory_ns.enum("CalibRegType")
SensorType = bl0906_factory_ns.enum("SensorType", is_class=True)
CalibNumberType = bl0906_factory_ns.enum("CalibNumberType", is_class=True)
StatisticsSensorType = bl0906_factory_ns.enum("StatisticsSensorType", is_class=True)
EEPROMType = bl0906_factory_ns.enum("EEPROMType", is_class=True)

# 现代化常量定义
CONF_BL0906_FACTORY_ID = "bl0906_factory_id"
CONF_COMMUNICATION = "communication"
CONF_CALIBRATION = "calibration"
CONF_CALIBRATION_MODE = "calibration_mode"
CONF_INITIAL_CALIBRATION = "initial_calibration"
CONF_REGISTER = "register"
CONF_VALUE = "value"
CONF_STORAGE_TYPE = "storage_type"
CONF_EEPROM_TYPE = "eeprom_type"
CONF_INSTANCE_ID = "instance_id"
CONF_UART_ID = "uart_id"
CONF_SPI_ID = "spi_id"
CONF_CS_PIN = "cs_pin"

# 通讯方式配置
COMMUNICATION_MODES = {
    "uart": "uart",
    "spi": "spi",
}

# 存储类型和EEPROM型号
STORAGE_TYPES = ["preference", "eeprom"]
EEPROM_TYPES = ["24c02", "24c04", "24c08", "24c16"]

# 初始校准值配置
INITIAL_CALIBRATION_SCHEMA = cv.Schema({
    cv.Required(CONF_REGISTER): cv.hex_uint8_t,
    cv.Required(CONF_VALUE): cv.int_range(min=-32768, max=32767),
})

# 简化的校准配置
CALIBRATION_SCHEMA = cv.Schema({
    cv.Optional("enabled", default=True): cv.boolean,
    cv.Optional(CONF_STORAGE_TYPE, default="preference"): cv.one_of(*STORAGE_TYPES, lower=True),
    cv.Optional(CONF_EEPROM_TYPE, default="24c02"): cv.one_of(*EEPROM_TYPES, lower=True),
})

# 条件验证函数
def validate_communication_config(config):
    comm_mode = config[CONF_COMMUNICATION]
    if comm_mode == "uart":
        if CONF_UART_ID not in config:
            raise cv.Invalid("uart_id is required when using UART communication")
        # 移除SPI相关配置
        config.pop(CONF_SPI_ID, None)
        config.pop(CONF_CS_PIN, None)
    elif comm_mode == "spi":
        if CONF_SPI_ID not in config:
            raise cv.Invalid("spi_id is required when using SPI communication")
        if CONF_CS_PIN not in config:
            raise cv.Invalid("cs_pin is required when using SPI communication")
        # 移除UART相关配置
        config.pop(CONF_UART_ID, None)
    return config

# 条件性I2C依赖验证
def validate_i2c_dependency(config):
    """验证I2C依赖并检查EEPROM配置"""
    if CONF_CALIBRATION in config:
        calib_config = config[CONF_CALIBRATION]
        if calib_config.get(CONF_STORAGE_TYPE) == "eeprom":
            # 验证I2C相关配置是否存在
            if "i2c_id" not in config:
                raise cv.Invalid("i2c_id is required when using EEPROM storage")
            if "address" not in config:
                # 使用默认EEPROM地址
                config["address"] = 0x50
    return config

# 基础配置模式（包含I2C可选项）
BASE_CONFIG_SCHEMA = cv.Schema({
    cv.GenerateID(): cv.declare_id(BL0906Factory),
    cv.Required(CONF_COMMUNICATION): cv.enum(COMMUNICATION_MODES, lower=True),
    cv.Optional(CONF_UPDATE_INTERVAL, default="60s"): cv.update_interval,
    cv.Optional(CONF_CALIBRATION): CALIBRATION_SCHEMA,
    cv.Optional(CONF_CALIBRATION_MODE, default=False): cv.boolean,
    cv.Optional(CONF_INITIAL_CALIBRATION): cv.ensure_list(INITIAL_CALIBRATION_SCHEMA),
    cv.Required(CONF_INSTANCE_ID): cv.hex_uint32_t,
    # UART模式配置
    cv.Optional(CONF_UART_ID): cv.use_id(uart.UARTComponent),
    # SPI模式配置
    cv.Optional(CONF_SPI_ID): cv.use_id(spi.SPIComponent),
    cv.Optional(CONF_CS_PIN): pins.gpio_output_pin_schema,
    # I2C模式配置（用于EEPROM存储）
    cv.Optional("i2c_id"): cv.use_id(i2c.I2CBus),
    cv.Optional("address"): cv.i2c_address,
}).extend(cv.polling_component_schema("60s"))

CONFIG_SCHEMA = cv.All(
    BASE_CONFIG_SCHEMA,
    validate_communication_config,
    validate_i2c_dependency
)

FINAL_VALIDATE_SCHEMA = validate_i2c_dependency

async def to_code(config):
    var = cg.new_Pvariable(config[CONF_ID])
    await cg.register_component(var, config)
    
    # 添加现代化组件标识
    cg.add_define("USE_BL0906_FACTORY")
    
    # 总是编译EEPROM存储支持，简化条件编译逻辑
    cg.add_define("USE_I2C_EEPROM_CALIBRATION")
    
    # 首先根据通讯方式添加编译宏（必须在其他代码之前）
    comm_mode = config[CONF_COMMUNICATION]
    if comm_mode == "spi":
        # 添加SPI适配器编译宏
        cg.add_define("USE_SPI_COMMUNICATION_ADAPTER")
    elif comm_mode == "uart":
        # 添加UART适配器编译宏
        cg.add_define("USE_UART_COMMUNICATION_ADAPTER")
    
    # 根据通讯方式创建和配置适配器
    if comm_mode == "spi":
        
        # 获取SPI组件和CS引脚
        spi_component = await cg.get_variable(config[CONF_SPI_ID])
        cs_pin = await cg.gpio_pin_expression(config[CONF_CS_PIN])
        
        # 创建SPI适配器并配置
        cg.add(cg.RawExpression(f"""
        {{
            auto spi_adapter_impl = new esphome::bl0906_factory::SpiCommunicationAdapter();
            spi_adapter_impl->set_spi_parent({spi_component});
            spi_adapter_impl->set_cs_pin({cs_pin});
            auto spi_adapter = std::unique_ptr<esphome::bl0906_factory::CommunicationAdapterInterface>(spi_adapter_impl);
            {var}->set_communication_adapter(std::move(spi_adapter));
        }}
        """))
        
        # SPI适配器会自己处理SPI设备的初始化
        
    elif comm_mode == "uart":
        # 获取UART组件
        uart_component = await cg.get_variable(config[CONF_UART_ID])
        
        # 创建UART适配器并配置
        cg.add(cg.RawExpression(f"""
        {{
            auto uart_adapter_impl = new esphome::bl0906_factory::UartCommunicationAdapter();
            uart_adapter_impl->set_uart_parent({uart_component});
            auto uart_adapter = std::unique_ptr<esphome::bl0906_factory::CommunicationAdapterInterface>(uart_adapter_impl);
            {var}->set_communication_adapter(std::move(uart_adapter));
        }}
        """))
        
        # UART适配器会自己处理UART设备注册

    # 处理校准模式
    if config.get(CONF_CALIBRATION_MODE, False):
        cg.add_define("BL0906_CALIBRATION_MODE")
    
    # 处理存储类型
    if CONF_CALIBRATION in config:
        calib_config = config[CONF_CALIBRATION]
        storage_type = calib_config.get(CONF_STORAGE_TYPE, "preference")
        
        cg.add(var.set_storage_type(storage_type))
        
        if storage_type == "eeprom":
            # 注册为I2C设备
            if "i2c_id" in config and "address" in config:
                # 获取I2C组件并设置
                i2c_component = await cg.get_variable(config["i2c_id"])
                cg.add(var.set_i2c_parent(i2c_component))
                cg.add(var.set_i2c_address(config["address"]))
                
                eeprom_type = calib_config.get(CONF_EEPROM_TYPE, "24c02")
                eeprom_enum_map = {
                    "24c02": "esphome::bl0906_factory::EEPROMType::TYPE_24C02",
                    "24c04": "esphome::bl0906_factory::EEPROMType::TYPE_24C04", 
                    "24c08": "esphome::bl0906_factory::EEPROMType::TYPE_24C08",
                    "24c16": "esphome::bl0906_factory::EEPROMType::TYPE_24C16"
                }
                cg.add(var.set_eeprom_type(cg.RawExpression(eeprom_enum_map[eeprom_type])))
                
                # I2C EEPROM宏已在开头定义
                
                # 添加I2C依赖
                cg.add_library("Wire", None)
            else:
                raise ValueError("EEPROM storage requires i2c_id and address configuration")
        else:
            # preference存储不需要额外的宏定义，使用默认的ESPHome preferences
            pass
    else:
        # 如果没有配置calibration，默认使用preference存储
        cg.add(var.set_storage_type("preference"))
    
    # 处理实例ID（现在是必填项）
    instance_id = config[CONF_INSTANCE_ID]
    cg.add(var.set_instance_id(instance_id))
    
    # 处理初始校准值
    if CONF_INITIAL_CALIBRATION in config:
        # 首先收集所有的初始值到一个字典
        initial_values = {}
        for item in config[CONF_INITIAL_CALIBRATION]:
            register = item[CONF_REGISTER]
            value = item[CONF_VALUE]
            initial_values[register] = value
        
        # 然后通过代码生成设置这些值
        for register, value in initial_values.items():
            cg.add(cg.RawExpression(f"{var}->initial_calibration_values_[0x{register:02X}] = {value}"))

    # 简化的校准配置处理
    if CONF_CALIBRATION in config:
        calib_config = config[CONF_CALIBRATION]
        if calib_config.get("enabled", True):
            cg.add_define("BL0906_CALIBRATION_ENABLED")

# 现代化平台模式定义
PLATFORM_SCHEMA = cv.Schema({
    cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory),
})
