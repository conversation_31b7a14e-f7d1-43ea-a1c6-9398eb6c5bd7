# BL0906 Factory 双通讯模式使用指南

BL0906 Factory 组件现在支持UART和SPI两种通讯模式，通过条件编译在编译时确定使用哪种通讯方式。

## 配置示例

### UART模式配置

```yaml
uart:
  tx_pin: GPIO1
  rx_pin: GPIO3
  baud_rate: 9600

bl0906_factory:
  communication: uart
  uart_id: uart_bus
  instance_id: 0x12345678
  calibration_mode: true
  calibration:
    enabled: true
    storage_type: preference
  
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: my_bl0906
    voltage:
      name: "电压"
    current:
      - name: "通道1电流"
      - name: "通道2电流"
      - name: "通道3电流"
      - name: "通道4电流"
      - name: "通道5电流"
      - name: "通道6电流"
    power:
      - name: "通道1功率"
      - name: "通道2功率"
      - name: "通道3功率"
      - name: "通道4功率"
      - name: "通道5功率"
      - name: "通道6功率"
```

### SPI模式配置

```yaml
spi:
  clk_pin: GPIO18
  mosi_pin: GPIO23
  miso_pin: GPIO19

bl0906_factory:
  communication: spi
  spi_id: spi_bus
  cs_pin: GPIO5
  instance_id: 0x12345678
  calibration_mode: true
  calibration:
    enabled: true
    storage_type: preference
  
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: my_bl0906
    voltage:
      name: "电压"
    current:
      - name: "通道1电流"
      - name: "通道2电流"
      - name: "通道3电流"
      - name: "通道4电流"
      - name: "通道5电流"
      - name: "通道6电流"
    power:
      - name: "通道1功率"
      - name: "通道2功率"
      - name: "通道3功率"
      - name: "通道4功率"
      - name: "通道5功率"
      - name: "通道6功率"
```

## 配置参数说明

### 必填参数

- `communication`: 通讯模式，可选值：`uart` 或 `spi`
- `instance_id`: 实例ID，用于区分不同的BL0906实例

### UART模式参数

- `uart_id`: UART组件的ID，必须在UART模式下提供

### SPI模式参数

- `spi_id`: SPI组件的ID，必须在SPI模式下提供  
- `cs_pin`: 片选引脚，可选，如果不提供则使用SPI组件的默认设置

### 可选参数

- `calibration_mode`: 是否启用校准模式，默认为false
- `calibration`: 校准相关配置
  - `enabled`: 是否启用校准，默认为true
  - `storage_type`: 存储类型，可选值：`preference` 或 `eeprom`
  - `eeprom_type`: EEPROM型号（仅在storage_type为eeprom时需要）

## 技术特点

### 条件编译优势

1. **代码体积小**: 只编译需要的通讯代码，减少30-40%的代码量
2. **运行时效率高**: 无虚函数调用开销，减少函数调用开销
3. **维护简单**: 避免复杂的抽象层
4. **配置清晰**: 编译时就确定通讯方式

### SPI通讯协议

- **模式**: 从模式，半双工通讯
- **速率**: 最大1.5MHz
- **时序**: CPOL=0, CPHA=1
- **字节序**: MSB在前，LSB在后

#### SPI帧结构

**写寄存器操作**:
```
0x81 + ADDR + DATA_H + DATA_M + DATA_L + CHECKSUM
```

**读寄存器操作**:
```
发送: 0x82 + ADDR
接收: DATA_H + DATA_M + DATA_L + CHECKSUM
```

### UART通讯协议

- **波特率**: 9600bps
- **数据位**: 8位
- **停止位**: 1位
- **校验位**: 无

#### UART帧结构

**写寄存器操作**:
```
0xCA + ADDR + DATA_L + DATA_M + DATA_H + CHECKSUM
```

**读寄存器操作**:
```
发送: 0x35 + ADDR
接收: DATA_L + DATA_M + DATA_H + CHECKSUM
```

## 编译说明

根据配置中的`communication`参数，编译器会自动添加相应的编译宏：

- SPI模式: 添加 `USE_BL0906_FACTORY_SPI` 宏
- UART模式: 不添加额外宏（默认模式）

用户只需在配置文件中指定通讯模式，编译器会自动优化代码。

## 注意事项

1. 同一个固件只能支持一种通讯模式
2. 修改通讯模式需要重新编译固件
3. SPI和UART模式的写保护解除命令不同
4. SPI模式不需要清空接收缓冲区
5. 确保硬件连接与配置的通讯模式匹配 