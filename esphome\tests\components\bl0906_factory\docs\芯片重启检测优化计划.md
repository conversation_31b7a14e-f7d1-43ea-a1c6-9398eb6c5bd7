# BL0906 芯片重启检测优化计划

## 问题分析

### 当前问题
当前的芯片重启检测逻辑存在一个问题：一旦检测到芯片重启，由于芯片刚重启后CF_count值仍然很小（小于5），会在后续的每个循环中重复触发芯片重启检测，导致不必要的重复处理。

### 期望行为
- 检测到芯片重启后，应该暂停重启检测
- 直到芯片重启检测条件不满足（即不是所有通道CF_count都小于5）时，才重新启用重启检测
- 避免重复检测和处理同一次重启

## 修改方案

### 1. 添加状态标记

在 `BL0906Factory` 类中添加新的成员变量：

```cpp
private:
  bool chip_restart_detected_ = false;  // 标记是否已检测到芯片重启
  uint32_t last_restart_detection_time_ = 0;  // 上次检测到重启的时间（可选，用于调试）
```

### 2. 修改 `detect_chip_restart` 方法逻辑

#### 2.1 检测条件判断
在当前检测逻辑之前，先检查是否已经检测到重启：

```cpp
void BL0906Factory::detect_chip_restart(const RawSensorData& data) {
  if (!energy_persistence_enabled_) {
    ESP_LOGV(FACTORY_TAG, "电量持久化存储已禁用，跳过芯片重启检测");
    return;
  }

  ESP_LOGV(FACTORY_TAG, "开始检测BL0906芯片重启...");

  bool all_channels_low = true;  // 标记所有通道是否都小于5

  // 检查各通道的CF_count
  for (int i = 0; i < CHANNEL_COUNT; i++) {
    uint32_t hardware_cf_count = static_cast<uint32_t>(data.channels[i].energy_raw);
    
    ESP_LOGV(FACTORY_TAG, "通道%d硬件CF_count: %u", i+1, hardware_cf_count);

    // 检查是否小于5
    if (hardware_cf_count >= 5) {
      all_channels_low = false;
      ESP_LOGV(FACTORY_TAG, "通道%d CF_count=%u >= 5，芯片未重启", i+1, hardware_cf_count);
    }
  }

  // 如果之前已检测到重启，检查是否可以重新启用检测
  if (chip_restart_detected_) {
    if (!all_channels_low) {
      // 条件不满足了，重新启用检测
      chip_restart_detected_ = false;
      ESP_LOGI(FACTORY_TAG, "芯片重启检测条件不再满足，重新启用重启检测");
    } else {
      // 仍然满足重启条件，跳过检测
      ESP_LOGV(FACTORY_TAG, "芯片重启已检测，跳过重复检测");
      return;
    }
  }

  // 只有所有通道的CF_count都小于5时才确定芯片重启
  if (all_channels_low && !chip_restart_detected_) {
    ESP_LOGW(FACTORY_TAG, "检测到BL0906芯片重启：所有通道CF_count都小于5");
    
    // 设置重启检测标记
    chip_restart_detected_ = true;
    last_restart_detection_time_ = millis();
    
    // 重新记录各通道的CF_count值
    for (int i = 0; i < CHANNEL_COUNT; i++) {
      uint32_t hardware_cf_count = static_cast<uint32_t>(data.channels[i].energy_raw);
      ESP_LOGI(FACTORY_TAG, "通道%d重启时CF_count: %u", i+1, hardware_cf_count);
      last_cf_count_[i] = hardware_cf_count;
    }
    
    // 标记需要保存last_cf_count
    last_cf_count_needs_save_ = true;
    
    ESP_LOGI(FACTORY_TAG, "芯片重启检测完成，已更新基准CF_count值，暂停重启检测");
  } else {
    ESP_LOGV(FACTORY_TAG, "芯片未重启，继续正常运行");
  }
}
```

#### 2.2 详细逻辑流程

1. **初始状态**：`chip_restart_detected_ = false`，正常进行重启检测
2. **检测到重启**：
   - 设置 `chip_restart_detected_ = true`
   - 执行重启处理逻辑
   - 记录检测时间（可选）
3. **后续循环**：
   - 如果 `chip_restart_detected_ = true`，先检查当前状态
   - 如果仍然满足重启条件（所有通道CF_count < 5），跳过检测
   - 如果不再满足重启条件（有通道CF_count >= 5），重新启用检测
4. **重新启用检测**：
   - 设置 `chip_restart_detected_ = false`
   - 恢复正常的重启检测流程

### 3. 添加调试信息

#### 3.1 在 `diagnose_energy_persistence` 方法中添加重启检测状态信息

```cpp
void BL0906Factory::diagnose_energy_persistence() {
  ESP_LOGI(FACTORY_TAG, "=== 电量持久化诊断信息（基于CF_count）===");
  ESP_LOGI(FACTORY_TAG, "持久化状态: %s", energy_persistence_enabled_ ? "启用" : "禁用");
  ESP_LOGI(FACTORY_TAG, "统计功能状态: %s", energy_statistics_enabled_ ? "启用" : "禁用");
  ESP_LOGI(FACTORY_TAG, "芯片重启检测状态: %s", chip_restart_detected_ ? "已检测到重启" : "正常运行");
  if (chip_restart_detected_) {
    ESP_LOGI(FACTORY_TAG, "上次重启检测时间: %u ms", last_restart_detection_time_);
    ESP_LOGI(FACTORY_TAG, "距离上次重启检测: %u ms", millis() - last_restart_detection_time_);
  }
  // ... 其他现有的诊断信息
}
```

#### 3.2 在头文件中添加对应的声明

在 `bl0906_factory.h` 的 private 部分添加：

```cpp
private:
  bool chip_restart_detected_ = false;  // 标记是否已检测到芯片重启
  uint32_t last_restart_detection_time_ = 0;  // 上次检测到重启的时间
```

### 4. 可选的改进

#### 4.1 添加超时机制
为防止某些异常情况下重启检测永远不恢复，可以添加超时机制：

```cpp
static const uint32_t RESTART_DETECTION_TIMEOUT = 300000;  // 5分钟超时

// 在 detect_chip_restart 方法中添加超时检查
if (chip_restart_detected_ && 
    (millis() - last_restart_detection_time_) > RESTART_DETECTION_TIMEOUT) {
  ESP_LOGW(FACTORY_TAG, "芯片重启检测超时，强制重新启用检测");
  chip_restart_detected_ = false;
}
```

#### 4.2 统计重启次数
可以添加重启次数统计，用于系统监控：

```cpp
private:
  uint32_t chip_restart_count_ = 0;  // 芯片重启次数计数

// 在检测到重启时递增计数器
chip_restart_count_++;
ESP_LOGI(FACTORY_TAG, "芯片重启次数: %u", chip_restart_count_);
```

## 修改文件列表

1. **bl0906_factory.h** - 添加新的成员变量声明
2. **bl0906_factory.cpp** - 修改 `detect_chip_restart` 方法逻辑
3. **bl0906_factory.cpp** - 更新 `diagnose_energy_persistence` 方法

## 预期效果

1. **避免重复检测**：检测到芯片重启后，不会在后续循环中重复触发相同的重启处理
2. **自动恢复检测**：当芯片正常运行一段时间后（CF_count增长），自动恢复重启检测功能
3. **更好的调试信息**：提供重启检测状态和时间信息，便于问题诊断
4. **系统稳定性**：减少不必要的日志输出和处理，提高系统整体稳定性

## 测试验证

### 测试场景
1. **正常启动**：验证初始状态下重启检测正常工作
2. **芯片重启**：模拟芯片重启，验证重启检测和暂停逻辑
3. **恢复检测**：验证CF_count增长后重启检测自动恢复
4. **多次重启**：验证多次重启的处理逻辑

### 验证指标
1. 重启检测只在实际重启时触发一次
2. 重启检测能在适当时机自动恢复
3. 日志输出清晰，便于调试
4. 系统性能无明显影响 