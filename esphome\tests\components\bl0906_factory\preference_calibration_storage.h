#pragma once
#include "calibration_storage_interface.h"
#include "esphome/core/preferences.h"
#include "esphome/core/log.h"

namespace esphome {
namespace bl0906_factory {

class PreferenceCalibrationStorage : public CalibrationStorageInterface {
public:
    PreferenceCalibrationStorage();
    
    bool init() override;
    bool read_instance(uint32_t instance_id, std::vector<CalibrationEntry>& entries) override;
    bool write_instance(uint32_t instance_id, const std::vector<CalibrationEntry>& entries) override;
    bool delete_instance(uint32_t instance_id) override;
    bool verify() override;
    bool erase() override;
    
    std::vector<uint32_t> get_instance_list() override;
    size_t get_max_instances() override { return 16; } // NVS支持更多实例

private:
    std::string get_preference_key(uint32_t instance_id);
    bool save_instance_list();
    bool load_instance_list();
    
    std::vector<uint32_t> instance_list_;
    static const char *const TAG;
};

}  // namespace bl0906_factory
}  // namespace esphome 