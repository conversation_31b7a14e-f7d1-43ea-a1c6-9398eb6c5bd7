# BL0906 Factory 嵌套配置结构实现总结

## 实现概述

BL0906 Factory组件采用嵌套的通道配置结构，将每个通道的所有传感器组织在一个嵌套的配置块中。这种配置方式使得传感器配置更加清晰、直观和易于管理。

## 核心设计理念

### 配置结构设计

#### 嵌套式配置
```yaml
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    
    # 全局传感器
    voltage:
      name: "电压"
    
    # 通道1传感器组（嵌套配置）
    ch1:
      current:
        name: "通道1电流"
      power:
        name: "通道1功率"
      today_energy:
        name: "通道1今日电量"
    
    # 通道2传感器组（嵌套配置）
    ch2:
      current:
        name: "通道2电流"
      power:
        name: "通道2功率"
      today_energy:
        name: "通道2今日电量"
```

### 技术实现架构

#### 配置模式设计
```python
# 全局传感器配置（包含基础传感器和总电量统计传感器）
GLOBAL_SENSOR_CONFIGS = {
    # 基础传感器
    CONF_FREQUENCY: {...},
    CONF_TEMPERATURE: {...},
    CONF_VOLTAGE: {...},
    "power_sum": {...},
    "energy_sum": {...},
    "total_energy_sum": {...},
    
    # 总电量统计传感器
    "yesterday_total_energy": {...},
    "today_total_energy": {...},
    "week_total_energy": {...},
    "month_total_energy": {...},
    "year_total_energy": {...},
}

# 通道传感器配置模板
CHANNEL_SENSOR_TEMPLATES = {
    # 基础测量传感器
    CONF_CURRENT: {...},
    CONF_POWER: {...},
    CONF_ENERGY: {...},
    "total_energy": {...},
    
    # 电量统计传感器
    "yesterday_energy": {...},
    "today_energy": {...},
    "week_energy": {...},
    "month_energy": {...},
    "year_energy": {...},
}
```

#### 动态配置模式构建
```python
def build_channel_sensor_schema():
    """构建单个通道的传感器配置模式"""
    schema_dict = {}
    for sensor_key, config in CHANNEL_SENSOR_TEMPLATES.items():
        schema_dict[cv.Optional(sensor_key)] = sensor.sensor_schema(
            unit_of_measurement=config["unit"],
            accuracy_decimals=config["accuracy"],
            device_class=config["device_class"],
            state_class=config["state_class"],
        )
    return cv.Schema(schema_dict)

def build_config_schema():
    """构建嵌套配置模式"""
    schema_dict = {cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory)}

    # 添加全局传感器配置
    for key, config in GLOBAL_SENSOR_CONFIGS.items():
        schema_dict[cv.Optional(key)] = sensor.sensor_schema(...)

    # 添加通道配置 (ch1-ch6)
    channel_schema = build_channel_sensor_schema()
    for i in range(1, CHANNEL_COUNT + 1):
        schema_dict[cv.Optional(f"ch{i}")] = channel_schema

    return cv.Schema(schema_dict)
```

#### 分层传感器注册
```python
async def to_code(config):
    var = await cg.get_variable(config[CONF_BL0906_FACTORY_ID])

    # 1. 注册全局传感器
    for sensor_key, sensor_config in GLOBAL_SENSOR_CONFIGS.items():
        if sensor_key in config:
            sens = await sensor.new_sensor(config[sensor_key])
            sensor_type = sensor_config["type"]
            
            # 判断是否为电量统计传感器
            if sensor_type in STATISTICS_SENSOR_TYPES:
                statistics_sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::StatisticsSensorType::{sensor_type}")
                cg.add(var.set_statistics_sensor(statistics_sensor_type_enum, sens, 0))
            else:
                sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::BL0906Factory::SensorType::{sensor_type}")
                cg.add(var.set_sensor(sensor_type_enum, sens, 0))

    # 2. 注册通道传感器
    for i in range(1, CHANNEL_COUNT + 1):
        channel_key = f"ch{i}"
        if channel_key in config:
            channel_config = config[channel_key]
            channel_index = i - 1  # 内部使用0-5索引
            
            for sensor_key, sensor_config in CHANNEL_SENSOR_TEMPLATES.items():
                if sensor_key in channel_config:
                    sens = await sensor.new_sensor(channel_config[sensor_key])
                    sensor_type = sensor_config["type"]
                    
                    # 判断传感器类型并使用相应的注册接口
                    if sensor_type in STATISTICS_SENSOR_TYPES:
                        statistics_sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::StatisticsSensorType::{sensor_type}")
                        cg.add(var.set_statistics_sensor(statistics_sensor_type_enum, sens, channel_index))
                    else:
                        sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::BL0906Factory::SensorType::{sensor_type}")
                        cg.add(var.set_sensor(sensor_type_enum, sens, channel_index))
```

## 实现特点

### 1. 层次化配置结构
```
配置层次:
├── 全局传感器
│   ├── 基础传感器: voltage, frequency, temperature
│   ├── 总和传感器: power_sum, energy_sum, total_energy_sum
│   └── 总电量统计: yesterday_total_energy, today_total_energy, week_total_energy, month_total_energy, year_total_energy
└── 通道传感器 (ch1-ch6)
    ├── 基础测量: current, power, energy, total_energy
    └── 统计分析: yesterday_energy, today_energy, week_energy, month_energy, year_energy
```

### 2. 简洁的设计
- **配置统一**: 全局传感器和通道传感器统一管理
- **模板复用**: 通道传感器使用统一模板，减少重复代码
- **动态生成**: 自动生成6个通道的配置模式

### 3. 类型安全
- **枚举映射**: 使用枚举确保传感器类型的正确性
- **编译时检查**: 配置验证在编译时进行
- **智能注册**: 自动识别传感器类型并使用相应的注册接口

### 4. 纯嵌套结构
- **单一配置方式**: 只支持嵌套配置结构
- **简化代码**: 移除向后兼容代码，代码更简洁
- **清晰逻辑**: 配置逻辑更加清晰和直观

## 支持的传感器数量

### 通道传感器 (54个)
- 6个通道 × 9种传感器类型 = 54个传感器
- 每个通道支持：
  - 4个基础传感器：current, power, energy, total_energy
  - 5个统计传感器：yesterday_energy, today_energy, week_energy, month_energy, year_energy

### 全局传感器 (11个)
- 6个基础传感器：voltage, frequency, temperature, power_sum, energy_sum, total_energy_sum
- 5个总电量统计传感器：yesterday_total_energy, today_total_energy, week_total_energy, month_total_energy, year_total_energy

### 总计支持 65个传感器
- 54个通道传感器 + 11个全局传感器 = 65个传感器

## 配置优势

### 1. 清晰的组织结构
```yaml
# 直观的通道分组
ch1:  # 通道1的所有传感器
  current: {...}
  power: {...}
  today_energy: {...}

ch2:  # 通道2的所有传感器
  current: {...}
  power: {...}
  today_energy: {...}
```

### 2. 简洁的配置
- 通道内传感器配置简洁明了
- 配置结构紧凑
- 易于理解和维护

### 3. 灵活的选择性配置
```yaml
# 可以为不同通道配置不同的传感器组合
ch1:  # 完整监控
  current: {...}
  power: {...}
  energy: {...}
  today_energy: {...}
  month_energy: {...}

ch2:  # 基础监控
  power: {...}
  today_energy: {...}

ch3:  # 最小监控
  today_energy: {...}
```

### 4. 优秀的可维护性
- 配置结构清晰，便于理解
- 通道独立配置，便于管理
- 减少配置错误的可能性

## 测试验证

### 测试文件
1. `test_energy_statistics_sensors.yaml` - 完整功能测试
2. `test_statistics_simple.yaml` - 简化配置测试
3. `example_nested_config.yaml` - 嵌套配置示例
4. `test_nested_config.py` - 配置验证脚本

### 验证内容
- 嵌套配置模式正确性验证
- 传感器注册机制验证
- 不同配置组合的兼容性验证
- 全局传感器和通道传感器的协同工作验证

## 文档支持

### 用户文档
1. `NESTED_CONFIG_GUIDE.md` - 嵌套配置使用指南
2. `ENERGY_STATISTICS_SENSORS_USAGE.md` - 电量统计传感器使用指南
3. `ENERGY_STATISTICS_SENSORS_IMPLEMENTATION.md` - 实现总结文档

### 技术文档
1. `NESTED_CONFIG_IMPLEMENTATION.md` - 本文档，技术实现总结
2. 各种测试配置文件和示例

## 总结

嵌套配置结构成功实现了：

1. **直观的配置方式**: 按通道组织，每个通道包含完整的传感器组
2. **灵活的配置选项**: 支持选择性配置，按需启用传感器
3. **优秀的可维护性**: 配置结构清晰，便于理解和管理
4. **简洁的代码实现**: 纯嵌套结构，代码简洁高效
5. **强大的扩展能力**: 支持65个传感器，满足复杂监控需求

这种配置方式特别适合需要监控多个通道的复杂应用场景，能够显著提升配置的可读性和维护性，是一种现代化的传感器配置解决方案。 