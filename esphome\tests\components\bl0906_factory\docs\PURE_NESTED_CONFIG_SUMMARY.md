# BL0906 Factory 纯嵌套配置结构总结

## 概述

BL0906 Factory组件现在采用纯嵌套配置结构，完全移除了向后兼容代码，只支持清晰直观的嵌套通道配置方式。这种设计使得代码更加简洁，配置更加直观。

## 核心特点

### 1. 纯嵌套结构
- **单一配置方式**: 只支持嵌套配置，不再支持平铺式配置
- **代码简洁**: 移除了所有向后兼容代码
- **逻辑清晰**: 配置逻辑更加直观和易于理解

### 2. 层次化组织
```yaml
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    
    # 全局传感器层
    voltage: {...}
    frequency: {...}
    power_sum: {...}
    today_total_energy: {...}
    
    # 通道传感器层
    ch1:
      current: {...}
      power: {...}
      today_energy: {...}
    
    ch2:
      current: {...}
      power: {...}
      today_energy: {...}
```

### 3. 统一的配置管理
- **全局传感器**: 包含基础传感器、总和传感器、总电量统计传感器
- **通道传感器**: 每个通道包含基础测量和电量统计传感器
- **智能注册**: 自动识别传感器类型并使用相应的注册接口

## 技术实现

### 配置结构
```python
# 全局传感器配置（11种）
GLOBAL_SENSOR_CONFIGS = {
    # 基础传感器 (3种)
    CONF_FREQUENCY: {...},
    CONF_TEMPERATURE: {...},
    CONF_VOLTAGE: {...},
    
    # 总和传感器 (3种)
    "power_sum": {...},
    "energy_sum": {...},
    "total_energy_sum": {...},
    
    # 总电量统计传感器 (5种)
    "yesterday_total_energy": {...},
    "today_total_energy": {...},
    "week_total_energy": {...},
    "month_total_energy": {...},
    "year_total_energy": {...},
}

# 通道传感器配置模板（9种）
CHANNEL_SENSOR_TEMPLATES = {
    # 基础测量传感器 (4种)
    CONF_CURRENT: {...},
    CONF_POWER: {...},
    CONF_ENERGY: {...},
    "total_energy": {...},
    
    # 电量统计传感器 (5种)
    "yesterday_energy": {...},
    "today_energy": {...},
    "week_energy": {...},
    "month_energy": {...},
    "year_energy": {...},
}
```

### 动态配置生成
```python
def build_config_schema():
    """构建嵌套配置模式"""
    schema_dict = {cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory)}

    # 添加全局传感器配置
    for key, config in GLOBAL_SENSOR_CONFIGS.items():
        schema_dict[cv.Optional(key)] = sensor.sensor_schema(...)

    # 添加通道配置 (ch1-ch6)
    channel_schema = build_channel_sensor_schema()
    for i in range(1, CHANNEL_COUNT + 1):
        schema_dict[cv.Optional(f"ch{i}")] = channel_schema

    return cv.Schema(schema_dict)
```

### 智能传感器注册
```python
async def to_code(config):
    var = await cg.get_variable(config[CONF_BL0906_FACTORY_ID])

    # 注册全局传感器
    for sensor_key, sensor_config in GLOBAL_SENSOR_CONFIGS.items():
        if sensor_key in config:
            sens = await sensor.new_sensor(config[sensor_key])
            sensor_type = sensor_config["type"]
            
            # 智能判断传感器类型
            if sensor_type in STATISTICS_SENSOR_TYPES:
                # 统计传感器
                statistics_sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::StatisticsSensorType::{sensor_type}")
                cg.add(var.set_statistics_sensor(statistics_sensor_type_enum, sens, 0))
            else:
                # 普通传感器
                sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::BL0906Factory::SensorType::{sensor_type}")
                cg.add(var.set_sensor(sensor_type_enum, sens, 0))

    # 注册通道传感器
    for i in range(1, CHANNEL_COUNT + 1):
        channel_key = f"ch{i}"
        if channel_key in config:
            channel_config = config[channel_key]
            channel_index = i - 1
            
            for sensor_key, sensor_config in CHANNEL_SENSOR_TEMPLATES.items():
                if sensor_key in channel_config:
                    sens = await sensor.new_sensor(channel_config[sensor_key])
                    sensor_type = sensor_config["type"]
                    
                    # 智能判断传感器类型
                    if sensor_type in STATISTICS_SENSOR_TYPES:
                        statistics_sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::StatisticsSensorType::{sensor_type}")
                        cg.add(var.set_statistics_sensor(statistics_sensor_type_enum, sens, channel_index))
                    else:
                        sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::BL0906Factory::SensorType::{sensor_type}")
                        cg.add(var.set_sensor(sensor_type_enum, sens, channel_index))
```

## 支持的传感器

### 传感器数量统计
- **通道传感器**: 6通道 × 9种类型 = 54个传感器
- **全局传感器**: 11种传感器
- **总计**: 65个传感器

### 传感器分类
```
全局传感器 (11个):
├── 基础传感器 (3个)
│   ├── voltage (电压)
│   ├── frequency (频率)
│   └── temperature (温度)
├── 总和传感器 (3个)
│   ├── power_sum (总功率)
│   ├── energy_sum (总实时电量)
│   └── total_energy_sum (总累计电量)
└── 总电量统计 (5个)
    ├── yesterday_total_energy (昨日总电量)
    ├── today_total_energy (今日总电量)
    ├── week_total_energy (本周总电量)
    ├── month_total_energy (本月总电量)
    └── year_total_energy (本年总电量)

通道传感器 (每通道9个):
├── 基础测量 (4个)
│   ├── current (电流)
│   ├── power (功率)
│   ├── energy (实时电量)
│   └── total_energy (累计电量)
└── 电量统计 (5个)
    ├── yesterday_energy (昨日电量)
    ├── today_energy (今日电量)
    ├── week_energy (本周电量)
    ├── month_energy (本月电量)
    └── year_energy (本年电量)
```

## 配置优势

### 1. 清晰直观
```yaml
# 每个通道的传感器集中配置
ch1:
  current: {...}      # 电流
  power: {...}        # 功率
  energy: {...}       # 实时电量
  today_energy: {...} # 今日电量

ch2:
  current: {...}
  power: {...}
  today_energy: {...}
```

### 2. 灵活配置
```yaml
# 可以为不同通道配置不同的传感器组合

# 完整监控通道
ch1:
  current: {...}
  power: {...}
  energy: {...}
  total_energy: {...}
  yesterday_energy: {...}
  today_energy: {...}
  week_energy: {...}
  month_energy: {...}
  year_energy: {...}

# 基础监控通道
ch2:
  current: {...}
  power: {...}
  today_energy: {...}

# 最小监控通道
ch3:
  today_energy: {...}
```

### 3. 易于维护
- **结构清晰**: 通道和传感器的关系一目了然
- **配置集中**: 每个通道的配置集中在一起
- **减少错误**: 嵌套结构减少配置错误的可能性

### 4. 代码简洁
- **单一路径**: 只有一种配置方式，代码逻辑简单
- **无冗余**: 移除了所有向后兼容代码
- **高效率**: 配置解析和传感器注册更加高效

## 使用示例

### 基本配置
```yaml
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    
    # 全局传感器
    voltage:
      name: "电压"
    
    # 通道传感器
    ch1:
      current:
        name: "通道1电流"
      power:
        name: "通道1功率"
      today_energy:
        name: "通道1今日电量"
    
    ch2:
      current:
        name: "通道2电流"
      power:
        name: "通道2功率"
      today_energy:
        name: "通道2今日电量"
    
    # 总电量统计
    today_total_energy:
      name: "今日总电量"
```

### 完整配置
```yaml
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    
    # 全局传感器
    voltage:
      name: "电压"
    frequency:
      name: "频率"
    temperature:
      name: "温度"
    power_sum:
      name: "总功率"
    energy_sum:
      name: "总实时电量"
    total_energy_sum:
      name: "总累计电量"
    yesterday_total_energy:
      name: "昨日总电量"
    today_total_energy:
      name: "今日总电量"
    week_total_energy:
      name: "本周总电量"
    month_total_energy:
      name: "本月总电量"
    year_total_energy:
      name: "本年总电量"
    
    # 通道1完整配置
    ch1:
      current:
        name: "通道1电流"
      power:
        name: "通道1功率"
      energy:
        name: "通道1实时电量"
      total_energy:
        name: "通道1累计电量"
      yesterday_energy:
        name: "通道1昨日电量"
      today_energy:
        name: "通道1今日电量"
      week_energy:
        name: "通道1本周电量"
      month_energy:
        name: "通道1本月电量"
      year_energy:
        name: "通道1本年电量"
    
    # 其他通道配置...
```

## 测试验证

### 测试文件
1. `test_nested_config.py` - 纯嵌套配置验证脚本
2. `test_energy_statistics_sensors.yaml` - 完整功能测试
3. `test_statistics_simple.yaml` - 简化配置测试
4. `example_nested_config.yaml` - 完整示例配置

### 测试内容
- 嵌套配置模式验证
- 全局传感器和通道传感器注册验证
- 灵活配置组合验证
- 传感器类型智能识别验证

## 总结

纯嵌套配置结构的优势：

1. **简洁高效**: 移除向后兼容代码，逻辑更清晰
2. **直观易用**: 嵌套结构直观反映通道和传感器的关系
3. **灵活强大**: 支持65个传感器，满足复杂监控需求
4. **易于维护**: 配置结构清晰，便于理解和管理
5. **现代化设计**: 采用现代化的配置理念，提升用户体验

这种纯嵌套配置结构是一种现代化、高效的传感器配置解决方案，特别适合需要监控多个通道的复杂应用场景。 