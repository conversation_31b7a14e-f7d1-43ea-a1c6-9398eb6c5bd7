# EnergyStatisticsManager 迁移到 PollingComponent

## 修改概述

将 `EnergyStatisticsManager` 类从手动调用 `update_sensors_on_save()` 的方式改为继承 `PollingComponent` 并使用 `update_interval` 机制。

## 主要修改

### 1. 头文件修改 (`energy_statistics_manager.h`)

- 添加 `#include "esphome/components/polling_component.h"`
- 将 `EnergyStatisticsManager` 类改为继承 `PollingComponent`
- 将 `setup()` 方法标记为 `override`
- 添加 `update()` 方法替代原来的 `update_sensors_on_save()`
- 添加 `set_update_interval()` 方法

### 2. 实现文件修改 (`energy_statistics_manager.cpp`)

- 修改构造函数调用 `PollingComponent(60000)` (默认60秒更新间隔)
- 将 `update_sensors_on_save()` 方法重命名为 `update()`
- 在 `update()` 方法中添加 `check_period_changes()` 调用

### 3. BL0906Factory 集成修改

#### 头文件 (`bl0906_factory.h`)
- 添加 `set_statistics_update_interval()` 方法声明

#### 实现文件 (`bl0906_factory.cpp`)
- 在初始化时调用 `this->register_component(energy_stats_manager_.get())` 将统计管理器注册为子组件
- 实现 `set_statistics_update_interval()` 方法

## 使用方式

### 设置更新间隔

```cpp
// 在YAML配置中或代码中设置更新间隔
bl0906_factory.set_statistics_update_interval(30000);  // 30秒更新一次
```

### 默认行为

- 默认更新间隔：60秒
- 自动调用传感器更新和周期变化检查
- 作为子组件自动管理生命周期

## 优势

1. **标准化**：使用ESPHome标准的PollingComponent机制
2. **自动化**：无需手动调用更新方法，由框架自动管理
3. **可配置**：可以灵活设置更新间隔
4. **解耦**：减少了与BL0906Factory的耦合度

## 兼容性

- 保持了所有原有的功能接口
- 统计数据计算逻辑保持不变
- 传感器设置和管理方式保持不变

## 测试

创建了 `test_polling_component.cpp` 文件用于验证功能正确性。 