# BL0906Factory 移除旧函数总结

## 概述

根据重构完成总结文档的要求，已成功移除所有为了向后兼容而保留的旧函数，现在代码完全使用新的统一架构。

## 已移除的旧函数

### 1. 数据读取相关函数

#### `read_basic_sensors()`
- **位置**: `bl0906_factory.h` 和 `bl0906_factory.cpp`
- **功能**: 旧的基础传感器读取方法（温度、频率、电压）
- **替代方案**: 新的状态机在 `State::READ_BASIC_SENSORS` 状态中直接调用统一读取函数

#### `read_channel_data(int channel)`
- **位置**: `bl0906_factory.h` 和 `bl0906_factory.cpp`
- **功能**: 旧的通道数据读取方法（电流、功率、电量）
- **替代方案**: 新的状态机在 `State::READ_CHANNEL_X` 状态中直接调用统一读取函数

#### `read_total_data()`
- **位置**: `bl0906_factory.h` 和 `bl0906_factory.cpp`
- **功能**: 旧的总和数据读取方法（总功率、总电量）
- **替代方案**: 新的状态机在 `State::READ_TOTAL_DATA` 状态中直接调用统一读取函数

#### `read_data_(uint8_t address, float reference, sensor::Sensor *sensor)`
- **位置**: `bl0906_factory.h` 和 `bl0906_factory.cpp`
- **功能**: 旧的通用数据读取方法，包含持久化逻辑
- **替代方案**: 
  - 数据读取: `read_raw_register_data(uint8_t address)`
  - 数据转换: `convert_raw_to_value(uint8_t address, int32_t raw_value)`
  - 持久化处理: `process_energy_persistence(const RawSensorData& data)`

### 2. 芯片重启检测相关函数

#### `check_chip_restart()`
- **位置**: `bl0906_factory.h` 和 `bl0906_factory.cpp`
- **功能**: 旧的芯片重启检测方法（实时读取版本）
- **替代方案**: `detect_chip_restart(const RawSensorData& data)` - 基于完整数据集的检测

### 3. 辅助函数

#### `read_raw_data_to_cache(uint8_t address, int32_t &raw_value)`
- **位置**: `bl0906_factory.h` 和 `bl0906_factory.cpp`
- **功能**: 旧的数据读取到缓存方法
- **替代方案**: 直接使用 `read_raw_register_data(uint8_t address)`

### 4. 向后兼容常量

#### 命名空间级别的常量定义
- **位置**: `bl0906_factory.h`
- **移除内容**:
  ```cpp
  constexpr auto CHGN = CalibRegType::CHGN;
  constexpr auto CHOS = CalibRegType::CHOS;
  constexpr auto RMSGN = CalibRegType::RMSGN;
  constexpr auto RMSOS = CalibRegType::RMSOS;
  constexpr auto WATTGN = CalibRegType::WATTGN;
  constexpr auto WATTOS = CalibRegType::WATTOS;
  constexpr auto CHGN_V = CalibRegType::CHGN_V;
  constexpr auto CHOS_V = CalibRegType::CHOS_V;
  ```
- **替代方案**: 直接使用 `CalibRegType::CHGN` 等枚举值

## 新架构的优势

### 1. 统一的数据处理流水线
- **数据读取**: `read_raw_register_data()` - 唯一的数据读取入口
- **数据转换**: `convert_raw_to_value()` - 唯一的数据转换入口
- **数据处理**: 状态机驱动的流水线处理

### 2. 清晰的状态机架构
```cpp
enum class State {
  IDLE,
  READ_BASIC_SENSORS,      // 读取基础传感器
  READ_CHANNEL_1,          // 读取通道1数据
  READ_CHANNEL_2,          // 读取通道2数据
  READ_CHANNEL_3,          // 读取通道3数据
  READ_CHANNEL_4,          // 读取通道4数据
  READ_CHANNEL_5,          // 读取通道5数据
  READ_CHANNEL_6,          // 读取通道6数据
  READ_TOTAL_DATA,         // 读取总和数据
  CHECK_CHIP_RESTART,      // 检测芯片重启
  PROCESS_PERSISTENCE,     // 处理持久化存储
  UPDATE_STATISTICS,       // 更新能量统计
  PUBLISH_SENSORS,         // 发布传感器数据
  HANDLE_ACTIONS          // 处理动作队列
};
```

### 3. 分离的数据处理阶段
- **数据收集**: 状态机负责收集所有原始数据到 `RawSensorData` 结构
- **芯片重启检测**: 基于完整数据集进行检测
- **持久化处理**: 统一处理所有通道的持久化逻辑
- **统计更新**: 统一更新所有统计数据
- **传感器发布**: 统一发布所有传感器数据

## 代码简化效果

### 移除前
- 多个分散的数据读取函数
- 重复的持久化逻辑
- 混合的数据处理和发布逻辑
- 向后兼容的冗余代码

### 移除后
- 单一的数据读取入口
- 统一的数据转换逻辑
- 清晰的数据处理流水线
- 现代化的状态机架构

## 影响评估

### 破坏性变更
- 移除了所有旧的公共接口函数
- 移除了向后兼容的常量定义
- 不再支持旧的调用方式

### 迁移指南
如果有外部代码依赖旧接口，需要：
1. 使用新的统一接口 `read_raw_register_data()` 和 `convert_raw_to_value()`
2. 使用 `CalibRegType::CHGN` 等枚举值替代旧常量
3. 依赖状态机的自动数据处理流程

## 总结

通过移除所有向后兼容的旧函数，BL0906Factory 组件现在具有：
- **更清晰的架构**: 统一的数据处理流水线
- **更好的维护性**: 消除了重复代码
- **更高的性能**: 优化的状态机驱动处理
- **更强的可扩展性**: 现代化的设计模式

这次清理完成了重构的最后一步，确保代码库完全采用新的统一架构，没有历史包袱。 