# BL0906 Factory 新存储系统使用指南

## 概述

BL0906 Factory组件已完成从旧的复杂存储系统到新的简洁统一存储系统的迁移。新系统支持两种存储方式：

- **preference**: 使用ESP32内置NVS存储，无需外部硬件
- **eeprom**: 使用I2C外部EEPROM芯片，支持24C02/04/08/16四种型号

## 主要优势

### 🎯 **简洁性**
- 统一的存储接口设计
- 简化的配置选项
- 清晰的代码结构
- 移除了复杂的适配器层

### 🚀 **灵活性**
- **preference**: 适合原型开发，无需额外硬件
- **EEPROM**: 适合生产环境，独立存储，数据安全

### 🛡️ **可靠性**
- preference: 使用ESP32成熟的NVS机制
- EEPROM: 独立存储，不受Flash限制，1,000,000次写入寿命
- CRC校验确保数据完整性

## 配置方式

### 1. Preference存储（推荐用于开发）

```yaml
bl0906_factory:
  id: sensor_bl0906
  uart_id: uart_bus
  instance_id: 0x906B0001
  calibration:
    enabled: true
    storage_type: preference    # 默认值
```

**特点**：
- ✅ 无需外部硬件
- ✅ 配置简单
- ✅ 适合原型开发
- ✅ 支持更多实例（最多16个）

### 2. I2C EEPROM存储（推荐用于生产）

```yaml
# I2C总线配置
i2c:
  sda: 8
  scl: 9
  scan: true

bl0906_factory:
  id: sensor_bl0906
  uart_id: uart_bus
  i2c_id: bus_a
  address: 0x50              # 可选，默认0x50
  instance_id: 0x906B0001
  calibration:
    enabled: true
    storage_type: eeprom
    eeprom_type: 24c04         # 选择型号
```

**特点**：
- ✅ 独立存储，数据安全
- ✅ 不占用Flash空间
- ✅ 高写入寿命
- ✅ 支持多种容量

## EEPROM型号选择

| 型号 | 容量 | 最大实例数 | 每实例条目数 | 适用场景 |
|------|------|------------|-------------|----------|
| **24C02** | 256字节 | 1个 | 77个 | 单实例小型应用 |
| **24C04** | 512字节 | 3个 | 80个 | 2-3实例基础应用 |
| **24C08** | 1024字节 | 6个 | 82个 | 4-6实例扩展应用 |
| **24C16** | 2048字节 | 12个 | 82个 | 8-12实例大容量应用 |

## 硬件连接（仅EEPROM模式）

```
EEPROM芯片连接：
VCC  -> 3.3V
GND  -> GND
SDA  -> GPIO 8 (可配置)
SCL  -> GPIO 9 (可配置)
A0   -> GND    (地址0x50)
A1   -> GND
A2   -> GND
```

## 迁移说明

### 从旧系统迁移

新系统**不向后兼容**旧的存储格式，但提供了简洁的迁移路径：

1. **备份现有校准数据**（如果需要）
2. **更新配置文件**，选择新的存储方式
3. **重新设置校准值**（通过YAML初始值或手动校准）

### 配置文件更新

**旧配置**：
```yaml
calibration:
  enabled: true
  storage_type: flash
  use_eeprom: true
```

**新配置**：
```yaml
calibration:
  enabled: true
  storage_type: eeprom    # 或 preference
  eeprom_type: 24c04      # 仅EEPROM模式需要
```

## 使用示例

### 开发环境配置
```yaml
# 使用preference存储，简单快速
bl0906_factory:
  calibration:
    storage_type: preference
```

### 生产环境配置
```yaml
# 使用EEPROM存储，数据安全
i2c:
  sda: 8
  scl: 9

bl0906_factory:
  i2c_id: bus_a
  calibration:
    storage_type: eeprom
    eeprom_type: 24c04
```

### 多实例配置
```yaml
# 实例1
bl0906_factory:
  id: sensor1
  instance_id: 0x906B0001
  calibration:
    storage_type: preference

# 实例2  
bl0906_factory:
  id: sensor2
  instance_id: 0x906B0002
  calibration:
    storage_type: preference
```

## 常见问题

### Q: 如何选择存储方式？
**A**: 
- 开发/测试：使用`preference`，简单无需额外硬件
- 生产环境：使用`eeprom`，数据更安全，独立存储

### Q: 如何选择EEPROM型号？
**A**: 根据实例数量选择：
- 1个实例：24C02
- 2-3个实例：24C04
- 4-6个实例：24C08
- 8-12个实例：24C16

### Q: 数据会自动迁移吗？
**A**: 不会。新系统不兼容旧格式，需要重新设置校准值。

### Q: 可以混合使用两种存储方式吗？
**A**: 可以。不同实例可以使用不同的存储方式。

### Q: EEPROM地址冲突怎么办？
**A**: 
- 使用不同的I2C地址（0x50-0x57）
- 通过A0/A1/A2引脚配置地址
- 使用I2C扫描功能检测冲突

## 技术细节

### 数据结构
- **preference**: 使用ESPHome内置NVS API
- **EEPROM**: 自定义紧凑格式，包含头部、索引区、数据区

### 数据完整性
- **preference**: 由ESP32 NVS保证
- **EEPROM**: CRC校验 + 魔数验证

### 性能特点
- **preference**: 读写快速，受Flash寿命限制
- **EEPROM**: 读写稍慢，但寿命更长（1,000,000次）

## 完整配置示例

参考文件：
- `6-ch-monitor-preference.yaml` - preference存储示例
- `6-ch-monitor-eeprom.yaml` - EEPROM存储示例

这些示例展示了完整的配置方法和最佳实践。 