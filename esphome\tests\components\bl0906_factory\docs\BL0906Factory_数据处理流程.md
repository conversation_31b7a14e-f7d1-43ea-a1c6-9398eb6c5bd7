# BL0906Factory 数据处理流程详细说明

## 概述

BL0906Factory是一个基于UART通信的电量监测组件，采用现代化状态机设计，支持6通道电量监测、校准寄存器管理、电量持久化存储和统计功能。

## 主要数据处理流程

### 1. 主循环状态机流程

```mermaid
stateDiagram-v2
    [*] --> IDLE
    IDLE --> READ_BASIC_SENSORS : 开始新的数据读取周期
    READ_BASIC_SENSORS --> READ_CHANNEL_DATA : 基础传感器读取完成
    READ_CHANNEL_DATA --> READ_CHANNEL_DATA : 当前通道 < 6
    READ_CHANNEL_DATA --> READ_TOTAL_DATA : 所有通道读取完成
    READ_TOTAL_DATA --> HANDLE_ACTIONS : 总数据读取完成
    HANDLE_ACTIONS --> IDLE : 动作处理完成
    
    state READ_basic_sensors_detail {
        [*] --> 读取温度传感器
        读取温度传感器 --> 读取频率传感器
        读取频率传感器 --> 读取电压传感器
        读取电压传感器 --> [*]
    }
    
    state read_channel_data_detail {
        [*] --> 读取通道电流
        读取通道电流 --> 读取通道功率
        读取通道功率 --> 读取通道电量
        读取通道电量 --> 通道计数器加1
        通道计数器加1 --> [*]
    }
    
    state read_total_data_detail {
        [*] --> 读取总功率
        读取总功率 --> 读取总电量
        读取总电量 --> [*]
    }
```

### 2. UART通信数据包处理流程

```mermaid
flowchart TD
    A[开始UART通信] --> B[清空接收缓冲区]
    B --> C[发送读命令]
    C --> D[发送寄存器地址]
    D --> E[等待响应数据]
    E --> F{数据是否可用?}
    F -->|否| G[超时检查]
    G -->|超时| H[返回错误]
    G -->|未超时| E
    F -->|是| I[读取4字节数据包]
    I --> J[验证校验和]
    J --> K{校验和正确?}
    K -->|否| L[记录错误日志]
    L --> H
    K -->|是| M[解析数据]
    M --> N[数据类型判断]
    N --> O{16位寄存器?}
    O -->|是| P[16位数据处理]
    O -->|否| Q[24位数据处理]
    P --> R[符号位扩展]
    Q --> R
    R --> S[返回处理后的值]
    S --> T[结束]
    H --> T
```

### 3. 电量持久化存储流程

```mermaid
flowchart TD
    A[电量数据读取] --> B[CF_count增量检测]
    B --> C{是否为CF_count寄存器?}
    C -->|否| D[普通传感器数据处理]
    C -->|是| E[CF_count处理逻辑]
    
    E --> F{是否首次读取?}
    F -->|是| G[初始化基准值]
    F -->|否| H[计算增量]
    
    H --> I{检测芯片重启?}
    I -->|是| J[重新初始化基准值]
    I -->|否| K[正常增量处理]
    
    K --> L[更新持久化CF_count]
    L --> M[更新统计管理器]
    M --> N[检查保存条件]
    
    N --> O{需要保存?}
    O -->|是| P[保存到Flash]
    O -->|否| Q[继续监测]
    
    P --> R[数据校验和计算]
    R --> S[写入Preferences]
    S --> T{保存成功?}
    T -->|是| U[更新保存时间戳]
    T -->|否| V[记录错误日志]
    
    G --> Q
    J --> Q
    D --> Q
    U --> Q
    V --> Q
    Q --> W[结束]
```

### 4. 电量统计管理流程

```mermaid
flowchart TD
    A[统计管理器启动] --> B[加载历史统计数据]
    B --> C[初始化时间组件]
    C --> D[设置周期检查]
    
    D --> E[持续监测循环]
    E --> F[检查时间变化]
    F --> G{日期是否变更?}
    
    G -->|否| H[更新当前CF_count]
    G -->|是| I[处理周期变更]
    
    I --> J{新的一天?}
    J -->|是| K[处理日变更]
    J -->|否| L{新的一周?}
    
    L -->|是| M[处理周变更]
    L -->|否| N{新的一月?}
    
    N -->|是| O[处理月变更]
    N -->|否| P{新的一年?}
    
    P -->|是| Q[处理年变更]
    P -->|否| R[无需处理]
    
    K --> S[创建昨日快照]
    M --> T[创建上周快照]
    O --> U[创建上月快照]
    Q --> V[创建去年快照]
    
    S --> W[更新统计传感器]
    T --> W
    U --> W
    V --> W
    R --> W
    
    H --> X[计算周期电量]
    X --> Y[更新传感器状态]
    Y --> Z[保存统计数据]
    
    W --> Z
    Z --> E
```

### 5. 校准寄存器管理流程

```mermaid
flowchart TD
    A[校准组件初始化] --> B[注册校准Number组件]
    B --> C[应用初始校准值]
    
    C --> D[解除写保护]
    D --> E{写保护解除成功?}
    E -->|否| F[记录错误]
    E -->|是| G[批量写入校准值]
    
    G --> H[遍历校准值列表]
    H --> I[写入单个寄存器]
    I --> J[验证写入结果]
    J --> K{写入成功?}
    K -->|否| L[记录失败]
    K -->|是| M[计数成功]
    
    M --> N{还有更多值?}
    N -->|是| H
    N -->|否| O[完成批量写入]
    
    O --> P[刷新所有校准组件]
    P --> Q[读取校准寄存器]
    Q --> R[更新Number组件状态]
    R --> S[完成初始化]
    
    F --> S
    L --> S
    S --> T[运行时校准管理]
    
    T --> U[接收校准值变更]
    U --> V[写入新的校准值]
    V --> W[实时验证]
    W --> X[更新组件状态]
    X --> T
```

### 6. 数据转换和处理流程

```mermaid
flowchart TD
    A[原始数据接收] --> B[数据类型识别]
    B --> C{数据类型}
    
    C -->|温度| D[温度转换公式]
    C -->|频率| E[频率转换公式]
    C -->|其他| F[标准转换公式]
    
    D --> G["(raw - 64) * 12.5 / 59.0 - 40.0"]
    E --> H["10000000.0 / raw"]
    F --> I["raw / reference"]
    
    G --> J[发布传感器状态]
    H --> J
    I --> J
    
    J --> K[检查电量持久化]
    K --> L{是否为CF_count?}
    L -->|是| M[处理CF_count逻辑]
    L -->|否| N[普通数据处理]
    
    M --> O[增量计算]
    O --> P[持久化更新]
    P --> Q[统计更新]
    
    N --> R[直接发布]
    Q --> R
    R --> S[完成处理]
```

### 7. 错误处理和恢复流程

```mermaid
flowchart TD
    A[检测到错误] --> B{错误类型}
    
    B -->|通信超时| C[清空缓冲区]
    B -->|校验和错误| D[记录错误日志]
    B -->|数据损坏| E[重置数据]
    B -->|写保护失败| F[重试解锁]
    
    C --> G[重新发送命令]
    D --> H[跳过当前数据]
    E --> I[从备份恢复]
    F --> J[多次尝试解锁]
    
    G --> K{重试成功?}
    K -->|是| L[继续正常流程]
    K -->|否| M[标记设备离线]
    
    H --> N[继续下一个读取]
    I --> O[重新初始化]
    J --> P{解锁成功?}
    P -->|是| L
    P -->|否| Q[禁用写入功能]
    
    M --> R[等待下次周期]
    N --> L
    O --> L
    Q --> L
    R --> L
    L --> S[恢复正常运行]
```

## 关键特性

### 1. 现代化设计特点
- **状态机驱动**: 使用枚举类型的状态机，逻辑清晰
- **RAII资源管理**: 自动管理UART资源和内存
- **线程安全**: 使用互斥锁保护共享数据
- **错误恢复**: 完善的错误检测和恢复机制

### 2. 性能优化
- **批量操作**: 减少系统调用次数
- **缓存机制**: 避免重复计算
- **增量更新**: 只处理变化的数据
- **异步处理**: 非阻塞的数据处理

### 3. 数据完整性
- **校验和验证**: 确保通信数据正确性
- **持久化存储**: 防止断电数据丢失
- **备份恢复**: 数据损坏时的恢复机制
- **版本兼容**: 支持数据结构升级

### 4. 扩展性设计
- **模块化架构**: 功能模块独立
- **接口统一**: 标准化的传感器接口
- **配置灵活**: 支持运行时配置变更
- **插件机制**: 支持功能扩展

## 配置示例

```yaml
bl0906_factory:
  # 基础配置
  uart_id: uart_bus
  update_interval: 5s
  
  # 电量持久化配置
  energy_persistence_enabled: true
  
  # 统计功能配置
  energy_statistics_enabled: true
  
  # 校准值配置
  calibration:
    current_1_gain: 16384
    current_1_offset: 0
    # ... 更多校准值

# 传感器配置
sensor:
  - platform: bl0906_factory
    voltage:
      name: "电压"
    frequency:
      name: "频率"
    temperature:
      name: "温度"
    current_1:
      name: "通道1电流"
    power_1:
      name: "通道1功率"
    energy_1:
      name: "通道1电量"
    total_energy_1:
      name: "通道1累计电量"
    # 统计传感器
    today_energy_1:
      name: "通道1今日电量"
    yesterday_energy_1:
      name: "通道1昨日电量"
```

## 总结

BL0906Factory组件采用现代化的C++设计模式，实现了高效、可靠的电量监测功能。通过状态机驱动的数据处理流程、完善的错误处理机制和灵活的配置选项，为用户提供了专业级的电量监测解决方案。 