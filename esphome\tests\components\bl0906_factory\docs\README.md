# BL0906工厂校准组件

该组件提供了对BL0906电能计量芯片的高级配置和校准功能，允许用户在YAML配置文件中设置芯片的各种校准寄存器值。

## 功能特点

- 支持读取BL0906的所有电压、电流、功率和能量计量寄存器
- 支持配置和读取所有校准寄存器
- 可以通过YAML配置文件为每个通道设置自定义校准值
- 在设备启动时自动应用校准值
- 优化的API设计，减少代码重复

## 校准寄存器说明

BL0906芯片主要有以下几组校准寄存器：

1. **电流通道增益校准 (CHGN)**：调整电流通道的增益系数
2. **电流通道偏置校准 (CHOS)**：调整电流通道的偏置
3. **电压通道增益校准 (CHGN_V)**：调整电压通道的增益系数
4. **电压通道偏置校准 (CHOS_V)**：调整电压通道的偏置
5. **有效值增益校准 (RMSGN)**：调整有效值计算的增益
6. **有效值偏置校准 (RMSOS)**：调整有效值计算的偏置
7. **功率增益校准 (WATTGN)**：调整功率计算的增益
8. **功率偏置校准 (WATTOS)**：调整功率计算的偏置

## 配置示例

```yaml
# BL0906芯片配置
bl0906_factory:
  id: bl0906_chip
  uart_id: uart_bus
  update_interval: 5s
  # 校准配置部分
  calibration:
    # 通道1校准参数
    channel_1:
      current_gain: 100       # 电流增益校准值 (CHGN_1)
      current_offset: -50     # 电流偏置校准值 (CHOS_1)
      rms_gain: 120           # 有效值增益校准值 (RMSGN_1)
      rms_offset: -30         # 有效值偏置校准值 (RMSOS_1)
      power_gain: 110         # 功率增益校准值 (WATTGN_1)
      power_offset: -20       # 功率偏置校准值 (WATTOS_1)
    
    # 通道2-6可以类似配置
    
    # 电压通道校准参数
    voltage:
      voltage_gain: 100       # 电压增益校准值 (CHGN_V)
      voltage_offset: -25     # 电压偏置校准值 (CHOS_V)
```

## 配置选项

### 顶层配置

| 配置项 | 类型 | 必需 | 默认值 | 说明 |
| ------ | ---- | ---- | ------ | ---- |
| id | ID | 是 | - | 组件ID |
| uart_id | ID | 是 | - | 引用UART总线 |
| update_interval | 时间 | 否 | 60s | 数据更新间隔 |
| calibration | 对象 | 否 | - | 校准寄存器配置 |

### 校准配置

校准配置下可以配置 `channel_1` 到 `channel_6` 以及 `voltage` 几个部分。

#### 通道校准选项 (channel_1 ~ channel_6)

| 配置项 | 类型 | 必需 | 范围 | 说明 |
| ------ | ---- | ---- | ---- | ---- |
| current_gain | 整数 | 否 | -32768~32767 | 电流增益校准值 (CHGN) |
| current_offset | 整数 | 否 | -32768~32767 | 电流偏置校准值 (CHOS) |
| rms_gain | 整数 | 否 | -32768~32767 | 有效值增益校准值 (RMSGN) |
| rms_offset | 整数 | 否 | -32768~32767 | 有效值偏置校准值 (RMSOS) |
| power_gain | 整数 | 否 | -32768~32767 | 功率增益校准值 (WATTGN) |
| power_offset | 整数 | 否 | -32768~32767 | 功率偏置校准值 (WATTOS) |

#### 电压通道校准选项 (voltage)

| 配置项 | 类型 | 必需 | 范围 | 说明 |
| ------ | ---- | ---- | ---- | ---- |
| voltage_gain | 整数 | 否 | -32768~32767 | 电压增益校准值 (CHGN_V) |
| voltage_offset | 整数 | 否 | -32768~32767 | 电压偏置校准值 (CHOS_V) |

## 工作原理

当设备启动时，BL0906组件会：

1. 初始化UART通信
2. 解除BL0906的写保护
3. 依次写入YAML中配置的校准寄存器值
4. 读取所有校准寄存器的当前值
5. 开始定期读取测量数据

每个校准值都会直接写入对应的芯片寄存器，并在写入后验证以确保数据正确。

## 代码优化

为了简化代码实现，我们对API进行了以下优化：

1. 使用统一的 `set_initial_calib_value` 方法，取代之前多个单独的校准值设置方法
2. 添加了 `CalibRegType` 枚举，用于区分不同类型的校准寄存器
3. 使用统一的寄存器地址映射表，减少代码重复
4. 将校准值应用逻辑封装到单独的 `apply_calibration_values` 方法中

这些优化使代码更加简洁，易于维护，同时保持了向后兼容性。

## 在C++中使用

在C++代码中，你可以通过两种方式使用校准API：

### 1. 使用命名空间级别的常量（推荐）

```cpp
#include "bl0906_factory.h"

using namespace esphome;
using namespace esphome::bl0906_factory;

void example(BL0906Factory* component) {
  // 使用命名空间级别的常量
  component->set_initial_calib_value(CHGN, 1, 100);     // 通道1的电流增益
  component->set_initial_calib_value(CHOS, 1, -50);     // 通道1的电流偏置
  component->set_initial_calib_value(CHGN_V, 0, 100);   // 电压增益
  
  // 应用所有校准值
  component->apply_calibration_values();
}
```

### 2. 使用枚举类型

```cpp
#include "bl0906_factory.h"

using namespace esphome;
using namespace esphome::bl0906_factory;

void example(BL0906Factory* component) {
  // 使用枚举类型
  component->set_initial_calib_value(CalibRegType::CHGN, 1, 100);
  component->set_initial_calib_value(CalibRegType::CHOS, 1, -50);
  
  // 应用所有校准值
  component->apply_calibration_values();
}
```

两种方法完全等效，选择你更喜欢的风格即可。

## 故障排除

如果校准值未正确应用，请检查：

1. UART连接是否正确
2. 校准值是否在有效范围内
3. 查看ESPHome日志，寻找写入过程中的错误信息

## 注意事项

- 校准值应根据实际测量和计算得出，不正确的校准值可能导致测量结果不准确
- 所有校准值为整数，范围为-32768~32767
- 写入校准值时会自动解除芯片写保护 