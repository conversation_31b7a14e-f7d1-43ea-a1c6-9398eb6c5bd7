# BL0906Factory 项目要点总结

## 项目概述

BL0906Factory是一个基于ESPHome框架的电能计量芯片驱动组件，专门用于BL0906六通道电能计量芯片的工厂级校准和数据采集。该项目提供了完整的硬件接口、数据处理、校准管理和电量统计功能。

## 核心架构

### 1. 主要类结构

```
BL0906Factory (主控制器)
├── PollingComponent (ESPHome轮询组件基类)
├── uart::UARTDevice (UART通信设备基类)
├── EnergyStatisticsManager (电量统计管理器)
├── CalibrationStorage (校准数据存储接口)
│   ├── PreferenceCalibrationStorage (Preference存储实现)
│   └── I2CEEPROMCalibrationStorage (I2C EEPROM存储实现)
└── BL0906Number (校准参数数字组件)
```

### 2. 数据流架构

```
硬件层 (BL0906芯片)
    ↓ UART通信
通信层 (UART读写、校验和验证)
    ↓ 原始数据
数据处理层 (数据转换、状态机管理)
    ↓ 处理后数据
业务逻辑层 (持久化、统计、校准)
    ↓ 最终数据
传感器发布层 (ESPHome传感器接口)
```

## 功能模块详解

### 1. 硬件通信模块

**文件**: `bl0906_registers.h`, `bl0906_factory.cpp`

**核心功能**:
- UART协议通信 (读命令: 0x35, 写命令: 0xCA)
- 寄存器读写操作
- 数据包校验和验证
- 写保护控制

**关键方法**:
- `send_read_command_and_receive()` - 统一寄存器读取
- `write_register_value()` - 寄存器写入
- `turn_off_write_protect()` - 写保护控制

### 2. 数据采集模块

**核心特性**:
- 状态机驱动的数据读取流程
- 支持6通道电流、功率、电量测量
- 温度、频率、电压基础参数测量
- 原始数据与转换数据分离

**状态机流程**:
```
IDLE → READ_BASIC_SENSORS → READ_CHANNEL_1~6 → 
READ_TOTAL_DATA → CHECK_CHIP_RESTART → 
PROCESS_PERSISTENCE → UPDATE_STATISTICS → 
PUBLISH_SENSORS → HANDLE_ACTIONS
```

**数据结构**:
```cpp
struct RawSensorData {
    uint32_t temperature_raw, frequency_raw, voltage_raw;
    struct ChannelData {
        uint32_t current_raw;    // 无符号
        int32_t power_raw;       // 有符号
        uint32_t energy_raw;     // 无符号
    } channels[6];
    int32_t power_sum_raw;       // 有符号
    uint32_t energy_sum_raw;     // 无符号
    uint32_t timestamp;
    bool read_complete;
};
```

### 3. 校准管理模块

**文件**: `bl0906_calibration.h`, `calibration_storage_interface.h`

**支持的校准类型**:
- **CHGN** - 电流通道增益校准
- **CHOS** - 电流通道偏置校准  
- **RMSGN** - 有效值增益校准
- **RMSOS** - 有效值偏置校准
- **WATTGN** - 功率增益校准
- **WATTOS** - 功率偏置校准
- **CHGN_V/CHOS_V** - 电压通道校准

**存储方案**:
- **Preference存储** - 基于ESP32 NVS
- **I2C EEPROM存储** - 外部EEPROM芯片
- 支持多实例存储 (通过instance_id区分)

**关键功能**:
- 自动RMSOS计算: `RMSOS[n] = (-current_raw[n]² / 256) × 0.787`
- 校准值批量应用和验证
- 16位/24位寄存器自动识别

### 4. 电量持久化模块

**核心概念**:
- 基于CF_count (脉冲计数) 的电量累计
- 芯片重启检测和数据恢复
- 断电保护和数据持久化

**数据结构**:
```cpp
struct EnergyPersistenceData {
    uint32_t persistent_cf_count[7];    // 软件维护的累计CF_count
    uint32_t last_cf_count[7];          // 上次读取的硬件CF_count
    uint32_t save_count;                // 存储计数器
    uint32_t checksum;                  // 数据校验和
};
```

**重启检测逻辑**:
- 当所有通道CF_count < 5时判定为芯片重启
- 自动更新基准值并恢复累计计数
- 防止重复检测的超时机制

### 5. 电量统计模块

**文件**: `energy_statistics_manager.h`

**统计周期**:
- 昨日、今日、本周、本月、本年电量统计
- 支持各通道独立统计和总和统计

**优化特性**:
- 只在时间周期变更时写入Flash
- 线程安全的数据访问
- 紧凑的时间快照结构

**数据结构**:
```cpp
struct OptimizedEnergyStatistics {
    CompactTimeSnapshot period_times[5];           // 周期时间快照
    uint32_t period_persistent_cf_count[7][5];     // 周期CF_count快照
    uint32_t current_persistent_cf_count[7];       // 当前CF_count
    time_t last_update_timestamp;
    uint32_t checksum;
    std::atomic<bool> updating;                    // 线程安全标志
};
```

### 6. 传感器接口模块

**传感器类型**:
```cpp
enum class SensorType {
    VOLTAGE, FREQUENCY, TEMPERATURE,
    CURRENT, POWER, ENERGY,
    POWER_SUM, ENERGY_SUM,
    TOTAL_ENERGY, TOTAL_ENERGY_SUM
};
```

**统计传感器类型**:
```cpp
enum class StatisticsSensorType {
    YESTERDAY_ENERGY, TODAY_ENERGY, WEEK_ENERGY,
    MONTH_ENERGY, YEAR_ENERGY,
    YESTERDAY_TOTAL_ENERGY, TODAY_TOTAL_ENERGY,
    WEEK_TOTAL_ENERGY, MONTH_TOTAL_ENERGY, YEAR_TOTAL_ENERGY
};
```

## 项目规则与约定

### 1. 编码规范

- **命名空间**: `esphome::bl0906_factory`
- **日志标签**: `FACTORY_TAG = "bl0906_factory"`
- **常量命名**: 全大写下划线分隔 (如 `BL0906_READ_COMMAND`)
- **枚举类**: 使用 `enum class` 避免命名冲突

### 2. 数据类型约定

- **无符号寄存器**: 电压、电流、频率、温度、CF_count
- **有符号寄存器**: 功率寄存器
- **16位寄存器**: 校准寄存器 (CHGN, CHOS, RMSGN, WATTGN, WATTOS系列)
- **24位寄存器**: 测量寄存器

### 3. 线程安全规则

- 使用 `std::mutex` 保护共享数据
- 使用 `std::atomic` 处理简单状态标志
- 所有统计数据访问通过 `_safe` 后缀方法

### 4. 错误处理规则

- 所有UART操作包含超时机制
- 数据校验和验证
- 失败时返回默认值而非异常
- 详细的日志记录 (LOGV/LOGD/LOGI/LOGW/LOGE)

### 5. 性能优化规则

- 批量数据读取减少UART调用
- 按需保存减少Flash写入
- 状态机避免阻塞操作
- 缓冲区预清理避免数据污染

## 配置示例

### 基础配置
```yaml
bl0906_factory:
  id: bl0906_chip
  uart_id: uart_bus
  update_interval: 5s
  instance_id: 0x906B0001
  energy_persistence_enabled: true
  energy_statistics_enabled: true
  storage_type: "preference"
```

### 校准配置
```yaml
bl0906_factory:
  calibration:
    channel_1:
      current_gain: 100
      current_offset: -50
      rms_gain: 120
      rms_offset: -30
      power_gain: 110
      power_offset: -20
    voltage:
      voltage_gain: 100
      voltage_offset: -25
```

### 传感器配置
```yaml
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_chip
    voltage:
      name: "Voltage"
    frequency:
      name: "Frequency"
    temperature:
      name: "Temperature"
    channel_1:
      current:
        name: "Channel 1 Current"
      power:
        name: "Channel 1 Power"
      energy:
        name: "Channel 1 Energy"
      total_energy:
        name: "Channel 1 Total Energy"
```

## 编译条件

### 校准模式
```cpp
#ifdef BL0906_CALIBRATION_MODE
// 校准版本特有功能
#endif
```

### I2C EEPROM支持
```cpp
#ifdef USE_I2C_EEPROM_CALIBRATION
// I2C EEPROM存储功能
#endif
```

## 关键算法

### 1. 数据转换公式
- **频率**: `10000000.0f / raw_value`
- **温度**: `(raw_value - 64) × 12.5f / 59.0f - 40.0f`
- **电压**: `raw_value / Kv`
- **电流**: `raw_value / Ki`
- **功率**: `raw_value / Kp`
- **电量**: `raw_value / Ke`

### 2. 校验和计算
```cpp
uint8_t checksum = (address + data.l + data.m + data.h) ^ 0xFF;
```

### 3. 符号扩展
```cpp
// 24位有符号数扩展到32位
int32_t result = (raw_value << 8) >> 8;
```

## 项目优势

1. **模块化设计** - 清晰的功能分离和接口定义
2. **高可靠性** - 完善的错误处理和数据验证
3. **高性能** - 优化的数据读取和存储策略
4. **易扩展** - 统一的接口设计支持功能扩展
5. **工厂级** - 支持批量校准和生产测试

## 维护要点

1. **定期检查** - Flash写入次数和数据完整性
2. **日志监控** - 关注UART通信错误和数据异常
3. **版本兼容** - 存储格式变更时的数据迁移
4. **性能调优** - 根据实际使用调整更新间隔和保存策略 