# BL0906 Factory 电量统计传感器注册功能实现总结

## 实现概述

本次更新为BL0906 Factory组件增加了完整的电量统计传感器注册功能，支持昨日、今日、本周、本月、本年的电量统计数据。

**核心设计理念**：采用按通道组织的方式，将电量统计传感器集成到每个通道的传感器组中，使配置更加直观和易于管理。

## 主要修改内容

### 1. sensor.py 文件修改

#### 新增电量统计传感器类型枚举
```python
# 电量统计传感器类型枚举映射
STATISTICS_SENSOR_TYPES = {
    "YESTERDAY_ENERGY": 0,        # 昨日电量（各通道）
    "TODAY_ENERGY": 1,           # 今日电量（各通道）
    "WEEK_ENERGY": 2,            # 本周电量（各通道）
    "MONTH_ENERGY": 3,           # 本月电量（各通道）
    "YEAR_ENERGY": 4,            # 本年电量（各通道）
    "YESTERDAY_TOTAL_ENERGY": 5, # 昨日总电量
    "TODAY_TOTAL_ENERGY": 6,     # 今日总电量
    "WEEK_TOTAL_ENERGY": 7,      # 本周总电量
    "MONTH_TOTAL_ENERGY": 8,     # 本月总电量
    "YEAR_TOTAL_ENERGY": 9       # 本年总电量
}
```

#### 按通道组织的传感器配置
```python
# 通道传感器模板（包含电量统计）
CHANNEL_SENSOR_TEMPLATES = {
    # 基础传感器
    "current": {...},
    "power": {...},
    "energy": {...},
    "total_energy": {...},
    
    # 电量统计传感器（集成到通道中）
    "yesterday_energy": {...},
    "today_energy": {...},
    "week_energy": {...},
    "month_energy": {...},
    "year_energy": {...},
}
```

#### 统一的传感器注册逻辑
```python
# 统一处理普通传感器和统计传感器
for sensor_key, sensor_config in SENSOR_CONFIGS.items():
    if sensor_key in config:
        sens = await sensor.new_sensor(config[sensor_key])
        sensor_type = sensor_config["type"]
        channel = sensor_config.get("channel", 0)
        
        # 判断传感器类型并使用相应的注册接口
        if sensor_type in STATISTICS_SENSOR_TYPES:
            # 统计传感器
            statistics_sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::StatisticsSensorType::{sensor_type}")
            cg.add(var.set_statistics_sensor(statistics_sensor_type_enum, sens, channel))
        else:
            # 普通传感器
            sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::BL0906Factory::SensorType::{sensor_type}")
            cg.add(var.set_sensor(sensor_type_enum, sens, channel))
```

### 2. __init__.py 文件修改

#### 新增StatisticsSensorType枚举定义
```python
StatisticsSensorType = bl0906_factory_ns.enum("StatisticsSensorType", is_class=True)
```

### 3. bl0906_factory.h 文件修改

#### 扩展SensorType枚举
```cpp
enum class SensorType {
    VOLTAGE,
    FREQUENCY,
    TEMPERATURE,
    CURRENT,
    POWER,
    ENERGY,
    POWER_SUM,
    ENERGY_SUM,
    TOTAL_ENERGY,      // 各通道累计电量
    TOTAL_ENERGY_SUM   // 总累计电量
};
```

#### 新增传感器成员变量
```cpp
// 基础传感器
sensor::Sensor *total_energy_sum_sensor_{nullptr};

// 通道传感器数组
sensor::Sensor *total_energy_sensors_[CHANNEL_COUNT]{nullptr};
```

## 按通道组织的传感器架构

### 传感器分组结构

```
通道X传感器组 (X = 1-6):
├── 基础测量传感器
│   ├── current_X      (电流)
│   ├── power_X        (功率)
│   └── energy_X       (实时电量)
├── 累计数据传感器
│   └── total_energy_X (累计电量)
└── 统计分析传感器
    ├── yesterday_energy_X (昨日电量)
    ├── today_energy_X     (今日电量)
    ├── week_energy_X      (本周电量)
    ├── month_energy_X     (本月电量)
    └── year_energy_X      (本年电量)

总电量统计传感器:
├── yesterday_total_energy (昨日总电量)
├── today_total_energy     (今日总电量)
├── week_total_energy      (本周总电量)
├── month_total_energy     (本月总电量)
└── year_total_energy      (本年总电量)
```

### 支持的传感器列表

#### 按通道组织的传感器（每通道9个，共54个）
对于通道1-6，每个通道支持：
1. `current_X` - 电流
2. `power_X` - 功率
3. `energy_X` - 实时电量
4. `total_energy_X` - 累计电量
5. `yesterday_energy_X` - 昨日电量
6. `today_energy_X` - 今日电量
7. `week_energy_X` - 本周电量
8. `month_energy_X` - 本月电量
9. `year_energy_X` - 本年电量

#### 总电量统计传感器（5个）
1. `yesterday_total_energy` - 昨日总电量
2. `today_total_energy` - 今日总电量
3. `week_total_energy` - 本周总电量
4. `month_total_energy` - 本月总电量
5. `year_total_energy` - 本年总电量

## 配置示例

### 按通道组织的配置
```yaml
sensor:
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_device
    
    # 通道1完整传感器组
    current_1:
      name: "通道1电流"
    power_1:
      name: "通道1功率"
    energy_1:
      name: "通道1实时电量"
    total_energy_1:
      name: "通道1累计电量"
    yesterday_energy_1:
      name: "通道1昨日电量"
    today_energy_1:
      name: "通道1今日电量"
    week_energy_1:
      name: "通道1本周电量"
    month_energy_1:
      name: "通道1本月电量"
    year_energy_1:
      name: "通道1本年电量"
    
    # 通道2传感器组（可选择性配置）
    current_2:
      name: "通道2电流"
    today_energy_2:
      name: "通道2今日电量"
    
    # 总电量统计
    today_total_energy:
      name: "今日总电量"
    month_total_energy:
      name: "本月总电量"
```

## 技术特点

### 1. 按通道组织设计
- **直观性**：每个通道的所有传感器集中配置，便于理解和管理
- **完整性**：每个通道包含从基础测量到统计分析的完整传感器组
- **层次化**：清晰的传感器层次结构，从基础测量到高级统计

### 2. 灵活配置
- **选择性配置**：用户可以只配置需要的传感器，不必全部配置
- **通道独立**：每个通道可以独立配置不同的传感器组合
- **渐进式配置**：可以从基础传感器开始，逐步添加统计传感器

### 3. 统一接口
- **双重注册机制**：自动识别传感器类型并使用相应的注册接口
- **类型安全**：使用枚举确保编译时类型检查
- **向后兼容**：不影响现有的传感器配置

### 4. 数据驱动
- **模板化配置**：使用模板自动生成所有通道的传感器配置
- **减少重复**：通过动态生成避免重复代码
- **易于维护**：集中管理传感器配置模板

## 测试文件

创建了以下测试文件：
1. `test_energy_statistics_sensors.yaml` - 完整的按通道组织的功能测试
2. `test_statistics_simple.yaml` - 简化的按通道配置测试
3. `ENERGY_STATISTICS_SENSORS_USAGE.md` - 详细的使用指南

## 配置优势对比

### 旧方式（分散配置）
```yaml
# 基础传感器
current_1: {...}
power_1: {...}

# 统计传感器（分散在其他地方）
yesterday_energy_1: {...}
today_energy_1: {...}
```

### 新方式（按通道组织）
```yaml
# 通道1完整传感器组
current_1: {...}
power_1: {...}
energy_1: {...}
total_energy_1: {...}
yesterday_energy_1: {...}
today_energy_1: {...}
week_energy_1: {...}
month_energy_1: {...}
year_energy_1: {...}
```

## 兼容性

- **向后兼容**：不影响现有的传感器功能
- **可选功能**：电量统计传感器为可选配置
- **渐进式升级**：可以逐步添加统计传感器到现有配置

## 总结

本次实现成功为BL0906 Factory组件添加了按通道组织的电量统计传感器注册功能，支持59个传感器（54个通道传感器 + 5个总电量统计传感器）。新的设计采用直观的按通道组织方式，提供了灵活的配置选项和清晰的传感器层次结构，大大提升了用户体验和配置的可维护性。 